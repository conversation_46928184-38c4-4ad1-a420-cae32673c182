<template>
  <div ref="fullscreenContainer" class="">
    <el-form :inline="true" :model="state.queryForm" @submit.prevent>
      <vab-query-form>
        <vab-query-form-left-panel :span="18">
          <el-form-item>
            <el-checkbox-group
              v-model="state.approveStatusSel"
              size="default"
              @change="queryDataByTimer"
              class="checkbox-group"
            >
              <el-checkbox-button
                v-for="op in state.approveStatus"
                :key="op"
                :label="op.id"
              >
                {{ op.name }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model.trim="state.queryForm.filter.keywords"
              clearable
              placeholder="请输入编号/料号/产品名称"
              @input="queryDataByTimer"
            />
          </el-form-item>

          <el-form-item>
            <!-- <el-select
              v-model="state.workStatusSel"
              @change="queryData"
              value-key=""
              placeholder="订单状态"
              filterable
              multiple
              clearable
              collapse-tags
              style="width: 150px"
            >
              <el-option
                v-for="item in state.workStatusList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              />
            </el-select> -->
            <el-cascader
              v-model="state.workStatusSel"
              :options="state.workStatusList"
              :props="state.statusListProps"
              @change="queryDataByTimer"
              clearable
              placeholder="订单状态"
              collapse-tags
              style="width: 150px"
              @show="awaitmin()"
            />
          </el-form-item>
          <el-form-item label="" prop="deliveryDate">
            <el-date-picker
              style="width: 240px"
              v-model="deliveryDate"
              start-placeholder="截止开始日期"
              end-placeholder="截止结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="~"
              type="daterange"
              unlink-panels
              :shortcuts="state.shortcuts"
              @change="changeDeliveryDate"
            />
          </el-form-item>
          <el-form-item v-if="state.activeName != 'accessories'">
            <el-popover
              :visible="state.visiblePopover"
              placement="bottom"
              :width="350"
              title="更多筛选"
              :show-arrow="false"
              popper-class="el-form-in-popover"
              v-if="!isProcessingFactory()"
            >
              <template #reference>
                <el-button
                  @click="changeQueryFormTopShow"
                  ref="buttonRef"
                  trigger="click"
                >
                  <vab-icon
                    :icon="
                      state.queryFormTopShow ? 'filter-2-line' : 'filter-2-fill'
                    "
                    :style="{ 'font-size': '16px' }"
                  />
                  更多筛选
                </el-button>
              </template>
              <el-form label-width="100px" v-if="!isProcessingFactory()">
                <el-form-item label="加工厂">
                  <el-select
                    v-model="state.queryForm.filter.factory_id"
                    style="width: 250px"
                    @change="queryData"
                    clearable
                    placeholder="加工厂"
                  >
                    <el-option
                      v-for="(sitem, skey) in state.factoryList"
                      :key="skey"
                      :label="sitem.name"
                      :value="sitem.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="信息状态">
                  <el-select
                    v-model="state.queryForm.filter.is_complete"
                    style="width: 250px"
                    @change="queryData"
                    clearable
                    placeholder="请选择信息状态"
                  >
                    <el-option
                      v-for="(sitem, skey) in state.completeStatus"
                      :key="skey"
                      :label="sitem.label"
                      :value="sitem.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="预计上线">
                  <el-date-picker
                    v-model="predictOnlineTime"
                    clearable
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    range-separator="-"
                    type="daterange"
                    @change="changePredictOnlineTime"
                    style="max-width: 250px"
                  />
                </el-form-item>
                <el-form-item v-if="state.activeName == 'opensource'">
                  <div>
                    <span style="padding-right: 6px; font-size: 14px">
                      上线信息
                    </span>
                    <el-tooltip
                      :content="
                        !state.tableShowOnlineDesc
                          ? '点击后只显示上线信息'
                          : '点击后不显示上线信息'
                      "
                      :enterable="false"
                      placement="top"
                    >
                      <el-switch
                        v-model="onlineDescSwitch"
                        :loading="state.switchLoading"
                        :disabled="state.switchLoading"
                        :before-change="showOnlineDescOnTable"
                      />
                    </el-tooltip>
                  </div>
                </el-form-item>
                <el-form-item label="生产总结">
                  <el-select
                    v-model="state.queryForm.filter.summary_finish_status"
                    style="width: 250px"
                    @change="queryData"
                    clearable
                    placeholder="请选择状态"
                  >
                    <el-option
                      v-for="(sitem, skey) in state.summaryStatus"
                      :key="skey"
                      :label="sitem.label"
                      :value="sitem.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="总结备注">
                  <el-input
                    style="width: 250px"
                    v-model.trim="state.queryForm.filter.summary_keywords"
                    clearable
                    placeholder="请输入生产问题/测试问题"
                    @input="queryDataByTimer"
                  />
                </el-form-item>
                <el-form-item label="产品平台">
                  <el-cascader
                    v-model="state.queryForm.filter.product_platform"
                    :options="state.productPlatformList"
                    :props="state.platformListProps"
                    @change="queryData"
                    clearable
                    filterable
                    placeholder="选择产品平台"
                    style="width: 250px"
                  />
                </el-form-item>
                <el-form-item label="原理图负责人">
                  <el-select
                    v-model="state.queryForm.filter.hardware_user_id"
                    filterable
                    clearable
                    placeholder="选择原理图负责人"
                    :filter-method="filterHardwareHandlerMethod"
                    @change="queryData"
                    style="width: 250px"
                  >
                    <el-option
                      v-for="(item, index) in state.hardwareUserList"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="软件负责人">
                  <el-select
                    v-model="state.queryForm.filter.software_user_id"
                    filterable
                    clearable
                    placeholder="选择软件负责人"
                    :filter-method="filterSoftHandlerMethod"
                    @change="queryData"
                    style="width: 250px"
                  >
                    <el-option
                      v-for="(item, index) in state.softwareUserList"
                      :key="index"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="有无关联IQC "
                  label-width="96"
                  style="margin-left: -3px"
                >
                  <el-radio-group
                    v-model="hasIQC"
                    @change="queryData"
                    style="margin-left: 3px"
                  >
                    <el-radio :label="0">全部</el-radio>
                    <el-radio :label="1">有</el-radio>
                    <el-radio :label="2">无</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="有无验证物料" label-width="96">
                  <el-radio-group
                    v-model="hasMaterialVerification"
                    @change="queryData"
                  >
                    <el-radio :label="0">全部</el-radio>
                    <el-radio :label="1">有</el-radio>
                    <el-radio :label="2">无</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-popover>
          </el-form-item>
          <el-form-item>
            <el-button
              type="default"
              :icon="RefreshIcon"
              @click="handleRefreshOptions"
            >
              重置
            </el-button>
          </el-form-item>
        </vab-query-form-left-panel>
        <vab-query-form-right-panel :span="6">
          <el-form-item>
            <el-button
              @click="sendMessage()"
              v-if="state.selectRows && state.selectRows.length > 0"
            >
              发送通知
            </el-button>
          </el-form-item>
          <el-form-item
            v-if="
              roleAuth(state.auth_role.adminRole) ||
              roleAuth(state.auth_role.productionRole)
            "
          >
            <el-button type="primary" @click="showEdit">
              <CommonIcon :type="IconType.Plus" />
              新增订单
            </el-button>
          </el-form-item>
          <!-- <el-form-item
            v-if="
              roleAuth(state.auth_role.adminRole) ||
              roleAuth(state.auth_role.productionRole)
            "
          >
            <el-button
              v-if="
                roleAuth(state.auth_role.productionRole) ||
                roleAuth(state.auth_role.adminRole)
              "
              type="primary"
              @click="openDialog('editForSysnOrder')"
              :loading="state.syncOrderLoading"
            >
              <vab-icon
                icon="repeat-2-line"
                v-if="!state.syncOrderLoading"
                class="pane-icon"
              />
              同步订单
            </el-button>
          </el-form-item>
          <el-form-item
            v-if="
              roleAuth(state.auth_role.adminRole) ||
              roleAuth(state.auth_role.productionRole)
            "
          >
            <el-button type="primary" @click="openDialog('editForPlatform')">
              <CommonIcon :type="IconType.Plus" />
              新增平台
            </el-button>
          </el-form-item>
          <el-form-item>
            <el-button
              type="default"
              :icon="RefreshIcon"
              @click="handleRefreshOptions"
            >
              重置
            </el-button>
          </el-form-item> -->
          <el-popover
            placement="bottom"
            trigger="click"
            content="this is content, this is content, this is content"
            v-if="!isProcessingFactory()"
          >
            <template #reference>
              <el-button
                type="default"
                ref="buttonRef"
                style="margin-left: 0px !important"
              >
                <vab-icon
                  icon="arrow-down-s-line"
                  style="width: 24px; margin-left: -10px"
                  is-custom-svg
                />
                更多操作
              </el-button>
            </template>

            <div>
              <div
                class="hoverable-item"
                @click="openDialog('editForPlatform')"
              >
                <CommonIcon :type="IconType.Plus" />
                新增平台
              </div>
              <div
                class="hoverable-item"
                @click="openDialog('editForSysnOrder')"
                :loading="state.syncOrderLoading"
              >
                <el-icon
                  style="width: 24px; height: 24px"
                  v-if="state.syncOrderLoading"
                >
                  <Loading />
                </el-icon>
                <CommonIcon
                  v-if="!state.syncOrderLoading"
                  :type="IconType.Refresh"
                />
                同步订单
              </div>
            </div>
          </el-popover>
          <!-- <el-popover
            placement="bottom"
            trigger="click"
            content="this is content, this is content, this is content"
          >
            <template #reference>
              <el-button type="primary" ref="buttonRef">
                <vab-icon
                  icon="arrow-down-s-line"
                  style="width: 24px; margin-left: -10px"
                />
                更多操作
              </el-button>
            </template>
            <el-button
              size="large"
              text
              type="primary"
              @click="openDialog('editForPlatform')"
            >
              <CommonIcon :type="IconType.Plus" />
              新增平台
            </el-button>

            <el-button
              size="large"
              class="more-operate-button"
              style="margin-left: 5px !important"
              v-if="
                roleAuth(state.auth_role.productionRole) ||
                roleAuth(state.auth_role.adminRole)
              "
              text
              type="primary"
              @click="openDialog('editForSysnOrder')"
              :loading="state.syncOrderLoading"
            >
              <vab-icon
                icon="repeat-2-line"
                v-if="!state.syncOrderLoading"
                class="pane-icon"
              />
              同步订单
            </el-button>
          </el-popover> -->
        </vab-query-form-right-panel>
      </vab-query-form>
    </el-form>

    <el-table
      v-if="state.showElTable"
      v-loading="state.listLoading"
      border
      :data="state.list"
      default-expand-all
      row-key="id"
      :default-sort="state.listSort"
      @sort-change="handleSort"
      size="default"
      :height="state.tableHeight"
      @selection-change="handleSelectionChange"
      @row-click="handleEdit"
    >
      <template #empty>
        <CommonEmpty />
      </template>
      <el-table-column
        align="center"
        show-overflow-tooltip
        type="selection"
        fixed
        width="50"
      />
      <template v-for="(column, index) in state.columns" :key="column.prop">
        <el-table-column
          :id="index"
          :type="column.type"
          v-if="!column.slot"
          :label="column.label"
          :align="column.align"
          :property="column.prop"
          :fixed="column.fixed"
          :width="column.width"
          :sortable="column.sortable"
          :min-width="column.minWidth"
          :show-overflow-tooltip="column.showOverflowTooltip"
          :class-name="
            column.prop === 'code' ? 'order-number-cell' : 'other-cell'
          "
        >
          <template v-if="column.template" #default="scope">
            <div
              v-if="column.prop === 'code'"
              @click.stop="handleEdit(scope.row)"
              style="
                display: inline-block;
                width: 100%;
                white-space: nowrap;
                cursor: pointer;
              "
            >
              {{ scope.row.code }}
              <vab-icon
                v-if="scope.row.is_complete == 0"
                icon="edit-2-fill"
                class="edit-key-icon"
              />
            </div>
            <div v-else-if="column.prop === 'product_name'">
              <el-tooltip
                class="item"
                effect="dark"
                :content="scope.row.product_name"
                placement="top"
              >
                <span class="product-name">
                  <span
                    :style="
                      scope.row.production_count == 1
                        ? 'width: calc(100% - 32px);'
                        : ''
                    "
                    class="name-text"
                  >
                    {{ scope.row.product_name }}
                  </span>
                  <el-tag
                    class="product-name-tag-danger"
                    v-if="scope.row.production_count == 1"
                    style="margin-left: 8px"
                    type="danger"
                    effect="plain"
                  >
                    首次
                  </el-tag>
                </span>
              </el-tooltip>
            </div>
            <span v-else-if="column.prop === 'delivery_date'">
              {{ scope.row.delivery_date?.slice(0, 16) }}
            </span>
            <el-tag
              v-else-if="column.prop === 'is_complete'"
              class="ml-2"
              :type="scope.row.is_complete ? 'primary' : 'info'"
            >
              {{ scope.row.is_complete ? '已完善' : '未完善' }}
            </el-tag>
            <span v-else-if="column.prop === 'hardware_user_name'">
              {{ scope.row.hardware_user_name }}
              <span
                v-if="
                  scope.row.schematic_upload_status == 1 &&
                  scope.row.hardware_user_name &&
                  scope.row.schematic_audit_status != 1
                "
              >
                <vab-icon
                  icon="user_todo"
                  is-custom-svg
                  class="common-icon"
                  style="margin: 0px 0px"
                />
              </span>
              <span
                v-if="
                  scope.row.schematic_upload_status != 1 &&
                  scope.row.hardware_user_name &&
                  scope.row.schematic_audit_status == 2
                "
              >
                <vab-icon
                  icon="no_pass"
                  is-custom-svg
                  class="common-icon"
                  style="margin: 0px 0px"
                />
              </span>
            </span>
            <span v-else-if="column.prop === 'layout_user_name'">
              {{ scope.row.layout_user_name }}
              <span
                v-if="
                  scope.row.layout_upload_status == 1 &&
                  scope.row.layout_user_name &&
                  scope.row.layout_audit_status != 1
                "
              >
                <vab-icon
                  icon="user_todo"
                  is-custom-svg
                  class="common-icon"
                  style="margin: 0px 0px"
                />
              </span>
              <span
                v-if="
                  scope.row.layout_upload_status != 1 &&
                  scope.row.layout_user_name &&
                  scope.row.layout_audit_status == 2
                "
              >
                <vab-icon
                  icon="no_pass"
                  is-custom-svg
                  class="common-icon"
                  style="margin: 0px 0px"
                />
              </span>
            </span>
            <span v-else-if="column.prop === 'software_user_name'">
              {{ scope.row.software_user_name }}
              <span
                v-if="
                  scope.row.software_upload_status == 1 &&
                  scope.row.software_user_name &&
                  scope.row.software_audit_status != 1
                "
              >
                <vab-icon
                  icon="user_todo"
                  is-custom-svg
                  class="common-icon"
                  style="margin: 0px 0px"
                />
              </span>
              <span
                v-if="
                  scope.row.software_upload_status != 1 &&
                  scope.row.software_user_name &&
                  scope.row.software_audit_status == 2
                "
              >
                <vab-icon
                  icon="no_pass"
                  is-custom-svg
                  class="common-icon"
                  style="margin: 0px 0px"
                />
              </span>
            </span>
            <div v-else-if="column.prop === 'approve_status'">
              <el-tag
                v-if="
                  scope.row.approve_status != 1 &&
                  scope.row.has_repair_file == 0
                "
                class="ml-2"
                :type="'success'"
                :style="{ border: 'none' }"
                effect="plain"
              >
                <div
                  style="
                    display: flex;
                    flex-wrap: nowrap;
                    align-items: center;
                    margin-top: 4px;
                  "
                  :style="
                    scope.row.approve_status == 2
                      ? { color: '#ff7801' }
                      : { color: '#909399' }
                  "
                >
                  <div
                    style="margin-top: 7px; margin-left: -4px"
                    class="common-icon-normal"
                  >
                    <vab-icon icon="subtract" is-custom-svg />
                  </div>
                  <span style="font-size: 14px">
                    {{ approveStatusMap(scope.row.approve_status) }}
                  </span>
                </div>
              </el-tag>
              <DropdownList
                v-else
                v-model:value="scope.row.approve_status"
                :only-show-tag="true"
                :keywords="'approve_status'"
                :big-tag-label="false"
              />
            </div>

            <el-popover
              v-else-if="column.prop === 'customer_remark'"
              placement="right"
              :width="400"
              trigger="hover"
              :content="scope.row.customer_remark"
              raw-content
              :disabled="!scope.row.customer_remark"
            >
              <template #reference>
                <span
                  class="over-flow"
                  :class="{ 'empty-remark': !scope.row.customer_remark }"
                  @click.stop="
                    editCustomerRemark(
                      scope.row,
                      roleAuth(state.auth_role.adminRole) ||
                        ((roleAuth(state.auth_role.productionRole) ||
                          user_id == scope.row.order_user_id) &&
                          workStatusNameIndexSuitable(scope.row, 1))
                    )
                  "
                >
                  {{ scope.row.customer_remark || '无' }}
                </span>
              </template>
              <template #default>
                <div class="notes-box">
                  <span
                    :class="{ 'empty-remark': !scope.row.customer_remark }"
                    @click.stop="
                      editCustomerRemark(
                        scope.row,
                        roleAuth(state.auth_role.adminRole) ||
                          ((roleAuth(state.auth_role.productionRole) ||
                            user_id == scope.row.order_user_id) &&
                            workStatusNameIndexSuitable(scope.row, 1))
                      )
                    "
                  >
                    {{ scope.row.customer_remark || '无' }}
                  </span>
                </div>
              </template>
            </el-popover>
            <el-tag
              v-else-if="column.prop === 'work_status_name'"
              class="ml-2 work-status-tag"
              :type="getStatusType(scope.row)"
              :style="
                getStatusType(scope.row) == 'info'
                  ? { color: '#999999 !important' }
                  : {}
              "
            >
              {{ scope.row.work_status_name }}
              <span
                v-if="
                  scope.row.work_status_append_text &&
                  scope.row.work_status_name == '资料录入'
                "
                style="margin: 2px 0; color: #999999"
              >
                •
              </span>
              <span
                v-if="
                  scope.row.work_status_append_text &&
                  scope.row.work_status_name == '生产中'
                "
                style="margin: 2px 0; color: #ffbb7f"
              >
                •
              </span>
              <span v-if="scope.row.work_status_append_text">
                <el-tooltip
                  :content="scope.row.work_status_tips"
                  placement="top"
                  :disabled="!scope.row.work_status_tips"
                >
                  {{ scope.row.work_status_append_text }}
                </el-tooltip>
              </span>
            </el-tag>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        label="操作"
        align="center"
        width="80"
        fixed="right"
        v-if="!isProcessingFactory()"
      >
        <template #default="{ row }">
          <el-tooltip
            :content="'不在【资料录入】阶段，或当前角色不可编辑'"
            :disabled="
              roleAuth(state.auth_role.adminRole) ||
              ((roleAuth(state.auth_role.productionRole) ||
                user_id == row.order_user_id) &&
                workStatusNameIndexSuitable(row, 1))
            "
            placement="top"
          >
            <div>
              <el-button
                :disabled="
                  !(
                    roleAuth(state.auth_role.adminRole) ||
                    ((roleAuth(state.auth_role.productionRole) ||
                      user_id == row.order_user_id) &&
                      workStatusNameIndexSuitable(row, 1))
                  )
                "
                text
                type="primary"
                @click.stop="showEdit(row)"
              >
                编辑
              </el-button>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      background
      :current-page="state.queryForm.pageNo"
      :layout="state.layout"
      :page-size="state.queryForm.pageSize"
      :total="state.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <Edit
      ref="editRef"
      @fetch-data="fetchData"
      :only-edit-customer-remark="state.editCustomerRemark"
    />
    <el-drawer
      v-model="state.drawerDialogVisible"
      :before-close="state.closeDialog"
      :modal="false"
      :append-to-body="true"
      modal-class="modal-product-info"
      direction="rtl"
      size="70vw"
      :with-header="false"
      class="production-drawer"
      ref="productDrawerRef"
    >
      <Details
        ref="detailRef"
        @close="handleCloseDrawer"
        @fetch-data="fetchData()"
        :modal="'drawer'"
        :production-order-id="state.selectRowId"
        :drawer-dialog-visible="state.drawerDialogVisible"
      />
    </el-drawer>
    <MessageSend ref="messageSendRef" :selected-rows="state.selectRows" />
    <firefly-dialog
      v-model="state.showDialog"
      :title="state.dialogTitle"
      :width="state.dialogWidth"
      @close="closeDialog()"
      @confirm="saveDialog(state.formEditFor)"
      show-default-button
      :footer-class="'foot-btn-bar-small-padding'"
    >
      <div v-if="state.formEditFor == 'editForPlatform'">
        <el-form
          ref="platformRef"
          label-width="80px"
          :model="state.platform"
          :rules="state.platformRules"
          label-position="top"
        >
          <el-form-item label="上级属性" prop="pid">
            <el-cascader
              style="width: 440px"
              v-model="state.platform.pid"
              :options="state.platformTreeList"
              :props="state.cascaderProps"
              :show-all-levels="false"
              clearable
              placeholder="可选"
            />
          </el-form-item>
          <el-form-item label="属性名称" prop="name">
            <el-input style="width: 440px" v-model="state.platform.name" />
          </el-form-item>
        </el-form>
      </div>
      <div v-else>
        <el-form
          ref="syncOrderFormRef"
          label-width="80px"
          :model="state.syncForm"
          :rules="state.syncFormRules"
          label-position="top"
        >
          <el-form-item label="" prop="checkDate">
            <el-date-picker
              style="width: 200px"
              v-model="syncCheckDate"
              start-placeholder="截止开始日期"
              end-placeholder="截止结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="~"
              type="daterange"
              unlink-panels
              :shortcuts="state.shortcuts"
            />
          </el-form-item>
        </el-form>
      </div>
    </firefly-dialog>
  </div>
</template>

<script setup>
  import {
    Plus as PlusIcon,
    Refresh as RefreshIcon,
  } from '@element-plus/icons-vue'
  import {
    getList,
    // getWorkStatus,
    queryConf,
    getFlowStatus,
    syncOrder,
  } from '@/api/productionOrder'
  import CommonEmpty from '~/src/components/CommonEmpty.vue'
  import Edit from './Edit.vue'
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import Details from './Details.vue'
  import { IconType } from '~/src/icon'
  import { roleAuth } from '@/utils/auth'
  import DropdownList from './DropdownList.vue'
  import MessageSend from './MessageSend.vue'
  import { treeCategory, editCategory } from '@/api/category'
  import { useUserStore } from '@/store/modules/user'
  import moment from 'moment'
  import { getUserThirdList } from '@/api/user'
  import { cloneDeep } from 'lodash'
  import { chineseFirstText } from '@/utils/pinyinUtils'
  import { style } from '@logicflow/extension/es/bpmn-elements/presets/icons'
  const route = useRoute()
  const userStore = useUserStore()
  let { redmine, user_settings, user_id } = userStore

  const $baseMessage = inject('$baseMessage')

  /**
   * 检查是否为加工厂角色
   * @returns {boolean}
   */
  const isProcessingFactory = () => {
    return roleAuth(['ProcessingFactory'])
  }

  const messageSendRef = ref(null)
  const syncOrderFormRef = ref(null)
  const buttonRef = ref()

  const hasMaterialVerification = ref(0)
  const hasIQC = ref(0)
  // const radio = ref(3)

  // 基础列配置
  const allColumns = [
    {
      label: '序号',
      type: 'index',
      width: 50,
      fixed: 'left',
      align: 'center',
    },
    {
      prop: 'code',
      label: '订单编号',
      align: 'left',
      fixed: user_id == 177 ? false : 'left',
      width: 168,
      sortable: true,
      template: true,
    },
    {
      prop: 'product_name',
      label: '产品名称',
      align: 'left',
      minWidth: 200,
      sortable: true,
      template: true,
      showOverflowTooltip: false,
    },
    {
      prop: 'factory_short_name',
      label: '加工厂',
      align: 'center',
      fixed: user_id != 177 ? false : 'left',
      minWidth: 90,
      sortable: true,
      showOverflowTooltip: true,
    },
    { prop: 'product_code', label: '料号', align: 'center', width: 162 },

    {
      prop: 'delivery_date',
      label: '订单日期',
      align: 'center',
      width: 120,
      sortable: true,
      template: true,
    },
    {
      prop: 'woqty',
      label: '加工数',
      align: 'center',
      width: 84,
      sortable: true,
    },
    {
      prop: 'predict_online_time',
      label: '预计上线时间',
      align: 'center',
      width: 130,
      sortable: 'custom',
    },
    {
      prop: 'order_days',
      label: '已进行天数',
      align: 'center',
      width: 88,
    },
    {
      prop: 'hardware_user_name',
      label: '原理图负责人',
      align: 'center',
      width: 132,
      sortable: 'custom',
      template: true,
      showOverflowTooltip: true,
      hideForProcessingFactory: true,
    },
    {
      prop: 'layout_user_name',
      label: 'layout负责人',
      align: 'center',
      width: 124,
      sortable: 'custom',
      template: true,
      showOverflowTooltip: true,
      hideForProcessingFactory: true,
    },
    {
      prop: 'software_user_name',
      label: '软件负责人',
      align: 'center',
      width: 112,
      sortable: 'custom',
      template: true,
      showOverflowTooltip: true,
      hideForProcessingFactory: true,
    },
    // {
    //   prop: 'is_complete',
    //   label: '信息状态',
    //   align: 'center',
    //   width: 120,
    //   template: true,
    // },
    {
      prop: 'work_status_name',
      label: '状态',
      align: 'left',
      width: 142,
      sortable: 'custom',
      template: true,
    },
    {
      prop: 'stock_order_code',
      label: '备货单号',
      align: 'left',
      minWidth: 140,
      template: false,
      showOverflowTooltip: true,
      hideForProcessingFactory: true,
    },
    {
      prop: 'stock_user',
      label: '业务员',
      align: 'left',
      width: 72,
      template: false,
      showOverflowTooltip: true,
      hideForProcessingFactory: true,
    },
    {
      prop: 'stock_delivery_date',
      label: '交货日期',
      align: 'left',
      width: 102,
      template: false,
      hideForProcessingFactory: true,
    },
    {
      prop: 'approve_status',
      label: '核准',
      align: 'left',
      width: 102,
      template: true,
      hideForProcessingFactory: true,
    },
    {
      prop: 'pass_rate',
      label: '直通率',
      align: 'left',
      width: 102,
      template: false,
      hideForProcessingFactory: false,
    },
    {
      prop: 'customer_remark',
      label: '客户备注',
      align: 'center',
      minWidth: 300,
      template: true,
      hideForProcessingFactory: true,
    },
  ]

  // 根据角色过滤列
  const baseColumns = computed(() => {
    if (isProcessingFactory()) {
      return allColumns.filter((col) => !col.hideForProcessingFactory)
    }
    return allColumns
  })

  const handleEdit = (row) => {
    state.selectRowId = row.id
    state.drawerDialogVisible = true
  }
  const validateDateRange = (rule, value, callback) => {
    // 检查日期范围是否合法的逻辑
    if (syncCheckDate.value && syncCheckDate.value.length === 2) {
      const startDate = syncCheckDate.value[0]
      const endDate = syncCheckDate.value[1]

      // 示例：检查开始日期是否小于结束日期
      if (startDate && endDate && startDate <= endDate) {
        const startTimestamp = new Date(startDate).getTime()
        const endTimestamp = new Date(endDate).getTime()
        const diffMilliseconds = Math.abs(endTimestamp - startTimestamp)
        const diffDays = Math.ceil(diffMilliseconds / (1000 * 60 * 60 * 24))
        const minDate = new Date('2024-01-01').getTime()
        if (diffDays > 30) {
          callback(new Error('日期范围不允许超过30天')) // 校验失败，返回错误信息
        } else if (
          startTimestamp < minDate &&
          !roleAuth(state.auth_role.adminRole)
        ) {
          callback(new Error('2024年1月前的数据不允许同步')) // 校验失败，返回错误信息
        } else {
          callback() // 校验通过
        }
      } else {
        callback(new Error('日期范围不合法，请检查')) // 校验失败，返回错误信息
      }
    } else {
      callback(new Error('日期范围不允许为空')) // 必填校验
    }
  }

  const state = reactive({
    showElTable: false,
    columns: {},
    sortRule: {
      prop: 'factory_name',
      order: 'descending',
    }, // 表格排序规则 列表默认排序按预计上线时间降序
    editCustomerRemark: false, // 修改用户备注
    statusListProps: {
      multiple: true,
      value: 'id',
      label: 'name',
      expandTrigger: 'hover',
      emitPath: false,
    },
    auth_role: {
      factoryRole: ['Factory'],
      adminRole: ['Admin'],
      productionRole: ['Production'],
    },
    selectRowId: 0,
    list: [],
    listLoading: true,
    drawerDialogVisible: false,
    listSort: { prop: 'created_date', order: 'descending' },
    tableHeight: 500,
    layout: 'total, sizes, prev, pager, next, jumper',
    total: 0,
    currentPage: 0,
    selectRows: [],
    showTopSearch: false,
    approveStatusSel: [1], //默认选中待核准
    approveStatus: [],
    workStatusSel: [],
    workStatusList: [],
    queryForm: {
      pageNo: 1,
      pageSize: 50,
      filter: {},
      op: {
        approve_status: 'IN',
        work_status_id: 'IN',
      },
    },
    flowStatusList: [], // 生产状态流
    visiblePopover: false,
    queryFormTopShow: false,
    completeStatus: [
      { value: 0, label: '未完善' },
      { value: 1, label: '已完善' },
    ],
    summaryStatus: [
      { value: 0, label: '新建' },
      { value: 1, label: '已解决' },
      { value: 2, label: '挂起' },
    ],
    factoryList: [],
    changeKeywordsTimer: null,
    changeSummaryKeywordsTimer: null,
    searchTimer: null,
    productPlatformList: [],
    platformListProps: {
      value: 'name',
      label: 'name',
      expandTrigger: 'hover',
      emitPath: false,
    },
    ///////////////////弹窗/////////////////////
    showDialog: false,
    dialogTitle: '新增平台',
    dialogWidth: 500,
    formEditFor: '',
    platform: {
      status: 1,
      type: 'product_platform',
    },
    platformRules: {
      pid: [
        {
          required: true,
          message: '上级属性不能为空',
          trigger: 'blur',
        },
      ],
      name: [
        {
          required: true,
          message: '属性名称不能为空',
          trigger: 'blur',
        },
      ],
    },
    cascaderProps: {
      value: 'id',
      label: 'name',
      checkStrictly: true,
      emitPath: false,
      expandTrigger: 'hover',
    },
    platformTreeList: [],
    syncForm: {
      filter: {
        checkdate: null,
        checks: 1,
      },
      op: {
        checkdate: 'DATETIME',
      },
    },
    syncFormRules: {
      checkDate: [
        {
          validator: validateDateRange,
          trigger: 'blur',
        },
      ],
    },
    syncOrderLoading: false,
    //////////////////////////////////////////
    hardwareUserList: [],
    softwareUserList: [],
    chargeListCopy: [],
    hardwareDepartmentUserIds: [],
    softwareDepartmentUserIds: [],
  })

  const deliveryDate = ref([])
  const predictOnlineTime = ref([])
  const syncCheckDate = ref([])
  const editRef = ref(null)
  const fullscreenContainer = ref(null)
  const platformRef = ref(null)
  const detailRef = ref(null)
  const queryData = async () => {
    state.queryForm.pageNo = 1
    if (state.productInfoDialogVisible) {
      await getOverView(formOverView.value.id)
      $pub('update-product-details-page')
      state.currentProductId = formOverView.value.id
    }
    fetchData()
  }
  const queryDataByTimer = () => {
    if (state.searchTimer) {
      clearTimeout(state.searchTimer)
      state.searchTimer = null
    }
    state.searchTimer = setTimeout(() => {
      fetchData()
    }, 800)
  }

  const editCustomerRemark = (row, auth = false) => {
    if (!auth) {
      $baseMessage(
        '不在【资料录入】阶段，或当前角色不可编辑',
        'warning',
        'vab-hey-message-warning'
      )
      return
    }
    if (row.id) {
      state.editCustomerRemark = true
      editRef.value.showEdit(row)
    }
  }

  const handleSizeChange = (val) => {
    state.queryForm.pageSize = val
    fetchData()
  }
  const handleCurrentChange = (val) => {
    state.queryForm.pageNo = val
    fetchData()
  }

  /**
   * 列表排序规则变动
   * @param {*} param0
   */
  const handleSort = ({ column, prop, order }) => {
    state.listSort.prop = prop
    state.listSort.order = order
    //不同表格字段，对应后台字段排序可能不一致
    const obj = {
      predict_online_time: 'predict_online_time',
      priority_id: 'priority_id',
      assigned_to_id: 'assigned_to_id',
      factory_short_name: 'factory_name',
      hardware_user_name: 'hardware_user_id',
      software_user_name: 'software_user_id',
      layout_user_name: 'layout_user_id',
      work_status_name: 'work_status_id',
    }
    state.queryForm.sort = obj[prop] ? obj[prop] : prop
    let orderStr = ''
    if (order === 'ascending') {
      orderStr = 'ASC'
    } else if (order === 'descending') {
      orderStr = 'DESC'
    }
    console.log('order:', order)

    if (order == null) {
      //取消使用排序后 默认按工厂名称排序
      state.queryForm.sort = 'factory_name'
    }

    state.queryForm.order = orderStr
    fetchData()
  }

  const handleCloseDrawer = () => {
    state.drawerDialogVisible = false
    if (detailRef.value && detailRef.value.refreshListStatus) {
      fetchData()
    }
  }

  /**
   * 重置条件参数
   */
  const handleRefreshOptions = () => {
    initOptions()
    fetchData()
  }
  /**
   * 替换当前url
   */
  const replaceUrl = async () => {
    const path = route.path
    let queryParams = ''
    for (const [key, value] of Object.entries(route.query)) {
      if (key && value) {
        queryParams += `${encodeURIComponent(key)}=${encodeURIComponent(
          value
        )}&`
      }
    }
    const newUrl = `#${path}?${queryParams}`
    window.history.replaceState(null, '', newUrl)
    await nextTick()
  }
  const initOptions = async () => {
    //重置时把url上的参数也消除
    if (route.query.stock_order_code) {
      delete route.query.stock_order_code
      await replaceUrl()
    }
    state.visiblePopover = false
    state.visibleClickPopover = false
    state.approveStatusSel = [1]
    state.workStatusSel = []
    deliveryDate.value = null
    predictOnlineTime.value = null
    // state.queryForm.filter.keywords = null
    // state.queryForm.filter.summary_keywords = null
    // state.queryForm.filter.is_complete = null
    // state.queryForm.filter.delivery_date = null
    // state.queryForm.filter.predict_online_time = null
    // state.queryForm.filter.summary_finish_status = null
    // state.queryForm.filter.factory_id = null
    // state.queryForm.filter.product_platform = null
    // state.queryForm.filter.hardware_user_id = null
    // state.queryForm.filter.software_user_id = null
    // state.queryForm.filter.hasMaterialVerification = null
    // state.queryForm.filter.hasIQC = null
    state.queryForm.filter = {}
    state.queryForm.pageNo = 1
    state.queryForm.pageSize = 50
    hasMaterialVerification.value = 0
    hasIQC.value = 0
  }

  /**
   * 更多筛选
   * @param akey
   */
  const changeQueryFormTopShow = (akey) => {
    state.visibleClickPopover = true // true为点击更多筛选显示事件
    state.visiblePopover = !state.visiblePopover
    state.queryFormTopShow = !state.queryFormTopShow
  }
  /**
   * 处理
   * @param {*} event
   */
  const handleClick = async (event) => {
    // 点击任意地方时如果显示有产品详情弹窗，则隐藏
    if (state.drawerDialogVisible) {
      handleCloseDrawer()
    }
  }
  /**
   * 隐藏更多筛选
   * @param event
   */
  const handleMoreClick = async (event) => {
    if (state.visiblePopover === true) {
      // 这里是先执行了changeQueryFormTopShow()函数判断到visibleClickPopover = true,防止再次隐藏弹窗使窗口无法正常显示
      if (state.visibleClickPopover == true) {
        state.visibleClickPopover = false
        return
      }
      state.visiblePopover = false
      return
    }
  }

  /**
   * 产品列表
   */
  const fetchData = async () => {
    state.list = []
    state.listLoading = true
    state.queryForm.filter['approve_status'] = state.approveStatusSel.join(',')
    state.queryForm.filter['work_status_id'] = state.workStatusSel.join(',')
    state.queryForm.filter['hasMaterialVerification'] =
      hasMaterialVerification.value
    state.queryForm.filter['hasIQC'] = hasIQC.value
    const {
      data: { data, total, currentPage },
    } = await getList(state.queryForm)
    state.list = data
    state.total = total
    state.currentPage = currentPage
    state.listLoading = false
  }

  const showEdit = (row) => {
    state.editCustomerRemark = false
    if (row.id) {
      editRef.value.showEdit(row)
    } else {
      editRef.value.showEdit()
    }
  }

  const getConf = async () => {
    const { data } = await queryConf()
    state.workStatusList = workStatusGetTree(data.work_status)
    state.approveStatus = data.approve_status
    state.factoryList = data.factory_list
    state.hardwareDepartmentUserIds = data.hardware_user_ids
    state.softwareDepartmentUserIds = data.software_user_ids
    getProductPlatformList()
  }

  function approveStatusMap(id) {
    const item = state.approveStatus.find((item) => item.id === id)
    return item?.name
  }

  /**
   * 根据不同人员设置初始查询条件
   */
  const setDefaultQuertForm = async () => {
    if (state.hardwareDepartmentUserIds.includes(user_id)) {
      state.queryForm.filter.hardware_user_id = user_id
    }
    if (state.softwareDepartmentUserIds.includes(user_id)) {
      state.queryForm.filter.software_user_id = user_id
    }
  }

  /**
   * 产品平台
   */
  const getProductPlatformList = async () => {
    let params = {
      filter: {
        type: 'product_platform',
        pid: 0,
        status: 1,
      },
      op: {
        pid: '>',
      },
      sort: 'sort',
      order: 'desc',
    }
    const { data } = await treeCategory(params)
    state.productPlatformList = data
  }
  /**
   * 加载人员列表
   */
  const getChargeList = async () => {
    let params = { third: 'redmine' }
    const { data } = await getUserThirdList(params)
    state.hardwareUserList = Object.assign([], data)
    state.softwareUserList = Object.assign([], data)
    state.chargeListCopy = Object.assign([], data)
  }

  /**
   * 硬件负责人选择栏过滤处理
   * @param {*} query
   */
  const filterHardwareHandlerMethod = (query) => {
    if (
      query ||
      (state.queryForm.filter.hardware_user_id &&
        isNaN(state.queryForm.filter.hardware_user_id))
    ) {
      state.hardwareUserList = handleFilterMethod(query)
      state.queryForm.filter.hardware_user_id = query
    } else {
      state.hardwareUserList = Object.assign([], state.chargeListCopy)
    }
  }

  /**
   * 订单软件人选择栏过滤处理
   * 这里select为单选，使用filter-method时有bug，这里是处理方案，如果是多选的select框请去ProductEdit.vue参考
   * @param {*} query
   */
  const filterSoftHandlerMethod = (query) => {
    if (
      query ||
      (state.queryForm.filter.software_user_id &&
        isNaN(state.queryForm.filter.software_user_id))
    ) {
      state.softwareUserList = handleFilterMethod(query)
      state.queryForm.filter.software_user_id = query
    } else {
      state.softwareUserList = Object.assign([], state.chargeListCopy)
    }
  }

  const handleFilterMethod = (query) => {
    let filterList = []
    if (query) {
      filterList = cloneDeep(
        state.chargeListCopy.filter((item) => {
          let chineseText = chineseFirstText(item.name)
          if (
            item.name.toLowerCase().indexOf(query.toLowerCase()) !== -1 ||
            chineseText.indexOf(query) >= 0
          ) {
            return true
          }
        })
      )
    } else {
      filterList = cloneDeep(state.chargeListCopy)
    }
    return filterList
  }

  /**
   * {0: "资料录入", 1: "资料待审", 2: "预计上线日期", 3: "首件资料录入", 4: "生产中", 5: "待核准", 6: "完成"}
   * @param {*} data
   */
  const workStatusGetTree = (data) => {
    // const midpoint = Math.ceil(data.length / 2)
    const midpoint = 3
    const tree = [
      { id: 1, name: '未上线', children: [] },
      { id: 2, name: '已上线', children: [] },
    ]

    const listLength = data.length

    data.forEach((status, index) => {
      if (index < midpoint) {
        tree[0].children.push(status)
      } else if (index < listLength - 1) {
        tree[1].children.push(status)
      } else {
        tree.push(status)
      }
    })

    return tree
  }
  const getStatusType = (row) => {
    const statusTree = Object.assign([], state.workStatusList)
    for (const status of statusTree) {
      if (status.children) {
        for (const child of status.children) {
          if (child.name === row.work_status_name) {
            return status.name === '未上线' ? 'info' : 'warning'
          }
        }
      }
      if (status.name === row.work_status_name) {
        return 'success'
      }
    }
    return 'info'
  }

  /**
   * 改变订单日期
   * @param e
   */
  const changeDeliveryDate = (e) => {
    if (
      typeof deliveryDate.value == 'object' &&
      deliveryDate.value != null &&
      deliveryDate.value.length > 0
    ) {
      let delivery_date = JSON.stringify(deliveryDate.value)
      if (delivery_date) {
        delivery_date = JSON.parse(delivery_date)
        state.queryForm.filter.delivery_date = delivery_date.join(' - ')
        state.queryForm.op.delivery_date = 'DATETIME'
      }
    } else {
      state.queryForm.filter.delivery_date = null
    }
    fetchData()
  }

  //监听同步的日期范围
  watch(
    () => syncCheckDate.value,
    () => {
      if (
        typeof syncCheckDate.value == 'object' &&
        syncCheckDate.value != null &&
        syncCheckDate.value.length > 0
      ) {
        let check_date = JSON.stringify(syncCheckDate.value)
        if (check_date) {
          check_date = JSON.parse(check_date)
          state.syncForm.filter.checkdate = check_date.join(' - ')
          state.syncForm.op.checkdate = 'DATETIME'
        }
      } else {
        state.syncForm.filter.deliverydate = null
      }
    },
    { deep: true }
  )

  /**
   * 改变预计上线日期
   * @param e
   */
  const changePredictOnlineTime = (e) => {
    if (
      typeof predictOnlineTime.value == 'object' &&
      predictOnlineTime.value != null &&
      predictOnlineTime.value.length > 0
    ) {
      let predict_online_time = JSON.stringify(predictOnlineTime.value)
      if (predict_online_time) {
        predict_online_time = JSON.parse(predict_online_time)
        state.queryForm.filter.predict_online_time =
          predict_online_time.join(' - ')
        state.queryForm.op.predict_online_time = 'DATETIME'
      }
    } else {
      state.queryForm.filter.predict_online_time = null
    }
    fetchData()
  }
  /**
   * 更新状态流
   */
  const getFlowStatusData = async () => {
    // 流程状态信息处理
    const { data } = await getFlowStatus({})
    state.flowStatusList = data
  }

  /**
   * 判断订单所处状态在预设状态流中的位次是否在传进的index之前
   * {0: "资料录入", 1: "资料待审", 2: "预计上线日期", 3: "首件资料录入", 4: "首件资料待审", 5: "生产中", 6: "待核准", 7: "完成"}
   * @param {*} row
   * @param {Number} index
   */
  const workStatusNameIndexSuitable = (row, index) => {
    return (
      state.flowStatusList.findIndex(
        (item) => item.name == row.work_status_name
      ) < index
    )
  }
  /////////////////////弹窗/////////////////////////////
  const openDialog = async (editor) => {
    state.formEditFor = editor
    if (editor == 'editForPlatform') {
      state.dialogTitle = '新增平台'
      state.dialogWidth = 500
    } else {
      state.dialogTitle = '选择日期范围'
      state.dialogWidth = 400
      syncCheckDate.value = [
        moment().startOf('day').format('YYYY-MM-DD'),
        moment().startOf('day').format('YYYY-MM-DD'),
      ]
    }
    await new Promise((resolve) => setTimeout(resolve, 200))
    state.showDialog = true
  }
  const closeDialog = () => {
    platformRef.value?.resetFields()
    state.showDialog = false
  }
  const saveDialog = (editor) => {
    if (editor == 'editForPlatform') {
      platformRef.value.validate(async (valid) => {
        if (valid) {
          const { msg } = await editCategory(state.platform)
          $baseMessage(msg, 'success', 'vab-hey-message-success')
          getConf()
          getPlatformTreeList()
          closeDialog()
        }
      })
    } else {
      syncOrderFormRef.value.validate(async (valid) => {
        if (valid) {
          closeDialog()
          state.syncOrderLoading = true
          await syncOrder(state.syncForm)
            .then((res) => {
              $baseMessage('同步成功', 'success', 'vab-hey-message-success')
              queryData()
            })
            .finally(() => {
              state.syncOrderLoading = false
            })
        }
      })
    }
  }
  const getPlatformTreeList = async () => {
    const { data } = await treeCategory({
      filter: { type: state.platform.type, status: 1 },
      sort: 'sort',
      order: 'desc',
    })
    state.platformTreeList = processData(data)
  }

  const processData = (data) => {
    if (!data || !Array.isArray(data)) return []

    return data.map((level1) => ({
      id: level1.id,
      name: level1.name,
      children: (level1.children || []).map((level2) => ({
        id: level2.id,
        name: level2.name,
        children: (level2.children || []).map((level3) => ({
          id: level3.id,
          name: level3.name,
          disabled: true,
        })),
      })),
    }))
  }

  /////////////////////////////////////////////////////

  /**
   * 表格可勾选的选择项发生变化
   */
  const handleSelectionChange = (selection) => {
    state.selectRows = selection
  }

  /**
   * 勾选订单后即将发送通知
   */
  const sendMessage = () => {
    // state.showDialog = true
    messageSendRef.value.showEdit()
  }

  // 按照顺序排列 columns 的函数
  const getSortedColumns = (order) => {
    const columnMap = new Map(
      allColumns.map((column) => [column.label, column])
    )
    const sortedColumns = order
      .map((label) => columnMap.get(label))
      .filter((column) => column !== undefined)

    // 如果是加工厂角色，过滤掉不需要的列
    if (isProcessingFactory()) {
      return sortedColumns.filter((col) => !col.hideForProcessingFactory)
    }
    return sortedColumns
  }
  const setRouteFilter = async () => {
    if (route.query.stock_order_code) {
      state.queryForm.filter.stock_order_code = route.query.stock_order_code
      state.approveStatusSel = [1, 2, 3]
    }
  }

  /**
   * dom完成加载后执行
   */
  onMounted(async () => {
    if (user_id == 177) {
      // 使用给定顺序进行排序
      const order = [
        '序号',
        '加工厂',
        '订单编号',
        '料号',
        '产品名称',
        '加工数',
        '订单日期',
        '预计上线时间',
        '已进行天数',
        '原理图负责人',
        'layout负责人',
        '软件负责人',
        '状态',
        '核准',
        '客户备注',
      ]
      const sortedColumns = computed(() => getSortedColumns(order))
      state.columns = sortedColumns.value
    } else {
      state.columns = baseColumns.value
    }

    state.showElTable = true

    state.tableHeight = document.documentElement.clientHeight - 180
    state.listLoading = false
    await getConf()
    await setDefaultQuertForm()
    await setRouteFilter()
    getChargeList()
    getPlatformTreeList()
    // fetchData()
    handleSort(state.sortRule)
    getFlowStatusData()
    document
      .querySelector('.modal-product-info')
      .addEventListener('click', handleClick)

    fullscreenContainer.value.addEventListener('click', handleMoreClick)
  })

  /**
   * 本页面消毁时
   */
  onBeforeUnmount(() => {
    document
      .querySelector('.modal-product-info')
      .removeEventListener('click', handleClick)
    fullscreenContainer.value.removeEventListener('click', handleMoreClick)
  })
</script>
<style lang="scss" scoped>
  .product-name {
    display: flex;
    align-items: center;
    overflow: hidden; /* 超出隐藏 */
    white-space: nowrap; /* 防止换行 */
  }

  .name-text {
    overflow: hidden;
    text-overflow: ellipsis; /* 溢出部分显示省略号 */
  }
  .hoverable-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 10px;
    font-size: 15px;
    cursor: pointer;

    /* 初始背景颜色 */
    background-color: white;
    border-radius: 6px;
    transition: background-color 0.3s;

    &:hover {
      color: $base-color-primary;
      background-color: #f0f0f0;
    }

    &.active {
      color: #3977f7; /* 点击后的字体颜色 */
    }

    .check-icon {
      margin-left: auto;
    }
  }
  .pane-icon {
    margin-right: 3px;
  }

  .corner-label {
    position: absolute;
    top: -5px;
    right: -15px;
    width: 63px;
    height: 27px;
    overflow: hidden;
    font-size: 10px;
    color: white;
    text-align: center;
    background-color: red;
    border-radius: 30px 30px 0 0;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.3);
    transform: rotate(45deg) scale(0.7) translateY(50%);
    transform-origin: right bottom;
  }

  .empty-remark {
    color: #999999;
  }
  .over-flow {
    cursor: pointer;
  }
  .notes-box {
    max-height: 850px;
    overflow: auto;
    white-space: break-spaces;
  }
  .edit-key-icon {
    color: #ff5e4b;
  }
  :deep() {
    .common-icon-normal {
      width: 24px !important;
      height: 24px !important;
    }
    .work-status-tag {
      .el-tag__content {
        font-weight: bold !important;
        transform: scale(1.08);
      }
    }
    .el-table {
      .order-number-cell {
        height: auto !important;
        padding: 0 !important;
      }
    }
    .expand-mode-radio-class {
      .el-radio-group {
        position: fixed;
        right: 29px;
      }
    }

    .el-table__body-wrapper .el-table-column--selection > .cell {
      justify-content: center;
    }

    .my-el-radio-button {
      .el-radio-button:last-child .el-radio-button__inner {
        width: 20px;
      }

      .el-dropdown {
        margin-left: -12px;
      }

      .el-radio-button__original-radio:checked + .el-radio-button__inner {
        border-color: #dcdfe6 !important;
        box-shadow: -1px 0 0 #dcdfe6 !important;
      }
    }

    .el-drawer__body {
      padding: 0px !important;
    }

    .el-table--default {
      .cell {
        padding: 0px 8px;
      }
    }

    .el-rate__icon {
      font-size: 22px;
    }

    .el-table__header tr,
    .el-table__header th {
      height: 35px;
      padding: 0;
    }

    .el-table__body tr,
    .el-table__body td {
      height: 42px;
      padding: 0;
    }

    .el-table__placeholder {
      display: none;
    }

    .el-tag--light a {
      color: var(--el-color-primary);
    }

    .el-progress__text {
      margin-left: 0px;
    }

    .el-checkbox-group {
      label:first-child {
        .el-checkbox-button__inner {
          border-radius: 6px 0 0 6px;
        }
      }

      label:last-child {
        .el-checkbox-button__inner {
          border-radius: 0px 6px 6px 0px;
        }
      }
    }

    .el-radio-group {
      label:first-child {
        .el-radio-button__inner {
          border-radius: 6px 0 0 6px !important;
        }
      }

      label:last-child {
        .el-radio-button__inner {
          border-radius: 0px 6px 6px 0px !important;
        }
      }
    }
  }
</style>
<style lang="scss">
  .product-name-tag-danger {
    height: 20px;
    padding: 0 4px;
    font-weight: 500 !important;
    line-height: 20px;
    color: #ff5e4b;
    --el-tag-border-color: rgba(255, 94, 75, 0.2) !important;
  }
  .product-name-tag-primary {
    height: 20px;
    padding: 0 4px;
    font-weight: 500 !important;
    line-height: 20px;
    color: #3977f3;
    --el-tag-border-color: rgba(57, 119, 243, 0.2) !important;
  }

  .production-drawer {
    .el-drawer__body {
      padding: 0px !important;
    }
  }
</style>
