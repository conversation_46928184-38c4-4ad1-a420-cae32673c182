<template>
  <div ref="fullscreenContainer" class="">
    <el-form :inline="true" :model="state.queryForm">
      <vab-query-form>
        <vab-query-form-left-panel :span="18">
          <el-form-item>
            <el-checkbox-group
              v-model="state.approveStatusSel"
              size="default"
              @change="queryDataByTimer"
              class="checkbox-group"
            >
              <el-checkbox-button
                v-for="op in state.conf.approve_status"
                :key="op"
                :label="op.id"
              >
                {{ op.name }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model.trim="state.queryForm.filter.keywords"
              clearable
              placeholder="请输入编号/料号/产品名称"
              @input="queryDataByTimer"
            />
          </el-form-item>
          <el-form-item>
            <el-date-picker
              style="width: 240px"
              v-model="state.orderDateRange"
              start-placeholder="截止开始日期"
              end-placeholder="截止结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="~"
              type="daterange"
              unlink-panels
              @change="changeDate('order_date')"
            />
          </el-form-item>
          <el-form-item v-if="!isProcessingFactory()">
            <el-popover
              ref="popoverRef"
              :visible="state.visiblePopover"
              placement="bottom"
              :width="800"
              :show-arrow="false"
              popper-class="el-form-in-popover"
              trigger="click"
            >
              <template #reference>
                <el-button ref="buttonRef" @click.stop="changeQueryFormTopShow">
                  <vab-icon
                    :icon="
                      state.visiblePopover ? 'filter-2-line' : 'filter-2-fill'
                    "
                    :style="{ 'font-size': '16px' }"
                  />
                  更多筛选
                </el-button>
              </template>

              <el-form label-width="80px">
                <el-row>
                  <el-form-item label="订单类型">
                    <el-checkbox-group class="input-width">
                      <el-checkbox-group v-model="state.orderTypeSel">
                        <el-checkbox
                          @change="queryDataByTimer"
                          v-for="item in state.conf.order_type"
                          :label="item.id"
                          :value="item.id"
                          :key="item.id"
                        >
                          {{ item.name }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </el-checkbox-group>
                  </el-form-item>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="软件负责人">
                      <PersonnelSelect
                        class="input-width"
                        v-model:value="state.queryForm.filter.software_user_id"
                        :option-list="state.chargeList"
                        @change="queryData"
                      />
                    </el-form-item>
                    <el-form-item label="组装负责人">
                      <PersonnelSelect
                        class="input-width"
                        v-model:value="
                          state.queryForm.filter[
                            'assemble_order_info.assemble_user_id'
                          ]
                        "
                        :option-list="state.chargeList"
                        @change="queryData"
                      />
                    </el-form-item>
                    <el-form-item label="业务员">
                      <PersonnelSelect
                        class="input-width"
                        v-model:value="state.queryForm.filter.sales_user_id"
                        :option-list="state.chargeList"
                        @change="queryData"
                      />
                    </el-form-item>

                    <el-form-item label="是否完善">
                      <el-select
                        class="input-width"
                        v-model="
                          state.queryForm.filter[
                            'assemble_order_info.is_complete'
                          ]
                        "
                        @change="queryData"
                        clearable
                        placeholder="请选择状态"
                      >
                        <el-option
                          v-for="(sitem, skey) in state.completeStatus"
                          :key="skey"
                          :label="sitem.label"
                          :value="sitem.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="订单阶段">
                      <el-select
                        class="input-width"
                        v-model="state.workStatusSel"
                        multiple
                        @change="queryDataByTimer"
                        clearable
                        placeholder="请选择状态"
                      >
                        <el-option
                          v-for="(witem, wkey) in state.conf['work_status']"
                          :key="wkey"
                          :label="witem.name"
                          :value="witem.id"
                        >
                          <div class="flex-algin-center tag-selection-item">
                            <el-tag
                              :style="mapWorkStatusStyle(witem.key)"
                              style="
                                min-width: 44px;
                                margin-top: 5px;
                                border: none;
                              "
                            >
                              <span style="font-weight: bold">
                                {{ witem.name }}
                              </span>
                            </el-tag>
                          </div>
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="阶段状态">
                      <el-cascader
                        class="input-width"
                        v-model="state.workTagSel"
                        :options="state.conf.work_tag"
                        :props="state.workTagProps"
                        @change="queryDataByTimer"
                        clearable
                        placeholder="订单状态"
                        collapse-tags
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="MAC地址">
                      <el-input
                        class="input-width"
                        v-model.trim="state.queryForm.filter.mac_address"
                        clearable
                        placeholder="请输入MAC地址"
                        @input="queryDataByTimer"
                      />
                    </el-form-item>
                    <el-form-item label="SN号">
                      <el-input
                        class="input-width"
                        v-model.trim="state.queryForm.filter.sn_code"
                        clearable
                        placeholder="请输入SN号"
                        @input="queryDataByTimer"
                      />
                    </el-form-item>
                    <el-form-item label="PCBA">
                      <el-input
                        class="input-width"
                        v-model.trim="state.queryForm.filter.pcba_remark"
                        clearable
                        placeholder="请输入PCBA"
                        @input="queryDataByTimer"
                      />
                    </el-form-item>
                    <el-form-item label="回货日期">
                      <el-date-picker
                        class="input-width"
                        v-model="state.backDateRange"
                        clearable
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        range-separator="-"
                        type="daterange"
                        @change="changeDate('first_back_date')"
                      />
                    </el-form-item>
                    <el-form-item label="交货日期">
                      <el-date-picker
                        class="input-width"
                        v-model="state.deliveryDateRange"
                        clearable
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        range-separator="-"
                        type="daterange"
                        @change="changeDate('delivery_date')"
                      />
                    </el-form-item>
                    <el-form-item
                      label="组装公司"
                      v-if="!isProcessingFactory()"
                    >
                      <el-select
                        class="input-width"
                        v-model="state.queryForm.filter['assemble_address']"
                        @change="queryData"
                        clearable
                        placeholder="请选择组装公司"
                      >
                        <el-option
                          v-for="(sitem, skey) in state.conf.shipment_place"
                          :key="skey"
                          :label="sitem.name"
                          :value="sitem.name"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- <el-form-item label="整机SN号">
                  <el-input
                    v-model.trim="state.queryForm.filter.sn_remark"
                    clearable
                    placeholder="请输入整机SN号"
                    @input="queryDataByTimer"
                  />
                </el-form-item> -->
              </el-form>
            </el-popover>
          </el-form-item>
          <el-form-item>
            <el-button
              type="default"
              :icon="RefreshIcon"
              @click="handleRefreshOptions"
            >
              重置
            </el-button>
          </el-form-item>
          <el-form-item v-if="!isProcessingFactory()">
            <span style="margin-right: 4px; color: #333">齐料模式</span>
            <el-switch v-model="state.showMaterial" />
          </el-form-item>
        </vab-query-form-left-panel>
        <vab-query-form-right-panel :span="6">
          <el-form-item style="margin-right: 0px !important">
            <el-button
              v-if="isHasSuperRole() || isHasRole('assembleRole')"
              type="primary"
              @click="showEdit"
            >
              <vab-icon class="button-icon" icon="add" is-custom-svg />
              新增订单
            </el-button>
            <el-button plain @click="doExport" v-if="!isProcessingFactory()">
              <vab-icon class="button-icon" icon="export" is-custom-svg />
              导出
            </el-button>
            <el-popover
              placement="bottom"
              trigger="click"
              content="this is content, this is content, this is content"
            >
              <template #reference>
                <el-button
                  v-if="isHasSuperRole() || isHasRole('assembleRole')"
                  type="default"
                  ref="buttonRef"
                  style="margin-left: 8px !important"
                >
                  <vab-icon
                    icon="arrow-down-s-line"
                    style="width: 24px; margin-left: -10px"
                  />
                  更多
                </el-button>
              </template>

              <div>
                <div
                  class="hoverable-item"
                  @click="openSyncDialog()"
                  :loading="state.syncOrderLoading"
                >
                  <el-icon
                    style="width: 24px; height: 24px"
                    v-if="state.syncOrderLoading"
                  >
                    <Loading />
                  </el-icon>
                  <CommonIcon
                    v-if="!state.syncOrderLoading"
                    :type="IconType.Refresh"
                  />
                  同步订单
                </div>
              </div>
            </el-popover>
            <el-popover
              v-if="!state.showMaterial && !isProcessingFactory()"
              ref="columnPopoverRef"
              :visible="state.columnVisiblePopover"
              placement="bottom-end"
              :width="200"
              :show-arrow="false"
              popper-class="column-set-popover"
              popper-style="padding:16px 0px 0px 0px"
              trigger="click"
            >
              <template #reference>
                <el-button
                  style="width: 20px"
                  plain
                  @click.stop="showColumnForm"
                >
                  <vab-icon
                    style="margin-right: 0px"
                    class="button-icon"
                    icon="setting"
                    is-custom-svg
                  />
                </el-button>
              </template>
              <div style="padding-left: 8px">
                <span style="padding-right: 16px; padding-left: 8px">
                  请选择列表中要展示的信息
                </span>
                <el-checkbox
                  style="padding-left: 8px"
                  v-model="state.columnAllChecked"
                  :indeterminate="state.columnIndeterminate"
                  @change="(value) => handleAllChecked(value)"
                >
                  全部
                </el-checkbox>
                <el-scrollbar height="540px">
                  <draggable
                    v-model="filteredColumns"
                    ghost-class="ghost"
                    :force-fallback="true"
                    chosen-class="chosenClass"
                    animation="300"
                    filter=".unmover"
                    handle=".move-box"
                    draggable=".mover-item"
                    @end="onDragEnd"
                  >
                    <template #item="{ element }">
                      <div
                        style="display: flex"
                        :class="
                          !element.fixed
                            ? 'item mover-item'
                            : 'item unmover fix-item'
                        "
                      >
                        <el-checkbox
                          :disabled="!element.fixed ? false : true"
                          v-model="element.show"
                          @change="changeColumnchecked"
                        >
                          {{ element.label }}
                        </el-checkbox>
                        <div class="move-box" style="width: 100%; height: 32px">
                          <div class="move-box-icon">
                            <vab-icon icon="draggable-move" is-custom-svg />
                          </div>
                        </div>
                      </div>
                    </template>
                  </draggable>
                </el-scrollbar>
              </div>
              <div class="flex-align-center column-set-bottom">
                <el-button
                  style="margin-left: 8px; font-size: 14px"
                  size="small"
                  link
                  type="primary"
                  @click="handleResetColumn"
                >
                  恢复默认
                </el-button>
                <div style="margin-right: 8px">
                  <el-button
                    style="padding: 3px 6px; font-size: 14px"
                    size="small"
                    @click="showColumnForm"
                  >
                    取消
                  </el-button>
                  <el-button
                    style="padding: 3px 6px; font-size: 14px"
                    size="small"
                    type="primary"
                    @click="handleSaveColumn"
                  >
                    保存
                  </el-button>
                </div>
              </div>
            </el-popover>
          </el-form-item>
        </vab-query-form-right-panel>
      </vab-query-form>
    </el-form>
    <el-table
      class="order-list-table"
      v-if="state.showElTable"
      v-loading="state.listLoading"
      border
      :data="state.list"
      default-expand-all
      row-key="id"
      :default-sort="state.listSort"
      @sort-change="handleSort"
      size="default"
      :height="state.tableHeight"
      @selection-change="handleSelectionChange"
      @row-click="handleEdit"
    >
      <template #empty>
        <CommonEmpty :description="'暂无数据'" />
      </template>
      <el-table-column align="center" type="selection" fixed width="36" />
      <template v-for="(column, index) in state.columns" :key="column.prop">
        <el-table-column
          :id="index"
          :type="column.type"
          v-if="shouldShowColumn(column)"
          :label="column.label"
          :align="column.align"
          :property="column.prop"
          :fixed="column.fixed"
          :width="column.width"
          :sortable="column.sortable"
          :min-width="
            column.type === 'text' || column.type === 'tag'
              ? getColumnWidth(column.prop, state.list, column.minWidth)
              : column.minWidth
          "
          :show-overflow-tooltip="column.showOverflowTooltip"
        >
          <template v-if="column.template" #default="scope">
            <div
              v-if="column.prop === 'code'"
              @click.stop="handleEdit(scope.row)"
              style="width: 100%; white-space: nowrap; cursor: pointer"
            >
              <span style="display: flex; align-items: center">
                <span style="overflow: hidden; text-overflow: ellipsis">
                  {{ scope.row.code }}
                </span>
                <!-- <span
                  style="overflow: hidden; text-overflow: ellipsis"
                  :style="
                    scope.row.is_complete == 0
                      ? 'width: calc(100% - 24px);'
                      : ''
                  "
                >
                  {{ scope.row.code }}
                </span>
                <vab-icon
                  v-if="scope.row.is_complete == 0"
                  icon="edit-2-fill"
                  class="edit-key-icon"
                /> -->
              </span>
            </div>
            <span
              v-if="column.prop === 'order_type'"
              :style="mapOrderTypeStyle(scope.row.order_type)"
            >
              {{ mapConfText('order_type', scope.row.order_type) }}
            </span>
            <el-tag
              v-if="column.prop === 'assemble_type'"
              :style="mapAssembleTypeStyle(scope.row.assemble_type)"
            >
              {{ mapConfText('assemble_type', scope.row.assemble_type) }}
            </el-tag>
            <el-tag
              v-if="column.prop === 'completeness_status'"
              :style="mapCompletenessStatusStyle(scope.row.completeness_status)"
            >
              {{
                mapConfText(
                  'completeness_status',
                  scope.row.completeness_status
                )
              }}
            </el-tag>
            <span v-else-if="column.prop === 'sales_user'">
              {{ scope.row.sales_user }}
              <span v-if="scope.row.sales_user && scope.row.has_not_audit">
                <vab-icon
                  icon="user_todo"
                  is-custom-svg
                  class="common-icon"
                  style="margin: 0px 0px"
                />
              </span>
              <span
                v-if="
                  scope.row.hardware_upload_status != 1 &&
                  scope.row.hardware_user_name &&
                  scope.row.hardware_audit_status == 2
                "
              >
                <vab-icon
                  icon="no_pass"
                  is-custom-svg
                  class="common-icon"
                  style="margin: 0px 0px"
                />
              </span>
            </span>
            <span v-if="column.prop === 'shipment_place'">
              {{ mapConfText('shipment_place', scope.row.shipment_place) }}
            </span>
            <template v-if="column.prop === 'work_status_tag'">
              <div>
                <el-tag
                  type="info"
                  v-for="(wItem, wKey) in scope.row.work_status_tag"
                  :key="wKey"
                >
                  {{ wItem }}
                </el-tag>
              </div>
            </template>
            <template v-if="column.prop === 'work_status.sort'">
              <div
                v-if="!scope.row.show_component"
                @mouseover="showRowComponent(scope)"
              >
                <el-tag
                  :style="
                    mapWorkStatusStyle(
                      mapWorkStatusKey(scope.row.work_status_id),
                      scope.row
                    )
                  "
                  style="margin-left: 10px"
                >
                  {{
                    mapConfText(
                      'work_status',
                      scope.row.work_status_key,
                      'key',
                      scope
                    )
                  }}
                  <!-- <span
                    v-if="scope.row.work_status_append_text"
                    style="margin: 2px 0"
                  >
                    •
                  </span>
                  <span v-if="scope.row.work_status_append_text">
                    <el-tooltip
                      :content="scope.row.work_status_tips"
                      placement="top"
                      :disabled="!scope.row.work_status_tips"
                    >
                      {{ scope.row.work_status_append_text }}
                    </el-tooltip>
                  </span> -->
                </el-tag>
              </div>
              <el-select
                v-else
                :class="[
                  'none-border-on-common',
                  'm-2',
                  'tag-select',
                  scope.row.approve_status !== 1
                    ? 'no-background-disabled-selection'
                    : '',
                ]"
                v-model="scope.row.work_status_id"
                placeholder=""
                remote
                collapse-tags
                style="width: 100%"
                :disabled="scope.row.approve_status !== 1 ? true : false"
                @change="(val) => handleSaveWorkStatus(val, scope)"
              >
                <template #prefix>
                  <el-tag
                    :style="
                      mapWorkStatusStyle(
                        mapWorkStatusKey(scope.row.work_status_id),
                        scope.row
                      )
                    "
                  >
                    {{
                      mapConfText(
                        'work_status',
                        scope.row.work_status_key,
                        'key',
                        scope
                      )
                    }}
                    <!-- <span
                      v-if="scope.row.work_status_append_text"
                      style="margin: 2px 0"
                    >
                      •
                    </span>
                    <span v-if="scope.row.work_status_append_text">
                      <el-tooltip
                        :content="scope.row.work_status_tips"
                        placement="top"
                        :disabled="!scope.row.work_status_tips"
                      >
                        {{ scope.row.work_status_append_text }}
                      </el-tooltip>
                    </span> -->
                  </el-tag>
                </template>
                <el-option
                  v-for="item in state.conf.work_status"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  <div class="flex-algin-center tag-selection-item">
                    <el-tag
                      :style="mapWorkStatusStyle(item.key, scope.row)"
                      style="min-width: 44px; margin-top: 5px; border: none"
                    >
                      <span style="font-weight: bold">
                        {{ mapConfText('work_status', item.key, 'key', scope) }}
                      </span>
                    </el-tag>
                  </div>
                </el-option>
              </el-select>
            </template>
            <span
              v-if="column.prop === 'approve_status'"
              :style="mapApproveStatusStyle(scope.row.approve_status)"
            >
              <vab-icon
                class="text-icon"
                :icon="mapApproveStatusIcon(scope.row.approve_status)"
                is-custom-svg
              />
              <span>
                {{ mapConfText('approve_status', scope.row.approve_status) }}
              </span>
            </span>
            <span v-if="column.prop === 'checked_time'">
              {{ scope.row.checked_time?.slice(0, 10) }}
            </span>
            <span v-if="column.prop === 'payment_time'">
              {{ scope.row.payment_time?.slice(0, 10) }}
            </span>

            <!-- <span
              v-if="column.prop === 'completeness_remark'"
              class="over-flow"
              :class="{ 'empty-remark': !scope.row.completeness_remark }"
              @click.stop="editRemark(scope.row, 'material')"
            >
              {{ scope.row.completeness_remark || '无' }}
            </span> -->
            <el-popover
              v-else-if="column.prop === 'completeness_remark'"
              placement="top"
              :width="400"
              trigger="hover"
              :content="scope.row.completeness_remark"
              effect="dark"
              raw-content
              :disabled="
                scope.row.completeness_remark &&
                scope.row.completeness_remark.length > 15
                  ? false
                  : true
              "
            >
              <template #reference>
                <span
                  class="over-flow"
                  :class="{ 'empty-remark': !scope.row.completeness_remark }"
                  @click.stop="editRemark(scope.row, 'material')"
                >
                  {{ scope.row.completeness_remark || '无' }}
                </span>
              </template>
              <template #default>
                <div class="notes-box">
                  <span
                    :class="{ 'empty-remark': !scope.row.completeness_remark }"
                    @click.stop="editRemark(scope.row, 'material')"
                  >
                    {{ scope.row.completeness_remark || '无' }}
                  </span>
                </div>
              </template>
            </el-popover>
            <el-popover
              v-else-if="column.prop === 'material_remark'"
              placement="top"
              :width="400"
              trigger="hover"
              effect="dark"
              :content="scope.row.material_remark"
              raw-content
              :disabled="
                scope.row.material_remark &&
                scope.row.material_remark.length > 15
                  ? false
                  : true
              "
            >
              <template #reference>
                <span
                  class="over-flow"
                  :class="{ 'empty-remark': !scope.row.material_remark }"
                  @click.stop="editRemark(scope.row, 'material_remark')"
                >
                  {{ scope.row.material_remark || '无' }}
                </span>
              </template>
              <template #default>
                <div class="notes-box">
                  <span
                    :class="{ 'empty-remark': !scope.row.material_remark }"
                    @click.stop="editRemark(scope.row, 'material_remark')"
                  >
                    {{ scope.row.material_remark || '无' }}
                  </span>
                </div>
              </template>
            </el-popover>
            <el-popover
              v-else-if="column.prop === 'other_remark'"
              placement="top"
              effect="dark"
              :width="400"
              trigger="hover"
              :content="scope.row.other_remark"
              raw-content
              :disabled="
                scope.row.other_remark && scope.row.other_remark.length > 15
                  ? false
                  : true
              "
            >
              <template #reference>
                <span
                  class="over-flow"
                  :class="{ 'empty-remark': !scope.row.other_remark }"
                  @click.stop="editRemark(scope.row, 'other_remark')"
                >
                  {{ scope.row.other_remark || '无' }}
                </span>
              </template>
              <template #default>
                <div class="notes-box">
                  <span
                    :class="{ 'empty-remark': !scope.row.other_remark }"
                    @click.stop="editRemark(scope.row, 'other_remark')"
                  >
                    {{ scope.row.other_remark || '无' }}
                  </span>
                </div>
              </template>
            </el-popover>

            <template v-if="column.aliasProp === 'material_status'">
              <div
                v-if="!scope.row.show_component"
                @mouseover="showRowComponent(scope)"
              >
                <vab-icon
                  style="width: 24px; height: 24px; margin-right: 0px"
                  :icon="
                    materialStatusIcon[
                      scope.row['material_status_' + column.material_type]
                    ]
                  "
                  is-custom-svg
                />
              </div>
              <el-select
                v-else
                :class="[
                  'only-show-icon-selection',
                  materialStatusEdit[scope.row.approve_status]
                    ? 'no-background-disabled-selection'
                    : '',
                ]"
                v-model="scope.row['material_status_' + column.material_type]"
                placeholder=""
                style="width: 100%"
                :disabled="materialStatusEdit[scope.row.approve_status]"
                @change="
                  handleSaveMaterialStatus(scope.row, column.material_type)
                "
              >
                <template #prefix>
                  <vab-icon
                    style="width: 24px; height: 24px; margin-right: -6px"
                    :icon="
                      materialStatusIcon[
                        scope.row['material_status_' + column.material_type]
                      ]
                    "
                    is-custom-svg
                  />
                </template>
                <el-option
                  v-for="item in state.conf.material_status"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  <div class="flex-algin-center">
                    <vab-icon
                      style="width: 24px; height: 24px"
                      :icon="materialStatusIcon[item.id]"
                      is-custom-svg
                    />
                    <span class="icon-select-item">{{ item.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </template>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        label="操作"
        align="center"
        width="80"
        fixed="right"
        v-if="!isProcessingFactory()"
      >
        <template #default="{ row }">
          <el-tooltip
            :content="'不可编辑'"
            :disabled="isHasEditRole(row)"
            placement="top"
          >
            <div>
              <el-button
                :disabled="!isHasEditRole(row)"
                text
                type="primary"
                @click.stop="showEdit(row)"
              >
                编辑
              </el-button>
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      background
      :current-page="state.queryForm.pageNo"
      :layout="state.layout"
      :page-size="state.queryForm.pageSize"
      :total="state.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <Edit ref="editRef" @fetch-data="fetchData" />
    <el-drawer
      v-model="state.drawerDialogVisible"
      :before-close="state.closeDialog"
      :modal="false"
      :append-to-body="true"
      modal-class="modal-product-info"
      direction="rtl"
      size="70vw"
      :with-header="false"
      class="production-drawer"
      ref="productDrawerRef"
    >
      <Details
        ref="detailRef"
        @close="handleCloseDrawer"
        @fetch-data="fetchData()"
        :modal="'drawer'"
        :assemble-order-id="state.selectRowId"
        :drawer-dialog-visible="state.drawerDialogVisible"
      />
    </el-drawer>
    <firefly-dialog
      v-model="state.showDialog"
      :title="state.dialogTitle"
      :width="state.dialogWidth"
      @close="closeDialog()"
      @confirm="saveDialog(state.formEditFor)"
      show-default-button
      :footer-class="'foot-btn-bar-small-padding'"
    >
      <div v-if="state.formEditFor == 'editForSysnOrder'">
        <el-form
          ref="syncOrderFormRef"
          label-width="80px"
          :model="state.syncForm"
          :rules="state.syncFormRules"
          label-position="top"
        >
          <el-form-item label="" prop="checkDate">
            <el-date-picker
              style="width: 200px"
              v-model="syncCheckDate"
              start-placeholder="截止开始日期"
              end-placeholder="截止结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              range-separator="~"
              type="daterange"
              unlink-panels
              :shortcuts="state.shortcuts"
            />
          </el-form-item>
        </el-form>
      </div>
    </firefly-dialog>
  </div>
</template>

<script setup>
  //接口
  import {
    conf,
    getList,
    changeMaterialStatus,
    changeWorkStatus,
    exportOrder,
    syncOrder,
  } from '@/api/assembleOrder'
  import { getUserThirdList } from '@/api/user'
  //组件
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import CommonEmpty from '~/src/components/CommonEmpty.vue'
  import Edit from './Edit.vue'
  import Details from './Details.vue'
  import PersonnelSelect from '~/src/components/PersonnelSelect.vue'
  //通用
  import {
    mapOrderTypeStyle,
    mapAssembleTypeStyle,
    mapApproveStatusStyle,
    mapApproveStatusIcon,
    mapMaterialStatusIcon,
    mapWorkStatusStyle,
    mapCompletenessStatusStyle,
  } from '@/json/assembleOrder'
  import draggable from 'vuedraggable'
  import { cloneDeep, debounce, each, map, over, throttle } from 'lodash'
  import { roleAuth } from '@/utils/auth'
  import moment from 'moment'
  import { useUserStore } from '@/store/modules/user'
  import { IconType } from '~/src/icon'
  const route = useRoute()
  const userStore = useUserStore()
  const { token, redmine, user_settings, user_id, username } =
    storeToRefs(userStore)
  const $baseConfirm = inject('$baseConfirm')

  /**
   * 检查是否为加工厂角色
   * @returns {boolean}
   */
  const isProcessingFactory = () => {
    return roleAuth(['ProcessingFactory'])
  }
  const $baseMessage = inject('$baseMessage')
  const $pub = inject('$pub')
  const $sub = inject('$sub')
  const $unsub = inject('$unsub')

  // 加工厂角色显示的字段
  const processingFactoryFields = [
    'index',
    'code',
    'product_name',
    'num',
    'product_code',
    'factory_name',
    'order_type',
    'order_date',
    'order_days',
    'assemble_type',
    'assemble_address',
    'predict_online_date',
    'actual_online_date',
    'pass_rate',
    'work_status.sort',
    'work_status_tag',
  ]

  // 所有列配置
  const allColumns = [
    {
      prop: 'index',
      label: '序号',
      type: 'index',
      minWidth: 50,
      fixed: 'left',
      align: 'center',
      show: true,
    },
    {
      prop: 'code',
      label: '订单编号',
      align: 'left',
      width: 140,
      fixed: 'left',
      sortable: true,
      template: true,
      showOverflowTooltip: true,
      show: true,
    },
    {
      prop: 'product_name',
      label: '产品名称',
      align: 'left',
      fixed: 'left',
      minWidth: 180,
      sortable: true,
      template: false,
      showOverflowTooltip: true,
      show: true,
    },
    {
      prop: 'num',
      label: '数量',
      align: 'center',
      fixed: 'left',
      width: 72,
      sortable: false,
      showOverflowTooltip: false,
      show: true,
    },
    {
      prop: 'product_code',
      label: '料号',
      align: 'left',
      sortable: true,
      minWidth: 148,
      showOverflowTooltip: true,
      show: false,
    },
    {
      prop: 'factory_name',
      label: '加工厂',
      align: 'left',
      sortable: true,
      minWidth: 120,
      showOverflowTooltip: true,
      show: false,
    },

    {
      prop: 'order_type',
      label: '订单类型',
      align: 'center',
      width: 100,
      sortable: true,
      template: true,
      show: true,
    },
    {
      prop: 'order_date',
      label: '订单日期',
      align: 'center',
      width: 104,
      sortable: true,
      show: true,
    },
    {
      prop: 'order_days',
      label: '天数',
      align: 'center',
      width: 72,
      sortable: false,
      show: true,
    },
    {
      prop: 'checked_time',
      label: '审核时间',
      align: 'center',
      width: 104,
      sortable: true,
      template: true,
      show: false,
    },
    {
      prop: 'payment_time',
      label: '出账时间',
      align: 'center',
      width: 104,
      sortable: true,
      template: true,
      show: false,
    },
    {
      prop: 'assemble_type',
      label: '组装类型',
      align: 'center',
      width: 100,
      sortable: true,
      template: true,
      show: true,
    },
    {
      prop: 'assemble_address',
      label: '地点',
      align: 'center',
      width: 76,
      sortable: true,
      show: true,
    },
    {
      prop: 'first_back_date',
      label: '首次回货日期',
      align: 'center',
      width: 130,
      sortable: true,
      show: false,
    },
    {
      prop: 'predict_online_date',
      label: '预计上线日期',
      align: 'center',
      width: 130,
      sortable: true,
      show: true,
    },
    {
      prop: 'actual_online_date',
      label: '实际上线日期',
      align: 'center',
      width: 130,
      sortable: true,
      show: true,
    },
    {
      prop: 'shipment_place',
      label: '出货地点',
      align: 'center',
      width: 84,
      sortable: false,
      template: true,
      show: false,
    },
    {
      prop: 'predict_delivery_date',
      label: '预定交货日期',
      align: 'center',
      width: 130,
      sortable: true,
      show: false,
    },
    {
      prop: 'delivery_date',
      label: '交货日期',
      align: 'center',
      width: 104,
      sortable: true,
      show: false,
    },
    {
      prop: 'work_status.sort',
      label: '订单阶段',
      align: 'left',
      width: 104,
      sortable: true,
      template: true,
      show: true,
    },
    {
      prop: 'work_status_tag',
      label: '阶段状态',
      align: 'left',
      minWidth: 90,
      sortable: false,
      template: true,
      show: true,
      type: 'tag',
    },
    {
      prop: 'sales_user',
      label: '业务员',
      align: 'center',
      width: 100,
      sortable: false,
      template: true,
      show: true,
      showOverflowTooltip: true,
    },
    {
      prop: 'software_user_name',
      label: '软件负责人',
      align: 'center',
      width: 100,
      sortable: false,
      show: true,
      showOverflowTooltip: true,
    },
    {
      prop: 'assemble_user_name',
      label: '组装负责人',
      align: 'center',
      width: 100,
      sortable: false,
      show: true,
      showOverflowTooltip: true,
    },
    {
      prop: 'test_user_name',
      label: '测试负责人',
      align: 'center',
      width: 100,
      sortable: false,
      show: true,
      showOverflowTooltip: true,
    },
    {
      prop: 'stock_order_code',
      label: '备货单号',
      align: 'center',
      width: 140,
      sortable: false,
      show: false,
      showOverflowTooltip: true,
    },

    {
      prop: 'completeness_status',
      label: '备料状态',
      align: 'left',
      width: 135,
      sortable: false,
      template: true,
      show: false,
    },
    //这里用来标识多个齐料类型的状态的位置，onMount时会用多个齐料类型替换
    //同步的属性有aliasProp，align，width，sortable，template，show
    {
      prop: 'material_status',
      aliasProp: 'material_status',
      label: '齐料状态',
      align: 'center',
      width: 122,
      sortable: true,
      template: true,
      material_type: 0,
      show: false,
    },

    {
      prop: 'approve_status',
      label: '核准状态',
      align: 'left',
      width: 102,
      sortable: false,
      template: true,
      show: true,
    },
    {
      prop: 'pass_rate',
      label: '直通率',
      align: 'center',
      width: 100,
      sortable: false,
      show: false,
    },
    {
      prop: 'completeness_remark',
      label: '齐料备注',
      align: 'left',
      minWidth: 90,
      sortable: false,
      template: true,
      show: false,
      type: 'text',
      showOverflowTooltip: false,
    },
    {
      prop: 'material_remark',
      label: '材料情况',
      align: 'left',
      minWidth: 90,
      sortable: false,
      template: true,
      show: true,
      type: 'text',
      showOverflowTooltip: false,
    },
    {
      prop: 'other_remark',
      label: '其他备注',
      align: 'left',
      minWidth: 90,
      sortable: false,
      template: true,
      show: false,
      type: 'text',
      showOverflowTooltip: false,
    },
  ]

  // 根据角色过滤列
  const baseColumns = computed(() => {
    if (isProcessingFactory()) {
      // 按照 processingFactoryFields 的顺序排序列
      return processingFactoryFields
        .map((fieldProp) => allColumns.find((col) => col.prop === fieldProp))
        .filter(Boolean) // 过滤掉找不到的列
    }
    return allColumns
  })

  // 判断列是否应该显示
  const shouldShowColumn = (column) => {
    if (isProcessingFactory()) {
      // 加工厂角色：如果列在允许范围内，则显示
      return processingFactoryFields.includes(column.prop)
    }
    // 其他角色：按原有的show属性
    return column.show
  }
  //设置后列
  const filteredColumns = ref([])

  //齐料模式的配置
  const materialOption = ref({
    fixedColumn: [
      'code',
      'product_name',
      'num',
      'delivery_date',
      'checked_time',
      'payment_time',
      'completeness_status',
    ],
    endColumn: ['completeness_remark', 'material_remark', 'other_remark'],
  })
  const materialColumns = ref()
  const buttonRef = ref()
  const popoverRef = ref()
  const fullscreenContainer = ref()
  const columnPopoverRef = ref()
  const editRef = ref()
  const detailRef = ref()
  const syncCheckDate = ref([])
  const syncOrderFormRef = ref(null)
  //初始化时是否开始齐料模式
  let defaultShowMaterialList = localStorage.getItem(
    'assembleListShowType' + user_id
  )

  const validateDateRange = (rule, value, callback) => {
    // 检查日期范围是否合法的逻辑
    if (syncCheckDate.value && syncCheckDate.value.length === 2) {
      const startDate = syncCheckDate.value[0]
      const endDate = syncCheckDate.value[1]

      // 示例：检查开始日期是否小于结束日期
      if (startDate && endDate && startDate <= endDate) {
        const startTimestamp = new Date(startDate).getTime()
        const endTimestamp = new Date(endDate).getTime()
        const diffMilliseconds = Math.abs(endTimestamp - startTimestamp)
        const diffDays = Math.ceil(diffMilliseconds / (1000 * 60 * 60 * 24))
        const minDate = new Date('2024-01-01').getTime()
        if (diffDays > 15) {
          callback(new Error('日期范围不允许超过15天')) // 校验失败，返回错误信息
        } else if (
          startTimestamp < minDate &&
          !roleAuth(state.auth_role.adminRole)
        ) {
          callback(new Error('2024年1月前的数据不允许同步')) // 校验失败，返回错误信息
        } else {
          callback() // 校验通过
        }
      } else {
        callback(new Error('日期范围不合法，请检查')) // 校验失败，返回错误信息
      }
    } else {
      callback(new Error('日期范围不允许为空')) // 必填校验
    }
  }
  const state = reactive({
    //搜索框
    queryForm: {
      pageNo: 1,
      pageSize: 20,
      filter: {},
      op: {
        approve_status: 'IN',
        work_status_id: 'IN',
        order_type: 'IN',
        mac_address: 'LIKE',
        sn_code: 'LIKE',
        pcba_remark: 'LIKE',
        sn_remark: 'LIKE',
      },
    },
    conf: {},
    chargeList: [],
    searchTimer: null,
    approveStatusSel: [1],
    workStatusSel: [],
    orderTypeSel: [1, 2],
    orderDateRange: [],
    backDateRange: [],
    deliveryDateRange: [],
    showMaterial: false, // 决定是否在列表中显示上线信息,
    visiblePopover: false,
    columnVisiblePopover: false,
    showDialog: false,
    dialogTitle: '新增平台',
    dialogWidth: 500,
    formEditFor: '',
    syncForm: {
      filter: {
        zzhcreatedt: null,
        wxcompanycode: 'IS NOT NULL',
      },
      op: {
        zzhcreatedt: 'DATETIME',
      },
    },
    syncFormRules: {
      zzhcreatedt: [
        {
          validator: validateDateRange,
          trigger: 'blur',
        },
      ],
    },
    syncOrderLoading: false,
    completeStatus: [
      { value: 0, label: '未完善' },
      { value: 1, label: '已完善' },
    ],
    workTagSel: [],
    workTagStatus: [],
    workTagProps: {
      multiple: true,
      value: 'id',
      label: 'name',
      expandTrigger: 'hover',
      emitPath: true,
    },
    //角色
    auth_role: {
      assembleRole: ['Assemble'],
      adminRole: ['Admin'],
      snMacRole: ['SnMacAssign'],
      warehouseRole: ['Warehouse'],
    },
    //列设置
    columnAllChecked: false,
    columnIndeterminate: false,
    //列表
    showElTable: false,
    tableHeight: 500,
    list: [],
    listLoading: true,
    layout: 'total, sizes, prev, pager, next, jumper',
    total: 0,
    columns: [],
    listSort: { prop: '', order: 'descending' },
    //详情
    selectRowId: 0,
    drawerDialogVisible: false,
    //导出
    exportForm: {
      filter: {
        'assemble_order.id': 0,
      },
      op: {
        'assemble_order.id': 'in',
      },
    },
    selectRows: [],
  })

  //----------------------搜索--------------------------
  //公共配置
  const queryConf = async () => {
    const { data } = await conf()
    state.conf = cloneDeep(data)
  }
  const getChargeList = async () => {
    let params = { third: 'redmine' }
    const { data } = await getUserThirdList(params)
    state.chargeList = Object.assign([], data)
  }
  //立即查询
  const queryData = async () => {
    state.queryForm.pageNo = 1
    fetchData()
  }
  //延迟查询
  const queryDataByTimer = () => {
    if (state.searchTimer) {
      clearTimeout(state.searchTimer)
      state.searchTimer = null
    }
    state.searchTimer = setTimeout(() => {
      state.queryForm.pageNo = 1
      fetchData()
    }, 800)
  }
  /**
   * 改变订单日期
   */
  const changeDate = (type) => {
    let dateRange = null
    if (type === 'order_date') {
      dateRange = state.orderDateRange
    } else if (type === 'first_back_date') {
      dateRange = state.backDateRange
    } else if (type === 'delivery_date') {
      dateRange = state.deliveryDateRange
    }
    if (
      typeof dateRange === 'object' &&
      dateRange != null &&
      dateRange.length > 0
    ) {
      dateRange = JSON.stringify(dateRange)
      if (dateRange) {
        dateRange = JSON.parse(dateRange)
        state.queryForm.filter[type] = dateRange.join(' - ')
        state.queryForm.op[type] = 'DATETIME'
      }
    } else {
      state.queryForm.filter[type] = null
    }
    fetchData()
  }
  /**
   * 更多筛选
   * @param akey
   */
  const changeQueryFormTopShow = () => {
    state.visiblePopover = !state.visiblePopover
  }
  /**
   * 隐藏更多筛选
   * @param event
   */
  const handleMoreClick = async (event) => {
    if (state.visiblePopover) {
      state.visiblePopover = false
    }
    if (state.columnVisiblePopover) {
      state.columnVisiblePopover = false
    }
  }
  //列设置
  const showColumnForm = () => {
    state.columnVisiblePopover = !state.columnVisiblePopover
    if (state.columnVisiblePopover) {
      changeAllChecked()
    } else {
      filteredColumns.value = cloneDeep(state.columns)
    }
  }

  const onDragEnd = () => {}

  const changeColumnchecked = () => {
    changeAllChecked()
  }

  const changeAllChecked = () => {
    let checked = filteredColumns.value.filter((item) => {
      return item.fixed || item.show
    })
    state.columnAllChecked = checked.length === filteredColumns.value.length
    state.columnIndeterminate =
      checked.length > 0 && checked.length < filteredColumns.value.length
  }

  const handleAllChecked = (val) => {
    filteredColumns.value.forEach((item, index) => {
      if (!item.fixed) {
        item.show = val
      }
    })
    changeAllChecked()
  }

  /**
   * 恢复默认列
   */
  const handleResetColumn = () => {
    filteredColumns.value = cloneDeep(allColumns)
    changeAllChecked()
  }

  const handleSaveColumn = () => {
    state.columnVisiblePopover = false
    localStorage.setItem(
      'assebleTableColumnSet' + user_id,
      JSON.stringify(filteredColumns.value)
    )
    state.columns = cloneDeep(filteredColumns.value)
  }

  /**
   * 重置条件参数
   */
  const handleRefreshOptions = async () => {
    await initOptions()
    fetchData()
  }
  /**
   * 替换当前url
   */
  const replaceUrl = async () => {
    const path = route.path
    let queryParams = ''
    for (const [key, value] of Object.entries(route.query)) {
      if (key && value) {
        queryParams += `${encodeURIComponent(key)}=${encodeURIComponent(
          value
        )}&`
      }
    }
    const newUrl = `#${path}?${queryParams}`
    window.history.replaceState(null, '', newUrl)
    await nextTick()
  }
  const initOptions = async () => {
    //重置时把url上的参数也消除
    if (route.query.stock_order_code) {
      delete route.query.stock_order_code
      await replaceUrl()
    }
    state.visiblePopover = false
    state.orderDateRange = null
    state.backDateRange = null
    state.deliveryDateRange = null
    state.approveStatusSel = [1]
    state.orderTypeSel = [1, 2]
    state.workStatusSel = []
    state.workTagSel = []
    state.workTagStatus = []
    state.queryForm.filter = {}
    state.queryForm.pageNo = 1
    state.queryForm.pageSize = 20
  }
  //跳转页
  const handleSizeChange = (val) => {
    state.queryForm.pageSize = val
    fetchData()
  }
  //页数据数
  const handleCurrentChange = (val) => {
    state.queryForm.pageNo = val
    fetchData()
  }

  /////////////////////弹窗/////////////////////////////
  const openSyncDialog = () => {
    if (state.syncOrderLoading) {
      $baseMessage('正在同步中，请勿重复操作', 'error', 'vab-hey-message-error')
      return
    }
    openDialog('editForSysnOrder')
  }
  const openDialog = async (editor) => {
    state.formEditFor = editor
    if (editor == 'editForSysnOrder') {
      state.dialogTitle = '选择日期范围'
      state.dialogWidth = 400
      syncCheckDate.value = [
        moment().startOf('day').format('YYYY-MM-DD'),
        moment().startOf('day').format('YYYY-MM-DD'),
      ]
    }
    await new Promise((resolve) => setTimeout(resolve, 200))
    state.showDialog = true
  }
  const closeDialog = () => {
    state.showDialog = false
  }
  const saveDialog = (editor) => {
    if (editor == 'editForSysnOrder') {
      syncOrderFormRef.value.validate(async (valid) => {
        if (valid) {
          closeDialog()
          state.syncOrderLoading = true
          await syncOrder(state.syncForm)
            .then((res) => {
              $baseMessage('同步成功', 'success', 'vab-hey-message-success')
              queryData()
            })
            .finally(() => {
              state.syncOrderLoading = false
            })
        }
      })
    }
  }

  //----------------------编辑----------------------------
  const showEdit = (row) => {
    state.editCustomerRemark = false
    if (row.id) {
      editRef.value.showEdit(row)
    } else {
      editRef.value.showEdit()
    }
  }
  //----------------------列表----------------------------
  //匹配配置文字
  const mapConfText = (type, value, filed = 'id', scope) => {
    if (
      type == 'work_status' &&
      value == 'finished' &&
      scope?.row.approve_status == 1
    ) {
      return '待完成'
    }
    return state.conf[type]?.find((item) => item[filed] === value)?.name || ''
  }
  const mapWorkStatusKey = (value) => {
    return state.conf.work_status.find((item) => item.id === value)?.key || ''
  }
  //齐料模式列
  const buildMateialColumn = async () => {
    const materialType = state.conf.material_type
    const baseCol = cloneDeep(baseColumns.value)
    let columns = []
    //固定列
    materialOption.value.fixedColumn.forEach((key) => {
      const colData = baseCol.find((item) => item.prop === key)
      if (colData) {
        colData.fixed = 'left'
        colData.show = true
        columns.push(colData)
      }
    })
    //齐料状态
    baseCol.forEach((item) => {
      if (item.aliasProp === 'material_status') {
        columns.push({ ...item, show: true })
      }
    })
    //后面列
    materialOption.value.endColumn.forEach((key) => {
      const colData = baseCol.find((item) => item.prop === key)
      if (colData) {
        colData.show = true
        columns.push(colData)
      }
    })
    materialColumns.value = columns
  }

  //编辑备注
  const editRemark = (row, type) => {
    if (row.id) {
      editRef.value.showEdit(row, type)
    }
  }
  //编辑齐料状态
  const handleSaveMaterialStatus = async (row, materialType) => {
    if (row.id && row['material_status_' + materialType]) {
      const data = {
        assemble_order_id: row.id,
        type: materialType,
        status: row['material_status_' + materialType],
      }
      await changeMaterialStatus(data)
      fetchData()
    }
  }
  //编辑工作状态
  const handleSaveWorkStatus = async (val, scope) => {
    scope.row.work_status_append_text = null
    if (scope.row.id) {
      const data = {
        assemble_order_id: scope.row.id,
        work_status_id: val,
      }
      await changeWorkStatus(data)
      fetchData()
    }
  }

  const fetchData = async () => {
    state.list = []
    state.listLoading = true
    await setWorkTagFilter()
    state.queryForm.filter['approve_status'] = state.approveStatusSel.join(',')
    state.queryForm.filter['order_type'] = state.orderTypeSel.join(',')
    const mergedWorkStatus = [
      ...new Set([...state.workStatusSel, ...state.workTagStatus]),
    ]
    state.queryForm.filter['work_status_id'] = mergedWorkStatus.join(',')
    const {
      data: { data, total },
    } = await getList(state.queryForm)
    state.list = data
    state.total = total
    state.listLoading = false
  }

  const setWorkTagFilter = async () => {
    state.workTagStatus = [...new Set(state.workTagSel.map((item) => item[0]))]
    const strings = state.workTagSel.map((item) => item[1])
    state.queryForm.filter['work_status_tag'] = strings.join(' or ')
  }

  /**
   * 列表排序规则变动
   * @param {*} param0
   */
  const handleSort = ({ column, prop, order }) => {
    state.listSort.prop = prop
    state.listSort.order = order

    state.queryForm.sort = prop
    let orderStr = ''
    if (order === 'ascending') {
      orderStr = 'ASC'
    } else if (order === 'descending') {
      orderStr = 'DESC'
    } else {
      state.queryForm.sort = null
    }
    state.queryForm.order = orderStr
    fetchData()
  }
  const doExport = async () => {
    if (state.selectRows.length === 0) return
    state.exportForm.filter['assemble_order.id'] = state.selectRows.join(',')
    await exportOrder(state.exportForm)
      .then((response) => {
        const blob = new Blob([response])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        const curday = moment().startOf('day').format('YYYY-MM-DD')
        link.setAttribute('download', '组装订单' + curday + '.xlsx') // 设置下载文件的名称
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url) // 释放内存
      })
      .catch((error) => {
        console.error('下载失败:', error)
      })
  }

  /**
   * 表格可勾选的选择项发生变化
   */
  const handleSelectionChange = (selection) => {
    state.selectRows = selection.map((item) => {
      return item.id
    })
  }

  const getColumnWidth = (prop, records, minWidth) => {
    const padding = 8 // 列内边距
    if (prop == 'work_status_tag') {
      const contentWidths = records.map((item) => {
        const value = item[prop] || []
        const textWidth = value.length * 78
        return textWidth + padding
      })
      let maxWidth = Math.max(...contentWidths)
      return maxWidth
    } else {
      const contentWidths = records.map((item) => {
        const value = item[prop] ? String(item[prop]) : ''
        const textWidth = getTextWidth(value)
        return textWidth + padding
      })

      let maxWidth = Math.max(...contentWidths)
      if (maxWidth > 256) {
        maxWidth = 256
      }
      return Math.max(minWidth, maxWidth)
    }
  }

  const getTextWidth = (text, fontSize = '16px') => {
    const span = document.createElement('span')
    span.style.visibility = 'hidden'
    span.style.position = 'absolute'
    span.style.top = '-9999px'
    span.style.whiteSpace = 'nowrap'
    span.style.fontSize = fontSize
    span.innerText = text
    document.body.appendChild(span)
    const width = span.offsetWidth
    document.body.removeChild(span)
    return width
  }

  const showRowComponent = (scope) => {
    scope.row.show_component = true
  }

  //---------------------详情-------------------------
  const handleEdit = (row) => {
    state.selectRowId = row.id
    state.drawerDialogVisible = true
  }
  const handleCloseDrawer = () => {
    state.drawerDialogVisible = false
    if (detailRef.value && detailRef.value.refreshListStatus) {
      fetchData()
    }
  }
  /**
   * 处理
   * @param {*} event
   */
  const handleClick = async (event) => {
    // 点击任意地方时如果显示有产品详情弹窗，则隐藏
    if (state.drawerDialogVisible) {
      handleCloseDrawer()
    }
  }
  //--------------------监听，订阅，发布-------------------
  watch(
    () => state.showMaterial,
    () => {
      localStorage.setItem(
        'assembleListShowType' + user_id,
        state.showMaterial ? 'show' : 'hide'
      )
      if (state.showMaterial) {
        state.columns = cloneDeep(materialColumns.value)
      } else {
        state.columns = cloneDeep(filteredColumns.value)
      }
    }
  )

  // watch(
  //   () => state.columnVisiblePopover,
  //   (newVal) => {
  //     console.log('columnVisiblePopover', newVal)
  //     if (!newVal) {
  //       localStorage.setItem(
  //         'assebleTableColumnSet' + user_id,
  //         JSON.stringify(filteredColumns.value)
  //       )
  //       state.columns = cloneDeep(filteredColumns.value)
  //       console.log('currentCol', state.columns)
  //     } else {
  //       changeAllChecked()
  //     }
  //   }
  // )

  //监听同步的日期范围
  watch(
    () => syncCheckDate.value,
    () => {
      if (
        typeof syncCheckDate.value == 'object' &&
        syncCheckDate.value != null &&
        syncCheckDate.value.length > 0
      ) {
        let check_date = JSON.stringify(syncCheckDate.value)
        if (check_date) {
          check_date = JSON.parse(check_date)
          state.syncForm.filter.zzhcreatedt = check_date.join(' - ')
          state.syncForm.op.zzhcreatedt = 'DATETIME'
        }
      } else {
        state.syncForm.filter.zzhcreatedt = null
      }
    },
    { deep: true }
  )
  const isSupper = computed(() => isHasSuperRole())
  const isWare = computed(() => isHasRole('warehouseRole'))

  const materialStatusIcon = computed(() => {
    return state.conf.material_status.reduce((acc, item) => {
      acc[item.id] = mapMaterialStatusIcon(item.id)
      return acc
    }, {})
  })
  const materialStatusEdit = computed(() => {
    return state.conf.approve_status.reduce((acc, item) => {
      acc[item.id] = !(isSupper.value || (isWare.value && item.id === 1))
      return acc
    }, {})
  })
  const setFilteredColumn = async (isInit = false) => {
    if (isProcessingFactory()) {
      // 加工厂角色直接使用过滤后的列，不使用本地存储
      filteredColumns.value = cloneDeep(baseColumns.value)
      return
    }

    let savedSettings = localStorage.getItem('assebleTableColumnSet' + user_id)
    if (savedSettings && !isInit) {
      savedSettings = JSON.parse(savedSettings)

      savedSettings = syncArrays(allColumns, savedSettings)

      filteredColumns.value = cloneDeep(savedSettings)
      return
    }

    // 如果没有保存的设置，使用默认列配置
    filteredColumns.value = cloneDeep(allColumns)
  }

  //同步数组
  const syncArrays = (Arr1, Arr2, prop = 'prop') => {
    //fixed放前面
    const fixedCol = cloneDeep(Arr1.filter((item) => item.fixed))
    Arr2 = cloneDeep(Arr2.filter((item) => item.fixed === undefined))
    fixedCol.reverse().forEach((item) => {
      Arr2.unshift(item)
    })
    // 创建 Arr2 中所有 id 的集合
    const arr2Props = new Set(Arr2.map((item) => item[prop]))

    // 找出需要添加到 arr2 的项
    const itemsToAdd = Arr1.filter((item) => !arr2Props.has(item[prop]))

    // 创建 Arr1 中所有 id 的集合
    const arr1Props = new Set(Arr1.map((item) => item[prop]))

    // 找出需要从 arr2 删除的项
    const itemsToRemove = Arr2.filter((item) => !arr1Props.has(item[prop]))

    // 将 arr1 中的新增项添加到 arr2
    itemsToAdd.forEach((newItem) => {
      const newItemProp = newItem[prop]
      const newItemIndexInArr1 = Arr1.findIndex(
        (item) => item[prop] === newItemProp
      )

      if (newItemIndexInArr1 > 0) {
        // 获取新项在 Arr1 中的前一个项的 prop
        const previousItemProp = Arr1[newItemIndexInArr1 - 1][prop]
        // 在 Arr2 中查找前一个项的 prop 的位置
        const previousItemIndexInArr2 = Arr2.findIndex(
          (item) => item[prop] === previousItemProp
        )

        if (previousItemIndexInArr2 !== -1) {
          // 如果找到了前一个项的位置，就在该位置后面插入新项
          Arr2.splice(previousItemIndexInArr2 + 1, 0, newItem)
        } else {
          // 如果在 Arr2 中没有找到前一个项的位置，就将新项添加到末尾
          Arr2.push(newItem)
        }
      } else {
        // 如果新项是 Arr1 中的第一个项，直接添加到 Arr2 的开头
        Arr2.unshift(newItem)
      }
    })

    // 从 arr2 删除不在 arr1 中的项
    itemsToRemove.forEach((item) => {
      const index = Arr2.findIndex((arrItem) => arrItem[prop] === item[prop])
      if (index !== -1) {
        Arr2.splice(index, 1)
      }
    })
    // 如果 Arr1 和 Arr2 中有相同项，更新 Arr2 中的属性
    const syncField = [
      'label',
      'width',
      'sortable',
      'align',
      'fixed',
      'type',
      'minWidth',
      'showOverflowTooltip',
    ]
    Arr1.forEach((item1) => {
      const item2 = Arr2.find((item) => item[prop] === item1[prop])
      if (item2) {
        syncField.forEach((key) => {
          if (item1[key] !== undefined) {
            item2[key] = item1[key]
          } else {
            delete item2[key]
          }
        })
      }
    })

    return Arr2 // 返回同步后的 arr2
  }

  /**
   * 列表插入齐料状态的多个属性
   */
  const updateBaseColumn = async () => {
    const materialType = state.conf.material_type
    const updatedColumns = allColumns
      .map((item) => {
        if (item.prop === 'material_status') {
          let columns = []
          // 找到 material_status 后，替换成 extraColumn 数组中的多个对象
          materialType.forEach((val) => {
            columns.push({
              prop: 'material_status_' + val.id,
              aliasProp: item.aliasProp,
              label: val.name + '齐料',
              align: item.align,
              width: item.width,
              sortable: item.sortable,
              template: item.template,
              material_type: val.id,
              show: item.show,
            })
          })
          return columns
        }
        return item
      })
      .flat()

    // 更新allColumns数组
    allColumns.splice(0, allColumns.length, ...updatedColumns)

    // 保持原来的默认设置逻辑
    filteredColumns.value = cloneDeep(allColumns)
  }
  //是否超级管理员和文总
  const isHasSuperRole = () => {
    return roleAuth(state.auth_role.adminRole) || username.value === '文晓东'
  }

  //是否拥有某个角色
  const isHasRole = (type) => {
    return roleAuth(state.auth_role[type])
  }

  const isHasEditRole = (row) => {
    if (isHasSuperRole()) {
      return true
    } else if (row.approve_status > 1) {
      return false
    } else if (
      isHasRole('assembleRole') ||
      isHasRole('snMacRole') ||
      isHasRole('warehouseRole')
    ) {
      return true
    }
    return false
  }

  const setRouteFilter = async () => {
    if (route.query.stock_order_code) {
      state.queryForm.filter.stock_order_code = route.query.stock_order_code
      state.approveStatusSel = [1, 2, 3]
    }
  }

  onMounted(async () => {
    state.showElTable = true
    state.tableHeight = document.documentElement.clientHeight - 180
    await queryConf()
    await updateBaseColumn()
    await buildMateialColumn()
    await setFilteredColumn()
    // 根据角色设置初始列
    if (isProcessingFactory()) {
      state.columns = cloneDeep(baseColumns.value)
    } else {
      state.columns = cloneDeep(filteredColumns.value)
    }
    state.showMaterial = defaultShowMaterialList
      ? defaultShowMaterialList == 'show'
        ? true
        : false
      : false
    getChargeList()
    await setRouteFilter()
    fetchData()
    document
      .querySelector('.modal-product-info')
      .addEventListener('click', handleClick)
    //添加更多筛选点击隐藏事件
    fullscreenContainer.value.addEventListener('click', handleMoreClick)
  })
  /**
   * 本页面消毁时
   */
  onBeforeUnmount(() => {
    document
      .querySelector('.modal-product-info')
      .removeEventListener('click', handleClick)
    fullscreenContainer.value.removeEventListener('click', handleMoreClick)
  })
</script>
<style lang="scss" scoped>
  .column-set-bottom {
    justify-content: space-between;
    height: 40px;
    background: #fff;
    border: 1px solid #e8e9eb;
    border-radius: 0px 0px 6px 6px;
    box-shadow: 0px -4px 4px 0px rgba(51, 51, 51, 0.05);
  }
  .move-box-icon {
    display: none;
    align-items: center;
    justify-content: flex-end;
    height: 32px;
  }

  .chosenClass {
    .move-box-icon {
      display: flex;
    }
  }
  .edit-key-icon {
    color: #ff5e4b;
  }
  .text-icon {
    width: 24px;
    height: 24px;
    margin-right: 0 !important;
  }
  .empty-remark {
    color: #999999;
  }
  .over-flow {
    cursor: pointer;
  }
  .notes-box {
    max-height: 850px;
    overflow: auto;
    white-space: break-spaces;
  }
  .button-icon {
    width: 20px;
    height: 20px;
  }
  :deep() {
    .input-width {
      width: 100% !important;
    }
    .order-list-table {
      .el-checkbox__inner {
        width: 16px;
        height: 16px;
      }
      .el-table__header.el-table__cell {
        padding: 8px 0;
      }
      th {
        .cell {
          height: 20px;
          padding: 0px 8px;
          color: #666 !important;
        }
      }
      td {
        padding: 4px 0;
        .cell {
          padding: 0px 8px;
          color: #333 !important;
        }
      }
      .el-select .el-input__inner {
        color: #333 !important;
      }
    }
    .tag-select {
      .el-input__inner {
        visibility: hidden;
      }
      .tag-selection-item {
        height: 34px;
      }
    }
    .icon-select {
      .el-input {
        align-content: center;
      }
      .el-input__wrapper {
        flex-grow: 0;
      }
      .el-input__inner {
        width: 0px;
        color: #333;
        visibility: hidden;
      }
    }
    .only-show-icon-selection {
      .el-input__wrapper {
        box-shadow: 0 0 0 0px
          var(--el-input-border-color, var(--el-border-color)) inset;

        .el-input__suffix {
          display: none;
        }
        .el-input__inner {
          display: none;
        }

        &:hover {
          .el-input__inner {
            display: inline-flex;
          }
          .el-input__suffix {
            display: inline-flex;
          }
          box-shadow: 0 0 0 1px
            var(--el-input-border-color, var(--el-border-color)) inset;
          // padding-left: 12px;
        }
        &:focus {
          box-shadow: 0 0 0 1px var(--el-select-input-focus-border-color) inset !important;
        }
      }
      .el-input__wrapper.is-focus {
        .el-input__inner {
          display: inline-flex;
        }
        .el-input__suffix {
          display: inline-flex;
        }
        box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
      }
    }
    .el-checkbox-group {
      label:first-child {
        .el-checkbox-button__inner {
          border-radius: 6px 0 0 6px;
        }
      }

      label:last-child {
        .el-checkbox-button__inner {
          border-radius: 0px 6px 6px 0px;
        }
      }
    }
  }
  .item {
    padding-left: 8px;
    margin-right: 16px;
    margin-bottom: 0px !important;
    cursor: move;
    .el-checkbox {
      font-weight: 400;
      color: #333;
    }
  }
  .item:hover {
    cursor: move;
    background-color: rgba(15, 84, 255, 0.05);
  }
  .hoverable-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 10px;
    font-size: 15px;
    cursor: pointer;

    /* 初始背景颜色 */
    background-color: white;
    border-radius: 6px;
    transition: background-color 0.3s;

    &:hover {
      color: $base-color-primary;
      background-color: #f0f0f0;
    }

    &.active {
      color: #3977f7; /* 点击后的字体颜色 */
    }

    .check-icon {
      margin-left: auto;
    }
  }
</style>
<style lang="scss">
  .order-textarea-input {
    .el-textarea__inner {
      border-radius: 6px;
    }
  }
  .production-drawer {
    .el-drawer__body {
      padding: 0px !important;
    }
  }
</style>
