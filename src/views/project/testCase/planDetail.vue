<template>
  <div class="test-plan-detail" style="height: calc(100vh - 60px)">
    <div class="container">
      <div class="container-top">
        <el-row class="align-items-center">
          <el-col :span="15">
            <el-row class="align-items-center">
              <vab-icon
                class="back-icon"
                icon="back_page"
                is-custom-svg
                @click="backPage"
              />
              <el-tooltip :content="state.form.title" placement="right">
                <span
                  class="order-description-title-text"
                  style="line-height: normal"
                >
                  {{ state.form.project_name }} - {{ state.form.title }}
                </span>
              </el-tooltip>

              <el-tag v-if="state.form.is_archived === 1" type="success">
                已归档
              </el-tag>
              <el-tag v-else-if="state.form.status === 1" type="info">
                未开始
              </el-tag>
              <el-tag v-else-if="state.form.status === 2" type="warning">
                进行中
              </el-tag>
              <el-tag v-else-if="state.form.status === 3" type="success">
                已完成
              </el-tag>
            </el-row>
          </el-col>
          <el-col :span="9" class="justify-content-end">
            <el-button
              type="primary"
              :disabled="state.form.is_archived ? true : false"
              @click="titleClick(state.firstRow)"
            >
              <vab-icon class="button-icon" icon="begin-test" is-custom-svg />
              开始测试
            </el-button>
            <el-button type="primary" @click="showTestReport(state.form)">
              测试报告
            </el-button>
            <el-button @click="showEdit()">
              <vab-icon class="button-icon" icon="edit" is-custom-svg />
              编辑
            </el-button>
            <el-dropdown style="margin-left: 8px" placement="bottom">
              <el-button>
                <vab-icon class="button-icon" icon="arrow_down" is-custom-svg />
                更多
              </el-button>
              <template #dropdown>
                <el-dropdown-menu style="width: 100px">
                  <el-dropdown-item @click="setIsArchived">
                    {{ state.form.is_archived === 1 ? '解除归档' : '归档' }}
                  </el-dropdown-item>
                  <el-dropdown-item @click="deletePlan">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
        </el-row>
        <el-row class="align-items-center plan-detail-text">
          <el-tooltip content="版本" placement="top">
            <span class="icon-text">
              <vab-icon icon="version" is-custom-svg class="plan-detail-icon" />
              <span>{{ state.form.version_name }}</span>
            </span>
          </el-tooltip>
          <el-tooltip content="测试人员" placement="top">
            <span class="icon-text">
              <vab-icon
                icon="assign_person"
                is-custom-svg
                class="plan-detail-icon"
              />
              <span>{{ state.form.test_user_name }}</span>
            </span>
          </el-tooltip>
          <el-tooltip content="测试周期" placement="top">
            <span class="icon-text">
              <vab-icon
                icon="calendar"
                is-custom-svg
                class="plan-detail-icon"
              />
              <span>
                {{
                  state.form.begin_date_text + '~' + state.form.end_date_text
                }}
              </span>
            </span>
          </el-tooltip>
          <el-tooltip content="展开/收起" placement="bottom">
            <span class="icon-text" style="margin-right: 0; margin-left: auto">
              <vab-icon
                :icon="
                  !state.showProgressBar ? 'expand_space' : 'collapse_space'
                "
                is-custom-svg
                class="plan-detail-icon"
                style="color: #666666; cursor: pointer"
                @click="expandOrCollaspeTopContainer()"
              />
            </span>
          </el-tooltip>
        </el-row>
        <el-row class="plan-detail-text" v-show="state.showProgressBar">
          <span style="color: #999">总结果：</span>
          <span style="padding-right: 8px">{{ state.form.pass_rate }}通过</span>
          <span style="padding-left: 20px; color: #999">已测用例：</span>
          <span>{{ state.form.test_num }}/{{ state.form.case_num }}</span>
        </el-row>
        <el-row v-show="state.showProgressBar">
          <div
            v-if="state.testProgress.length > 0"
            style="width: 760px; margin-left: 50px"
          >
            <TestPlanProgressBar
              :progress-data="state.testProgress"
              :height="20"
              :show-count="true"
            />
          </div>
        </el-row>
      </div>
      <div class="container-button">
        <el-radio-group v-model="radio" v-if="!isProductMode">
          <el-radio-button label="测试用例" value="测试用例" />
          <el-radio-button label="相关事项" value="相关事项" />
        </el-radio-group>
      </div>
      <div class="container-content">
        <div v-if="radio === '测试用例'">
          <el-form
            ref="queryFormRef"
            :inline="true"
            :model="state.queryForm"
            @submit.prevent
          >
            <vab-query-form>
              <vab-query-form-left-panel :span="17">
                <el-form-item>
                  <el-input
                    v-model.trim="state.caseQueryForm.filter['test_case.title']"
                    clearable
                    placeholder="请输入标题搜索"
                    @input="queryDataByTimer"
                  />
                </el-form-item>
                <el-form-item>
                  <el-select
                    v-model="state.assignUserSel"
                    placeholder="选择测试人员"
                    filterable
                    clearable
                    multiple
                    remote
                    collapse-tags
                    style="width: 182px"
                    @change="queryDataByTimer()"
                  >
                    <el-option
                      v-for="item in state.assignOptions"
                      :key="item.user_id"
                      :label="item.user.name"
                      :value="item.user_id"
                    >
                      <div class="flex-algin-center">
                        <img
                          :src="item.user.thumb_avatar"
                          class="assigned-avatar"
                        />
                        {{ item.user.name }}
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
                <!-- <el-form-item>
                  <IssuePriority
                    v-if="state.enumerationList.length > 0"
                    v-model:array-value="state.priorityIdsSel"
                    border
                    clearable
                    style="
                      width: 180px;
                      border: 1px solid var(--el-border-color);
                    "
                    :option="state.enumerationList"
                    @change="queryDataByTimer"
                    :multiple="true"
                  />
                </el-form-item> -->
                <el-form-item>
                  <!-- <el-select
                    v-model="state.priorityIdsSel"
                    placeholder="优先级"
                    remote
                    collapse-tags
                    style="width: 100%"
                    clearable
                    :multiple="true"
                    @change="queryDataByTimer"
                  >
                    <el-option
                      v-for="item in issueOption"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                      <div class="flex-algin-center" style="height: 34px">
                        <el-tag :type="item.type">{{ item.label }}</el-tag>
                      </div>
                    </el-option>
                  </el-select> -->
                  <IssuePriority
                    v-model:array-value="state.priorityIdsSel"
                    border
                    clearable
                    style="
                      width: 194px;
                      border: 1px solid var(--el-border-color);
                    "
                    @change="queryDataByTimer"
                    :multiple="true"
                  />
                </el-form-item>
                <el-form-item>
                  <el-select
                    v-model="state.caseTestStatusSel"
                    placeholder="测试状态"
                    remote
                    collapse-tags
                    clearable
                    style="width: 204px"
                    :multiple="true"
                    @change="queryDataByTimer"
                  >
                    <el-option
                      v-for="item in testStatusOption"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                      <div class="flex-algin-center">
                        <vab-icon
                          style="width: 24px; height: 24px"
                          :icon="item.icon"
                          :color="item.color"
                          is-custom-svg
                        />
                        {{ item.label }}
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
              </vab-query-form-left-panel>
              <vab-query-form-right-panel :span="7">
                <el-form-item>
                  <el-button
                    :disabled="state.form.is_archived ? true : false"
                    @click="showCaseSelect()"
                  >
                    <vab-icon
                      style="width: 24px; height: 24px"
                      class="button-icon"
                      icon="add"
                      is-custom-svg
                    />
                    新增用例
                  </el-button>
                </el-form-item>
                <el-form-item>
                  <el-dropdown
                    placement="bottom-end"
                    :disabled="state.selectRows.length == 0"
                  >
                    <!-- <el-tooltip
                      :disabled="state.selectRows.length > 0"
                      content="请先选择至少一行数据"
                      placement="top"
                    > -->
                    <!-- <template #default> -->
                    <div>
                      <el-button
                        style="margin-right: 0px !important"
                        :disabled="state.selectRows.length === 0"
                      >
                        <vab-icon
                          class="button-icon"
                          icon="batch"
                          is-custom-svg
                          style="width: 24px; height: 24px"
                        />
                        批量操作
                      </el-button>
                    </div>
                    <!-- </template> -->
                    <!-- </el-tooltip> -->
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          :disabled="state.form.is_archived ? true : false"
                        >
                          <el-popover
                            class="dropdown-popover"
                            trigger="hover"
                            placement="left-start"
                            :disabled="state.form.is_archived ? true : false"
                          >
                            <div style="margin: 0; text-align: right">
                              <div
                                v-for="item in filterTestStatusOption"
                                :key="item.value"
                                class="dropdown-select-item"
                                @click="doMultiEdit('test_status', item.value)"
                              >
                                <div class="flex-algin-center">
                                  <vab-icon
                                    :icon="mapIconFun(item.value)"
                                    :color="mapColorFun(item.value)"
                                    is-custom-svg
                                  />
                                  {{ item.label }}
                                </div>
                              </div>
                            </div>
                            <template #reference>
                              <span
                                style="
                                  display: inline-flex;
                                  align-items: center;
                                  justify-content: space-between;
                                  width: 100%;
                                "
                              >
                                <span>设置测试结果</span>
                                <vab-icon icon="arrow_right" is-custom-svg />
                              </span>
                            </template>
                          </el-popover>
                        </el-dropdown-item>
                        <el-dropdown-item
                          :disabled="state.form.is_archived ? true : false"
                        >
                          <el-popover
                            class="dropdown-popover"
                            trigger="hover"
                            placement="left-start"
                            :disabled="state.form.is_archived ? true : false"
                          >
                            <div style="height: 274px; overflow-y: auto">
                              <div
                                v-for="item in state.assignOptions"
                                :key="item.user_id"
                                :label="item.user.name"
                                :value="item.user_id"
                                @click="
                                  doMultiEdit('assigned_user', item.user_id)
                                "
                                class="dropdown-select-item"
                              >
                                <div class="flex-algin-center">
                                  <img
                                    :src="item.user.thumb_avatar"
                                    class="assigned-avatar"
                                  />
                                  {{ item.user.name }}
                                </div>
                              </div>
                            </div>
                            <template #reference>
                              <span
                                style="
                                  display: inline-flex;
                                  align-items: center;
                                  justify-content: space-between;
                                  width: 100%;
                                "
                              >
                                <span>分配人</span>
                                <vab-icon icon="arrow_right" is-custom-svg />
                              </span>
                            </template>
                          </el-popover>
                        </el-dropdown-item>
                        <el-dropdown-item
                          :disabled="state.form.is_archived ? true : false"
                          @click="openDialog('moveToPlan')"
                        >
                          移动到其他计划
                        </el-dropdown-item>
                        <el-dropdown-item @click="doExport()">
                          导出{{
                            state.selectRows.length > 0 ? '选中' : '所有'
                          }}用例
                        </el-dropdown-item>
                        <el-dropdown-item
                          :disabled="state.selectRows.length === 0"
                          @click="detachCase()"
                          :style="
                            state.selectRows.length === 0
                              ? {}
                              : { color: '#ff5e4b' }
                          "
                        >
                          移除选中用例
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </el-form-item>
                <el-form-item>
                  <el-tooltip content="用例一键展开/收起" placement="top">
                    <el-button style="width: 32px" @click="changeExpand()">
                      <vab-icon
                        style="margin-left: 3px"
                        icon="expand_up_down"
                        is-custom-svg
                      />
                    </el-button>
                  </el-tooltip>
                </el-form-item>
              </vab-query-form-right-panel>
              <el-collapse-transition v-if="state.isShowUnit">
                <vab-query-form-bottom-panel
                  ref="unitRef"
                  id="vab-query-form-bottom-panel-issue-list"
                  class="custom-bottom-panel-style"
                  :style="{ 'margin-bottom': '0px' }"
                >
                  <el-form inline style="display: flex; flex-wrap: wrap">
                    <div v-for="tagItem in state.unitList" :key="tagItem.name">
                      <el-form-item v-if="tagItem.cases_count > 0">
                        <el-button
                          :style="{
                            color: isSelectedTag(tagItem)
                              ? '#3977F3'
                              : '#999999',
                            borderColor: isSelectedTag(tagItem)
                              ? '#3977F3'
                              : '#e4e7ed',
                          }"
                          @click="toggleSelectionTag(tagItem)"
                        >
                          <!-- @click="queryDataSearch('search_category', tagItem.id)" -->

                          {{ tagItem.name }}&nbsp;•&nbsp;{{
                            tagItem.cases_count ?? 0
                          }}
                        </el-button>
                      </el-form-item>
                    </div>
                  </el-form>
                </vab-query-form-bottom-panel>
              </el-collapse-transition>
            </vab-query-form>
          </el-form>
          <VxeTable
            :border="false"
            show-overflow
            round
            :height="state.tableHeight"
            ref="tableRef"
            v-loading="state.listLoading"
            :scroll-y="scrollYConfig"
            :data="state.caseTreeList"
            :header-cell-style="{ height: '36px' }"
            @checkbox-change="handleSelectionChange"
            @checkbox-all="selectAllChangeEvent"
            :column-config="{ resizable: true }"
            @scroll="scrollEvent"
            :row-class-name="tableRowClassName"
            :tree-config="{
              transform: true,
              rowField: 'key_id',
              parentField: 'parent_id',
              expandAll: true,
              reserve: true,
              iconOpen: 'vxe-icon-arrow-down',
              iconClose: 'vxe-icon-arrow-right',
            }"
            :row-config="{
              keyField: 'key_id',
            }"
            :cell-config="{
              height: 32, // 加上内边距，整体为45px了
            }"
          >
            <!-- 选择框列 -->
            <vxe-column type="checkbox" width="40" />

            <template v-for="(column, index) in caseColumn" :key="column.prop">
              <vxe-column
                :tree-node="column.prop == 'title'"
                :key="index"
                :type="column.type"
                v-if="!column.slot"
                :title="column.label"
                :align="column.align"
                :field="column.prop"
                :fixed="column.fixed"
                :width="column.width"
                :sortable="column.sortable"
                :min-width="column.minWidth"
                :show-overflow="column.showOverflowTooltip ? 'tooltip' : ''"
                :class-name="
                  column.prop === 'title' ? 'order-number-cell' : 'other-cell'
                "
              >
                <template v-if="column.template" #default="scope">
                  <template
                    v-if="
                      column.prop === 'priority' &&
                      typeof scope.row.priority != 'undefined'
                    "
                  >
                    <!-- <IssuePriority
                      v-if="state.enumerationList.length > 0"
                      v-model:value="scope.row.priority"
                      :option="state.enumerationList"
                      :disabled="state.form.is_archived"
                      @change="handleSave(scope.row, 'priority')"
                    /> -->
                    <div
                      v-if="showPriorityComponent || !scope.row.show_component"
                      @mouseover="testStatusMouseover(scope)"
                    >
                      <el-tag
                        disable-transitions
                        :type="priorityTagType[scope.row.priority]"
                        style="min-width: 44px; margin-left: 11px; border: none"
                      >
                        <span style="font-weight: bold">
                          {{ priorityTagName[scope.row.priority] }}
                        </span>
                      </el-tag>
                    </div>

                    <el-select
                      v-else-if="scope.row.show_component"
                      :class="[
                        'priority-tag-of-plan-detail-list',
                        'none-border-on-common',
                        'm-2',
                        'tag-select',
                        state.form.is_archived
                          ? 'no-background-disabled-selection'
                          : '',
                      ]"
                      v-model="scope.row.priority"
                      placeholder=""
                      remote
                      collapse-tags
                      style="width: 100%"
                      :disabled="state.form.is_archived ? true : false"
                      @change="handleSave(scope.row, 'priority')"
                    >
                      <template #prefix>
                        <el-tag
                          disable-transitions
                          :type="priorityTagType[scope.row.priority]"
                          style="min-width: 44px; border: none"
                        >
                          <span style="font-weight: bold">
                            {{ priorityTagName[scope.row.priority] }}
                          </span>
                        </el-tag>
                      </template>
                      <el-option
                        v-for="item in issueOption"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                        <div class="flex-algin-center tag-selection-item">
                          <el-tag
                            :type="item.type"
                            style="
                              min-width: 44px;
                              margin-top: 5px;
                              border: none;
                            "
                          >
                            <span style="font-weight: bold">
                              {{ item.label }}
                            </span>
                          </el-tag>
                        </div>
                      </el-option>
                    </el-select>
                  </template>
                  <template
                    v-if="
                      column.prop === 'title' &&
                      typeof scope.row.title != 'undefined'
                    "
                  >
                    <div
                      :style="
                        scope.row.type == 'case' ? { cursor: 'pointer' } : {}
                      "
                      @click="caseTitleClick(scope)"
                    >
                      <vab-icon
                        v-if="scope.row.type == 'directory'"
                        :icon="'icon-dir'"
                        is-custom-svg
                        :style="{
                          width: 16 + 'px',
                          height: 16 + 'px',
                          marginRight: '6px',
                        }"
                      />
                      <vab-icon
                        v-else-if="scope.row.type == 'library'"
                        icon="version"
                        is-custom-svg
                        style="
                          width: 24px;
                          height: 24px;
                          margin-right: 1px;
                          margin-left: -3px;
                        "
                      />
                      <vab-icon
                        v-else-if="scope.row.type == 'case'"
                        icon="feedback"
                        is-custom-svg
                        style="
                          width: 24px;
                          height: 24px;
                          margin-right: 1px;
                          margin-left: -3px;
                          color: #666666;
                        "
                      />
                      <span>
                        {{ scope.row.title }}
                      </span>
                      <el-tag
                        type="info"
                        class="module-name"
                        v-if="scope.row.category_text?.id"
                        style="margin-left: 8px"
                      >
                        {{ scope.row.category_text?.name }}
                      </el-tag>
                    </div>
                  </template>

                  <template
                    v-if="
                      column.prop === 'assigned_user' &&
                      typeof scope.row.assigned_user != 'undefined'
                    "
                  >
                    <div
                      @mouseover="testStatusMouseover(scope)"
                      v-if="showAssignerComponent || !scope.row.show_component"
                      style="display: flex; margin-left: 11px"
                    >
                      <img
                        v-if="
                          typeof scope.row.assigned_user != 'undefined' &&
                          scope.row.assigned_user > 0
                        "
                        :src="
                          state.assignOptions.find(
                            (item) => item.user_id === scope.row.assigned_user
                          )?.user?.thumb_avatar
                        "
                        style="display: block; margin-right: 8px"
                        class="assigned-avatar-normal"
                      />
                      <span>
                        {{
                          state.assignOptions.find(
                            (item) => item.user_id == scope.row.assigned_user
                          )?.user?.name ?? '&nbsp;&nbsp;-'
                        }}
                      </span>
                    </div>
                    <el-select
                      v-else-if="scope.row.show_component"
                      :class="[
                        'none-border-on-common',
                        'm-2',
                        state.form.is_archived
                          ? 'no-background-disabled-selection'
                          : '',
                      ]"
                      v-model="scope.row.assigned_user"
                      placeholder="-"
                      remote
                      collapse-tags
                      style="width: 100%"
                      :disabled="state.form.is_archived ? true : false"
                      @change="handleSave(scope.row, 'assigned_user')"
                    >
                      <template #prefix>
                        <img
                          v-if="
                            typeof scope.row.assigned_user != 'undefined' &&
                            scope.row.assigned_user > 0
                          "
                          :src="
                            state.assignOptions.find(
                              (item) => item.user_id === scope.row.assigned_user
                            )?.user?.thumb_avatar
                          "
                          style="display: block"
                          class="assigned-avatar-normal"
                        />
                      </template>
                      <el-option
                        v-for="item in state.assignOptions"
                        :key="item.user_id"
                        :label="item.user.name"
                        :value="item.user_id"
                      >
                        <div class="flex-algin-center">
                          <img
                            :src="item.user.thumb_avatar"
                            class="assigned-avatar"
                          />
                          {{ item.user.name }}
                        </div>
                      </el-option>
                    </el-select>
                  </template>
                  <template
                    v-if="
                      column.prop === 'test_status' &&
                      typeof scope.row.test_status != 'undefined'
                    "
                  >
                    <div
                      v-if="isScrolling || !scope.row.show_component"
                      style="margin-left: 11px"
                      @mouseover="testStatusMouseover(scope)"
                    >
                      <div class="flex-algin-center">
                        <vab-icon
                          style="width: 24px; height: 24px; margin-right: 2px"
                          :icon="mapIconFun(scope.row.test_status)"
                          :color="mapColorFun(scope.row.test_status)"
                          is-custom-svg
                        />
                        {{
                          testStatusOption.find(
                            (item) => item.value == scope.row.test_status
                          )?.label
                        }}
                      </div>
                    </div>
                    <el-select
                      v-else-if="scope.row.show_component"
                      :class="[
                        'none-border-on-common',
                        'm-2',
                        state.form.is_archived
                          ? 'no-background-disabled-selection'
                          : '',
                      ]"
                      v-model="scope.row.test_status"
                      placeholder=""
                      remote
                      collapse-tags
                      style="width: 100%"
                      :disabled="state.form.is_archived ? true : false"
                      @change="handleSave(scope.row, 'test_status')"
                    >
                      <template #prefix>
                        <vab-icon
                          style="width: 24px; height: 24px; margin-right: -6px"
                          :icon="mapIconFun(scope.row.test_status)"
                          :color="mapColorFun(scope.row.test_status)"
                          is-custom-svg
                        />
                      </template>
                      <el-option
                        v-for="item in testStatusOption"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      >
                        <div class="flex-algin-center">
                          <vab-icon
                            style="width: 24px; height: 24px"
                            :icon="item.icon"
                            :color="item.color"
                            is-custom-svg
                          />
                          {{ item.label }}
                        </div>
                      </el-option>
                    </el-select>
                  </template>
                </template>
              </vxe-column>
            </template>
          </VxeTable>

          <!-- <el-pagination
            background
            :current-page="state.caseQueryForm.pageNo"
            :layout="state.layout"
            :page-size="state.caseQueryForm.limit"
            :total="state.total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          /> -->
        </div>
        <!---------------------相关事项---------------------->
        <div v-else>
          <el-form
            ref="queryFormRef"
            :inline="true"
            :model="state.queryForm"
            @submit.prevent
          >
            <vab-query-form>
              <vab-query-form-left-panel :span="20">
                <el-form-item>
                  <el-input
                    v-model="state.issueKeyWord"
                    placeholder="请输入标题搜索"
                    clearable
                    @input="queryIssueData"
                    style="width: 160px"
                  />
                </el-form-item>
                <el-form-item>
                  <el-select
                    v-model="state.assignUserIssueSel"
                    placeholder="选择处理人"
                    filterable
                    clearable
                    multiple
                    remote
                    collapse-tags
                    style="width: 180px"
                    @change="queryIssueData"
                  >
                    <el-option
                      v-for="item in state.assignOptionsForIssueList"
                      :key="item.user_id"
                      :label="item.user.name"
                      :value="item.user_id"
                    >
                      <div class="flex-algin-center">
                        <img
                          :src="item.user.thumb_avatar"
                          class="assigned-avatar"
                        />
                        {{ item.user.name }}
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <IssuePriority
                    v-if="state.enumerationList.length > 0"
                    v-model:array-value="state.priorityIdsIssueSel"
                    border
                    clearable
                    style="
                      width: 180px;
                      border: 1px solid var(--el-border-color);
                    "
                    :option="state.enumerationList"
                    @change="queryIssueData"
                    :multiple="true"
                  />
                </el-form-item>
              </vab-query-form-left-panel>
              <vab-query-form-right-panel :span="4">
                <el-form-item />
              </vab-query-form-right-panel>
            </vab-query-form>
          </el-form>
          <issue-list
            v-if="state.issueQueryForm.filter.plan_id"
            :query-form-filter="state.issueQueryForm.filter"
            :query-form-op="state.issueQueryForm.op"
            :show-cloumn="state.showFloumn"
            :show-query="false"
            :is-issue-my="true"
            :page-size="20"
            :pagination="true"
            :border="false"
            :height="state.tableHeight"
            :memory-status="false"
            :show-public="false"
            :is-test-plan-use="true"
            @refresh="reloadData"
            @project-member-got="handleAllProjectMembers"
          />
        </div>
      </div>
    </div>
    <TestPlanEdit
      ref="editRef"
      :project-id="state.projectId"
      :product-id="state.productId"
      :is-product-mode="isProductMode"
    />
    <firefly-dialog
      v-model="state.showDialog"
      :title="state.dialogTitle"
      :width="state.dialogWidth"
      @close="closeDialog()"
      @confirm="saveDialog(state.formEditFor)"
      show-default-button
      :footer-class="'foot-btn-bar-small-padding'"
    >
      <div v-if="state.formEditFor == 'moveToPlan'">
        <el-form
          ref="planSelectFormRef"
          label-width="80px"
          :model="state.planSelectForm"
          :rules="state.planSelectFormRule"
          label-position="top"
        >
          <el-form-item>
            {{ '已选择' + (state.selectRows.length || 0) + '条用例' }}
          </el-form-item>

          <el-form-item label="" prop="test_plan_id">
            <el-select
              style="width: 400px"
              v-model="state.planSelectForm.test_plan_id"
              placeholder="请选择计划"
              filterable
              remote
              fit-input-width
              :remote-method="remotePlanList"
              :loading="state.planLoading"
              class="input-width"
            >
              <el-option
                v-for="p in state.planOption"
                :key="p.id"
                :label="p.title"
                :value="p.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </firefly-dialog>
    <TestCaseSelect
      ref="caseSelectRef"
      v-model:checked-list="state.addCaseList"
      :project-id="isProductMode ? -1 : state.projectId"
      :case-tree-flattern-list="state.caseTreeList"
      @handle-save="addPlanCase"
    />
    <TestCaseDialog
      ref="testCaseDialogRef"
      :height="600"
      :open-type="'dialog'"
      @update-row="updateRow"
      :is-product-mode="isProductMode"
    />
    <TestPlanReport ref="testPlanReportRef" />
  </div>
</template>
<script setup>
  import {
    conf,
    getList as getPlanList,
    overView,
    doEdit as doPlanEdit,
    doDelete as doPlanDelete,
    addCase,
  } from '@/api/testPlan'
  import {
    doEdit as doPlanCaselEdit,
    getAllList as getPlanCase,
    getIssueList,
    doMulti as doPlanCaseMulti,
    doDelete as doDeletePlanCase,
    exportCase,
  } from '@/api/testPlanCase'

  import { getCategoryList, doEdit as doCaselEdit } from '@/api/testCase'

  import {
    Plus as PlusIcon,
    Refresh as RefreshIcon,
  } from '@element-plus/icons-vue'

  import TestCaseDialog from './components/TestCaseDialog.vue'
  import CommonEmpty from '~/src/components/CommonEmpty.vue'
  import IssueList from '@/views/project/issue/components/List.vue'
  import { roleAuth } from '@/utils/auth'
  import { useUserStore } from '@/store/modules/user'
  import CommonIcon from '~/src/components/CommonIcon.vue'

  import TestPlanProgressBar from './components/TestPlanProgressBar.vue'
  import TestCaseSelect from './components/TestCaseSelect.vue'
  import TestPlanReport from './components/TestPlanReport.vue'
  import TestResultCreate from './components/TestResultCreate.vue'
  import { getMemberList, getEnumerationList } from '@/api/projectIssue'
  import IssuePriority from '@/components/IssuePriority.vue'
  import TestPlanEdit from './components/TestPlanEdit.vue'
  import { cloneDeep, debounce, over, throttle } from 'lodash'
  import {
    testStatus,
    filterStatus,
    testStatusIcon,
    testStatusColor,
    testStatusOption,
    filterTestStatusOption,
    mapColorFun,
    mapIconFun,
  } from '@/json/testPlan'
  import {
    issueStatusClass,
    priorityIcon,
    priorityTagName,
    priorityTagType,
    issueOption,
  } from '@/json/issues'
  const $baseConfirm = inject('$baseConfirm')
  const $baseMessage = inject('$baseMessage')
  const $pub = inject('$pub')
  const $sub = inject('$sub')
  const $unsub = inject('$unsub')

  const route = useRoute()
  const router = useRouter()
  const userStore = useUserStore()
  const { token, redmine, user_settings, user_id } = storeToRefs(userStore)

  const emits = defineEmits(['handleSave'])

  const isScrolling = ref(false)
  const showAssignerComponent = ref(false)
  const showPriorityComponent = ref(false)

  const scrollEvent = (e) => {
    isScrolling.value = true
    showAssignerComponent.value = true
    showPriorityComponent.value = true
    scrollTimeoutOver(e)
  }
  const scrollTimeoutOver = _.debounce((e) => {
    // console.log(e)
    state.caseTreeList.forEach((item) => {
      item.show_component = false
    })
    isScrolling.value = false
    handleShowPriorityComponent()
  }, 288)
  const handleShowPriorityComponent = _.debounce(() => {
    showPriorityComponent.value = false
    handleShowAssignerComponent()
  }, 200)
  const handleShowAssignerComponent = _.debounce(() => {
    showAssignerComponent.value = false
  }, 200)

  const testStatusMouseover = (scope) => {
    scope.row.show_component = true
  }

  const tableRef = ref(null)
  const containerTopHeight = ref('172px')

  const radio = ref('测试用例')
  const editRef = ref(null)
  const planSelectFormRef = ref(null)
  const caseSelectRef = ref(null)
  const testCaseDialogRef = ref(null)
  const testPlanReportRef = ref(null)
  const caseColumn = ref([
    {
      prop: 'title',
      label: '用例标题',
      align: 'left',
      minWidth: 220,
      template: true,
      showOverflowTooltip: true,
    },
    {
      prop: 'test_status',
      label: '测试结果',
      align: 'left',
      width: 140,
      template: true,
    },
    {
      prop: 'priority',
      label: '优先级',
      align: 'left',
      width: 120,
      template: true,
    },
    {
      prop: 'assigned_user',
      label: '分配人',
      align: 'left',
      width: 200,
      template: true,
    },
    {
      prop: 'assigned_at',
      label: '分配时间',
      align: 'left',
      width: 150,
    },
    {
      prop: 'execution_count',
      label: '执行次数',
      align: 'left',
      width: 120,
    },
    {
      prop: 'issues_count',
      label: '关联事项数',
      align: 'left',
      width: 120,
    },
  ])
  const state = reactive({
    assignOptionsForIssueList: [], // 多个项目的成员列表集合
    selectedTags: [],
    unitList: [], // 分类（模块）列表
    isShowUnit: false, // 模块是否展示
    showProgressBar: true,
    expandAllRow: false,
    total: 100,
    layout: 'total, sizes, prev, pager, next',

    projectId: Number(route.query.project_id),
    planId: Number(route.query.plan_id),
    form: {},
    caseList: [],
    caseTreeList: [],
    flaternTreeListButOnlyCase: [],
    issuesList: [],
    listLoading: false,
    caseQueryForm: {
      filter: {},
      op: {
        'test_case.title': 'like',
        'test_case.priority': 'in',
        'test_plan_case.test_status': 'in',
        'test_plan_case.assigned_user': 'in',
      },
      order: 'desc',
      pageNo: 1,
      limit: 99999,
    },
    issueQueryForm: {
      filter: {
        plan_id: route.query.plan_id,
        // project_id: route.query.project_id,
      },
      op: {
        subject: 'LIKE',
        assigned_to_id: 'in',
        priority_id: 'in',
        parent_id: '<>',
      },
      order: 'desc',
    },
    tableHeight: 500,
    searchTimer: null,
    assignOptions: [],
    conf: [],
    assignUserSel: [],
    assignUserIssueSel: [],
    priorityIdsSel: [],
    priorityIdsIssueSel: [],
    caseTestStatusSel: [],
    enumerationList: [], //优先级
    memberqueryForm: {},
    showFloumn: {
      status: true,
      priority: true,

      indexColumn: false,
      projectColumn: false,
      assignedColumn: true,
      authorColumn: false,
      updatedOn: false,
      dueDateColumn: true,
    },
    issueKeyWord: '',
    selectRows: [],
    selectRowsSet: new Set(),
    planOption: [],
    planSelectFormRule: {
      plan_id: [
        {
          required: true,
          message: '计划不能为空',
          trigger: 'blur',
        },
      ],
    },
    planSelectForm: {},
    showDialog: false, // 控制弹窗是否显示
    dialogTitle: '新增', // 弹窗title
    dialogHeight: 500,
    dialogWidth: 'auto',
    formEditFor: '',
    planLoading: false,
    queryPlanTimer: '',
    addCaseList: [],
    exportForm: {
      filter: {
        'test_plan_case.test_plan_id': route.query.plan_id,
      },
      op: {
        'test_plan_case.id': 'in',
      },
    },
    testProgress: [],
    testList: [],
    firstRow: {},
    activeRowId: 0, //在测试中的行id
  })
  //详情
  const getOverView = async () => {
    state.testProgress = []
    const { data } = await overView({ id: state.planId })
    state.form = data
    state.testProgress = data.test_progress
  }
  //-------------------------测试用例-------------------

  const isProductMode = computed(() => {
    return route.query.is_product_mode === '1'
  })

  //返回计划列表
  const backPage = () => {
    changeRouter()
  }

  const changeRouter = () => {
    if (isProductMode.value) {
      if (route.query.source_page == 'product_detail') {
        // 从新窗口产品详情页面进入的，返回新窗口产品详情
        const path = '/project/productDetailsIndex'
        const query = {
          product_id: state.projectId,
        }
        router.push({
          path,
          query,
        })
      } else {
        // 从产品列表进入的，返回产品列表并自动弹出详情抽屉
        const path = '/project/productIndexIndex'
        const query = {
          auto_open_product: state.projectId,
          active_name: 'test_plan',
        }
        router.push({
          path,
          query,
        })
      }
    } else {
      // 项目模式保持原逻辑
      const path = '/project/testCase'
      const query = {
        project_id: state.projectId,
        type: '测试计划',
        fixed_version_id: state.form.version_id,
      }
      router.push({
        path,
        query,
      })
    }
  }

  //显示编辑弹窗
  const showEdit = (row) => {
    editRef.value.showEdit({ id: state.planId })
  }
  /**
   * 获取测试人员
   */
  const handleMemberList = async (projectId) => {
    state.memberqueryForm = {
      limit: 100,
      filter: {
        project_id: projectId ?? -1,
        user: { type: 'User' },
      },
    }
    const {
      data: { data },
    } = await getMemberList(state.memberqueryForm)
    state.assignOptions = data
    state.assignOptions.unshift({
      user_id: 0,
      user: {
        id: 0,
        type: 'User',
        name: '未指定',
      },
    })
    state.cloneMembers = JSON.parse(JSON.stringify(data))
    //缓存成员数据，搜索从这边查找
  }
  const fetchCaseData = async () => {
    state.caseList = []
    // state.caseTreeList = []
    state.listLoading = true
    state.caseQueryForm.filter['test_plan_case.assigned_user'] =
      state.assignUserSel != null ? state.assignUserSel.join(',') : null
    state.caseQueryForm.filter['test_case.priority'] =
      state.priorityIdsSel != null ? state.priorityIdsSel.join(',') : null
    state.caseQueryForm.filter['test_plan_case.test_status'] =
      state.caseTestStatusSel != null ? state.caseTestStatusSel.join(',') : null
    state.caseQueryForm.filter['test_plan_case.test_plan_id'] = state.planId
    const response = await getPlanCase(state.caseQueryForm)
    const data = response.data.data
    state.total = response.data.total
    state.testList = data.list

    state.caseList = data.list
    state.caseTreeList = data.tree

    state.firstRow = data.list.length > 0 ? data.list[0] : {}
    state.listLoading = false
    await nextTick()
    await nextTick()
    state.flaternTreeListButOnlyCase = getFlaternTreeListButOnlyCase()
  }
  const queryDataByTimer = async () => {
    if (state.searchTimer) {
      clearTimeout(state.searchTimer)
      state.searchTimer = null
    }
    state.searchTimer = setTimeout(() => {
      fetchCaseData()
    }, 800)
  }
  const queryIssueData = async () => {
    if (state.searchTimer) {
      clearTimeout(state.searchTimer)
      state.searchTimer = null
    }
    state.searchTimer = setTimeout(() => {
      state.issueQueryForm.filter['subject'] = state.issueKeyWord
      state.issueQueryForm.filter['assigned_to_id'] =
        state.assignUserIssueSel != null
          ? state.assignUserIssueSel.join(',')
          : null
      state.issueQueryForm.filter['priority_id'] =
        state.priorityIdsIssueSel != null
          ? state.priorityIdsIssueSel.join(',')
          : null
    }, 800)
  }
  const getConf = async () => {
    const { data } = await conf()
    state.conf = data
  }

  //归档
  const setIsArchived = throttle(async () => {
    let isArchived = state.form.is_archived
    let tip = ''
    if (isArchived === 1) {
      tip = '是否解除归档'
      isArchived = 0
    } else {
      tip = '是否归档'
      isArchived = 1
    }
    $baseConfirm(tip, null, async () => {
      const data = {
        id: state.planId,
        is_archived: state.form.is_archived === 1 ? 0 : 1,
      }
      await doPlanEdit(data)
      getOverView()
      $baseMessage('修改成功', 'success', 'vab-hey-message-success')
    })
  })

  //删除
  const deletePlan = throttle(async () => {
    $baseConfirm('确认删除该计划', null, async () => {
      const data = {
        ids: [state.planId],
      }
      await doPlanDelete(data)
      $baseMessage('删除成功', 'success', 'vab-hey-message-success')
      //跳回到列表页面
      backPage()
    })
  })

  //批量操作
  const doMultiEdit = async (field, value) => {
    if (state.selectRows.length === 0) {
      return
    }
    let data = {
      ids: state.selectRows,
      params: {
        type: field,
      },
    }
    data.params[field] = value
    await doPlanCaseMulti(data)
    $baseMessage('修改成功', 'success', 'vab-hey-message-success')
    getOverView()
    fetchCaseData()
  }

  /**
   * 表格可勾选的选择项发生变化
   */
  // const handleSelectionChange = (selection) => {
  //   state.selectRows = selection.map((item) => {
  //     return item.id
  //   })
  //   console.log('state.selectRows :', state.selectRows)
  // }
  const handleSelectionChange = async (selection) => {
    const { row, checked } = selection
    const rowId = row.case_id
    let set = cloneDeep(state.selectRowsSet)

    // 如果选中行是 'case' 类型
    if (row.type === 'case') {
      if (checked) {
        // 使用 Set 进行判断和添加
        set.add(rowId)
        console.log('rowId added:', rowId)
      } else {
        // 移除该行 id
        set.delete(rowId)
      }
    } else {
      // 非 'case' 类型，处理 'library' 或其他类型
      let ids = await getChildIds(row.children)

      // 使用 Set 进行判断和添加
      if (checked) {
        ids.forEach((id) => set.add(id))
        console.log('ids added:', ids)
      } else {
        ids.forEach((id) => set.delete(id))
      }
    }
    state.selectRowsSet = set

    // 打印选择的所有行
    // console.log('state.selectRowsSet:', state.selectRowsSet)
  }

  /**
   * vxe-table
   * @param {*} selection
   */
  const selectAllChangeEvent = async (selection) => {
    // 更新 state.selectRows
    state.selectRowsSet = await getChildIds(selection.records)
    // console.log('state.selectRowsSet :', state.selectRowsSet)
  }

  const getChildIds = async (records) => {
    // 使用 Set 存储所有 case_id，自动去重
    const caseIds = new Set()

    // 栈用于迭代
    const stack = Array.isArray(records) ? [...records] : []

    while (stack.length > 0) {
      const item = stack.pop()

      if (item.case_id) {
        // 当存在 case_id 时，提取 case_id
        caseIds.add(item.case_id)
      }

      if (item.children && item.children.length > 0) {
        // 如果存在 children，将子节点压入栈
        stack.push(...item.children)
      }
    }

    // 返回 Set
    return caseIds
  }

  watch(
    () => state.selectRowsSet,
    () => {
      state.selectRows = [...state.selectRowsSet]
      // console.log('state.selectRowsSet:', state.selectRowsSet)
      // console.log('state.selectRows:', state.selectRows)
    }
  )

  const handleSave = async (row, field, value) => {
    if (field === 'priority') {
      let data = { id: row['test_case_id'] }
      data[field] = value ? value : row[field]
      doCaselEdit(data)
    } else {
      let data = { id: row['id'] }
      data[field] = value ? value : row[field]
      await doPlanCaselEdit(data)
      getOverView()
      fetchCaseData()
    }

    $baseMessage('修改成功', 'success', 'vab-hey-message-success')
  }

  const handleEnumerationList = async () => {
    const { data } = await getEnumerationList()
    state.enumerationList = data
  }

  const showCaseSelect = () => {
    state.addCaseList = []
    caseSelectRef.value.showEdit()
  }
  const doExport = async () => {
    state.exportForm.filter['test_plan_case.id'] = state.selectRows.join(',')
    await exportCase(state.exportForm)
      .then((response) => {
        const blob = new Blob([response])
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', '测试用例.xlsx') // 设置下载文件的名称
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url) // 释放内存
      })
      .catch((error) => {
        console.error('下载失败:', error)
      })
  }
  const detachCase = async () => {
    if (state.selectRows.length === 0) {
      return
    }
    let data = {
      ids: state.selectRows,
    }
    await doDeletePlanCase(data)
    getOverView()
    fetchCaseData()
    $baseMessage('删除完成', 'success', 'vab-hey-message-success')
  }

  const caseTitleClick = (scope) => {
    let row = null
    if (scope.row.type == 'case') {
      row = scope.row
    }
    titleClick(row)
  }

  /**
   * title列点击事件处理
   * @param {*} row
   */
  const titleClick = async (row = null) => {
    if (row === null) {
      return
    }
    if (Object.values(row).length === 0) {
      $baseMessage('没有可测试的用例', 'warning', 'vab-hey-message-warning')
      return
    }
    state.activeRowId = row.id
    const tempRow = cloneDeep(row)
    // console.log('tempRow', row, tempRow, !tempRow)

    //增加测试标致
    tempRow.type = 'plan_case'
    testCaseDialogRef.value.showEdit(tempRow, {
      plan_case_list: state.flaternTreeListButOnlyCase,
      // plan_case_list: state.caseList,
    }) // row传入dialog 再经过事件发布传到点击函数
  }

  const tableRowClassName = ({ row }) => {
    // 如果当前行的 ID 和 selectId 相同，返回高亮类名
    return row.id === state.activeRowId ? 'current-row' : ''
  }

  //弹窗
  /////////////////////弹窗/////////////////////////////
  const openDialog = async (editor) => {
    state.formEditFor = editor
    if (editor == 'moveToPlan') {
      state.dialogTitle = '移动用例'
      state.dialogWidth = 500
    }
    await new Promise((resolve) => setTimeout(resolve, 200))
    state.showDialog = true
  }
  //加载计划列表
  const remotePlanList = async (query) => {
    if (state.queryPlanTimer) {
      clearTimeout(state.queryPlanTimer)
      state.getQcTimer = null
    }
    let keyWord = null
    if (query) {
      keyWord = query
    }
    state.queryPlanTimer = setTimeout(async () => {
      let queryForm = {
        filter: {
          'test_plan.title': keyWord,
          'test_plan.is_archived': 0,
        },

        op: {
          'test_plan.title': 'like',
        },
        limit: 999,
      }
      // 只有在产品模式下才添加project_id筛选条件
      if (isProductMode.value) {
        queryForm.filter['test_plan.project_id'] = state.projectId
      }
      const { data } = await getPlanList(queryForm)
      state.planOption = data.data
    }, 880)
  }

  const showTestReport = (row) => {
    testPlanReportRef.value.showEdit(row)
  }

  const saveDialog = throttle(async (editor) => {
    if (editor === 'moveToPlan') {
      planSelectFormRef.value.validate(async (valid) => {
        if (valid) {
          if (state.planSelectForm.test_plan_id === state.planId) {
            $baseMessage(
              '不允许移动到本身计划',
              'warning',
              'vab-hey-message-warning'
            )
            return
          }
          if (!state.planSelectForm.test_plan_id) {
            $baseMessage(
              '尚未选中任何计划',
              'warning',
              'vab-hey-message-warning'
            )
            return
          }
          await doMultiEdit('test_plan_id', state.planSelectForm.test_plan_id)
          closeDialog()
        }
      })
    }
  })
  const closeDialog = () => {
    state.showDialog = false
  }

  const addPlanCase = throttle(async () => {
    if (state.addCaseList.length === 0) {
      return
    }
    // 将 caseTreeList 转换为 Map，按 id 和 type === 'case' 分组
    const caseTreeMap = new Map(
      state.caseTreeList
        .filter((citem) => citem.type === 'case') // 只保留 type 为 'case' 的项
        .map((citem) => [citem.test_case_id, citem.assigned_user ?? 0]) // id -> assigned_user
    )

    // 遍历 addCaseList 并更新 assigned_user
    state.addCaseList.forEach((item) => {
      if (item.assigned_user == 0) {
        // 如果未定义则尝试赋值
        item.assigned_user = caseTreeMap.get(item.id) ?? 0
      }
    })
    await addCase({ id: state.planId, plan_case: state.addCaseList })
    getOverView()
    fetchCaseData()
    $baseMessage('修改成功', 'success', 'vab-hey-message-success')
  })

  const handleCurrentChange = (val) => {
    state.caseQueryForm.pageNo = val
    fetchCaseData()
  }

  const handleSizeChange = (val) => {
    state.caseQueryForm.limit = val
    fetchCaseData()
  }

  // 计算属性来动态控制虚拟滚动
  const scrollYConfig = computed(() => {
    return state.caseList.length > 20
      ? { enabled: true, gt: 0, mode: 'wheel' } // 当数据大于时启用虚拟滚动
      : { enabled: false } // 否则禁用虚拟滚动
  })

  /**
   * 控制用例目录是否展开
   */
  const changeExpand = () => {
    state.expandAllRow = !state.expandAllRow
    state.expandAllRow ? expandAllEvent() : clearExpandEvent()
  }

  const expandAllEvent = () => {
    const $table = tableRef.value
    if ($table) {
      $table.setAllTreeExpand(true)
    }
  }
  const clearExpandEvent = () => {
    const $table = tableRef.value
    if ($table) {
      $table.clearTreeExpand()
    }
  }

  /**
   * 展开/收起顶部状态条
   */
  const expandOrCollaspeTopContainer = () => {
    state.showProgressBar = !state.showProgressBar
    containerTopHeight.value = state.showProgressBar ? '172px' : '86px'

    state.tableHeight += state.showProgressBar ? -86 : 86
  }

  /**
   * 模块（分类）按钮是否已被选中判断
   */
  const isSelectedTag = (tagItem) => {
    return state.selectedTags.includes(tagItem.id)
  }

  /**
   * 模块标签列表被点击
   */
  const toggleSelectionTag = (item) => {
    const index = state.selectedTags.indexOf(item.id)
    if (index === -1) {
      state.selectedTags.push(item.id)
    } else {
      state.selectedTags.splice(index, 1)
    }
    state.caseQueryForm.filter.category_id = Object.values(
      state.selectedTags
    ).join(',')
    state.caseQueryForm.op.category_id = 'in'
    if (state.selectedTags.length === 0) {
      state.caseQueryForm.filter.category_id = null
      state.caseQueryForm.op.category_id = null
    }

    fetchCaseData(false)
  }

  /**
   * 获取模块（分类）列表
   */
  const handleCategoryList = async () => {
    let directory = []
    state.caseTreeList.forEach((item) => {
      if (item.type == 'directory') {
        directory.push(item.id)
      }
    })
    let directoryStr = directory.join(',')
    let categoryQueryForm = {
      filter: {
        directory_id: directoryStr,
        project_id: state.projectId,
        test_plan_id: state.planId,
      },
      op: {
        directory_id: 'in',
      },
      limit: 99999,
      order: 'desc',
      pageNo: 1,
    }
    const res = await getCategoryList(categoryQueryForm)
    state.unitList = res.data

    state.isShowUnit = _.some(state.unitList, (item) => item.cases_count > 0)
  }

  /**
   * 单独更新行数据
   * @param {*} rowId
   */
  const updateRow = async (rowId) => {
    let rowIndex = tableRef.value.data.findIndex(
      (item) => item.test_case_id == rowId && item.type == 'case'
    )
    const response = await getPlanCase({
      filter: {
        'test_case.id': rowId,
      },
      order: 'desc',
      pageNo: 1,
      limit: 99999,
    })
    let data = response.data.data.list[0]
    handleCategoryList()
    Object.assign(tableRef.value.data[rowIndex], data)
  }

  /**
   * 获取只有用例数据的treeData，用来判断是否自动前进到下一个
   */
  const getFlaternTreeListButOnlyCase = () => {
    let { visibleData } = tableRef.value.getTableData()
    let tempVisibleData = cloneDeep(visibleData)
    let caseList = []
    caseList = getCaseOnly(tempVisibleData)
    // console.log('caseList:', caseList)

    return caseList
  }

  /**
   * 多个项目成员合并
   * @param {Array} members
   */
  const handleAllProjectMembers = (members) => {
    state.assignOptionsForIssueList = cloneDeep(state.assignOptions)
    state.assignOptionsForIssueList.shift()
    state.assignOptionsForIssueList.unshift({
      user_id: -1,
      user: {
        id: -1,
        type: 'User',
        name: '未指定',
      },
    })
    // 存储已处理的 user_id
    const userIdSet = new Set()

    _.forEach(members, (v, k) => {
      if (k != state.projectId) {
        v.forEach((item) => {
          if (item.id != -1 && !userIdSet.has(item.id)) {
            // 如果 user_id 不在 Set 中，添加到 assignOptions
            state.assignOptionsForIssueList.push({
              user_id: item.id,
              user: item.user,
            })
            userIdSet.add(item.id) // 记录已添加的 user_id
          }
        })
      }
    })
  }

  const getCaseOnly = (data) => {
    let childCase = []
    _.forEach(data, (item) => {
      if (item.type == 'case') {
        childCase.push(item)
      }
      if (item.children?.length > 0) {
        childCase = [...childCase, ...getCaseOnly(item.children)]
      }
    })
    return childCase
  }

  $sub('add-case-result-success', () => {
    getOverView()
    fetchCaseData()
  })
  $sub('plan-add-success', () => {
    getOverView()
    fetchCaseData()
  })
  $sub('change-active-plan-case', function (params) {
    // console.log('change-active-plan-case', params)

    state.activeRowId = params.id || 0
  })
  // 窗口大小变化监听器
  const handleResize = () => {
    state.tableHeight =
      document.documentElement.clientHeight -
      432 +
      Number(!state.unitList.length) * 60
  }

  onMounted(async () => {
    radio.value = '测试用例'
    await handleCategoryList()

    state.tableHeight =
      document.documentElement.clientHeight -
      432 +
      Number(!state.unitList.length) * 60

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize)

    getConf()
    handleEnumerationList()
    getOverView()
    handleMemberList(state.projectId)

    await fetchCaseData()

    changeExpand() // 首次加载完成默认需要展开所有树

    remotePlanList()
  })
  onUnmounted(async () => {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize)

    $unsub('plan-add-success')
    $unsub('add-case-result-success')
    $unsub('change-active-plan-case')
  })

  // 监听activeRowId的变化
  watch(
    () => state.activeRowId,
    (newVal) => {
      if (newVal && tableRef.value) {
        // 延迟一点执行，确保DOM已更新
        setTimeout(() => {
          // 需要找到对应id的行对象
          const row = findRowById(newVal)
          console.log('row:', row)
          if (row) {
            // 滚动到当前选中的行
            tableRef.value
              .scrollToRow(row)
              .then(() => {
                // 滚动完成后可以做一些操作（如有需要）
                console.log('滚动到指定行完成')
              })
              .catch((error) => {
                console.error('滚动失败:', error)
              })
          }
        }, 100)
      }
    }
  )

  // 根据id查找行对象的辅助函数
  const findRowById = (id) => {
    if (state.caseTreeList.length == 0) return null

    // 获取表格数据
    const fullData = state.caseTreeList

    // 在数据中查找对应key_id的行
    return fullData.find((row) => row.id === id || row.key_id === id)
  }
</script>
<style lang="scss" scoped>
  .button-icon {
    width: 20px;
    height: 20px;
  }
  .plan-detail-text {
    height: 24px;
    margin: 8px 50px;
    margin-right: 0px;
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: #333;
    .icon-text {
      display: flex;
      align-items: center;
      margin-right: 24px;
    }
  }
  .plan-detail-icon {
    width: 24px !important;
    height: 24px !important;
    color: #999;
  }
  .order-description-title-text {
    max-width: 40vw;
    padding: 0px 8px 0px 15px;
    overflow: hidden;
    font-size: 20px;
    font-weight: 600;
    color: #000;
    text-overflow: ellipsis;
    letter-spacing: -0.01px;
    white-space: nowrap;
  }
  .container-top {
    height: v-bind('containerTopHeight');

    padding: 16px;

    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 6px;
  }
  .container-button {
    margin-top: 16px;
  }
  .container-content {
    margin-top: 16px;
  }
  .test-plan-detail {
    padding: 16px !important;
    background: #fff;
  }
  .circle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .custom-button {
    color: #007bff; /* 蓝色图标 */
    background-color: #d3d3d3; /* 灰色背景 */
  }

  .custom-button:hover {
    background-color: #b3b3b3; /* 悬停时背景色变化 */
  }
  .hoverable-item {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    font-size: 15px;
    cursor: pointer;

    /* 初始背景颜色 */
    background-color: white;
    border-radius: 6px;
    transition: background-color 0.3s;

    &:hover {
      color: $base-color-primary;
      background-color: #f0f0f0;
    }

    &.active {
      color: #3977f7; /* 点击后的字体颜色 */
    }

    .check-icon {
      margin-left: auto;
    }
  }
  .dropdown-select-item {
    box-sizing: border-box;
    height: 34px;
    padding: 0 32px 0 20px;
    overflow: hidden;
    font-size: 14px;
    line-height: 34px;
    color: var(--el-text-color-regular);
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    &:hover {
      color: $base-color-primary;
      background-color: #ebf1fe;
    }

    &.active {
      color: #3977f7; /* 点击后的字体颜色 */
    }
    border-radius: 6px;
    .vab-icon {
      width: 24px;
      height: 24px;
    }
  }
  .assigned-avatar-normal {
    width: 22px;
    height: 22px;
    border-radius: 22px;
  }
  .assigned-avatar {
    width: 18px;
    height: 18px;
    margin-right: 10px;
    border-radius: 18px;
  }
  .assigned-name {
    font-size: 16px;
    color: #333;
  }

  :deep() {
    .priority-tag-of-plan-detail-list {
      .el-select__wrapper {
        gap: 10px;
      }
    }

    .progress-segment:last-child {
      border-radius: 0 4px 4px 0;
    }
    .progress-segment:first-child {
      border-radius: 4px 0 0 4px;
    }
    .progress-segment:only-child {
      border-radius: 4px; /* 只有一个元素时，四个角都为2px */
    }

    .el-radio-button:last-child .el-radio-button__inner {
      border-radius: 0 var(--el-border-radius) var(--el-border-radius) 0;
    }
    .el-radio-button:first-child .el-radio-button__inner {
      border-radius: var(--el-border-radius) 0 0 var(--el-border-radius);
    }
    /* 移除表格四周的边框 */
    .el-table::before {
      display: none;
    }

    // 移除表格行内左右的border
    .el-table--enable-row-transition .el-table__body td.el-table__cell {
      border-right: none;
      border-left: none;
    }

    /* 移除表格头部的分隔线 */
    .el-table__header-wrapper {
      border-bottom: none !important;
    }

    /* 移除表格四周外层边框 */
    .el-table__body-wrapper {
      border: none !important;
    }

    // el-table border 上边界去除
    .el-table--border .el-table__inner-wrapper::after {
      height: 0px !important;
    }

    // el-table border 左边界去除
    .el-table__border-left-patch {
      width: 0px !important;
    }

    // el-table border 右边界去除
    .el-table--border::after {
      width: 0px !important;
    }

    // el-table border 下边界去除
    .el-table__inner-wrapper::before {
      height: 0px !important;
    }

    .vab-query-form .bottom-panel {
      border-top: none;
    }
    .el-popover {
      --el-popover-title-font-size: 14px;
    }
    .expand-mode-radio-class {
      position: fixed;
      right: 28px;
    }
    .el-form-item__label {
      justify-content: left;
    }

    .el-switch {
      height: 0px;
      padding-left: 4px;
    }

    .el-table .el-table__cell {
      padding: 6px 0;
    }

    .el-pagination {
      justify-content: left;
    }

    a + a {
      margin-left: 0 !important;
    }
    .dropdown-active {
      color: var(--el-dropdown-menuItem-hover-color);
      background-color: var(--el-dropdown-menuItem-hover-fill);
    }

    .issue-subject {
      .el-tooltip {
        display: flex;
        align-items: center;
        a {
          // width: calc(100% - 30px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
        }
      }
    }
  }

  :deep(.el-table__placeholder) {
    display: none; //不隐藏会导致标题列和th未对齐
  }

  .out-border {
    border-top: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    border-left: 1px solid #ebeef5;
    border-radius: $base-border-radius;
  }

  .row-select .el-scrollbar__wrap {
    min-height: 100px; /*避免下拉菜单在加载得时候，无法计算高度导致在下方弹出*/
  }

  .el-dropdown-menu {
    z-index: 9999;
  }

  //下拉菜单文字垂直居中
  .vertical-td {
    position: relative;
    align-items: center;
    width: 100%;
    height: 100%;
    min-height: 30px;
    vertical-align: middle !important;
    cursor: pointer;
    .arrow-down-icon {
      visibility: hidden;
    }
    & > div {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 8px;

      //把日期input隐藏掉
      .el-date-editor {
        position: absolute; //绝对定位
        top: 0;
        left: 0;
        opacity: 0;
      }
      .el-input__prefix {
        display: none;
      }
      .el-input__wrapper,
      .el-input__inner {
        cursor: pointer;
      }
    }
    &:hover {
      background-color: rgba(133, 146, 166, 0.1);
      border-radius: 2.5px;
      .arrow-down-icon {
        visibility: visible;
      }
    }
  }

  .oper-bar {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #fff;
    & > div {
      margin-right: 10px;
    }
  }

  // :deep(.el-table__indent) {
  //   padding-left: 32px !important;
  // }

  :deep(.el-table__expand-icon) {
    margin-right: 2px !important;
    margin-left: -14px !important;
  }

  .module-name {
    margin-left: 10px;
    color: #999;
    background: #fff;
    border-color: #dcdfe6;
    border-radius: 6px;
  }
  .back-icon {
    width: 32px;
    height: 32px;
    cursor: pointer;
  }
  :deep() {
    .case-list-table {
      th .cell {
        color: #666 !important;
      }
      td .cell {
        color: #333 !important;
      }
      .el-select .el-input__inner {
        color: #333 !important;
      }
    }
    .tag-select {
      .el-input__inner {
        visibility: hidden;
      }
      .tag-selection-item {
        height: 34px;
      }
    }
  }
</style>
<style lang="scss" scoped>
  // vxe-table表头、行样式自定义
  :deep() {
    .vxe-header--column {
      height: 40px !important;
      padding: 0;
    }

    .vxe-header--column .vxe-cell {
      height: 40px !important;
      min-height: 40px !important;
      line-height: 40px !important;
    }

    .vxe-body--column {
      padding: 6px 0;
      border-bottom: 1px solid #ebeef5;
    }
    .vxe-body--row:hover {
      background-color: #f5f7fa;
    }

    // 添加当前选中行的样式
    .vxe-body--row.current-row {
      background-color: #f5f7fa;
    }
  }
</style>
