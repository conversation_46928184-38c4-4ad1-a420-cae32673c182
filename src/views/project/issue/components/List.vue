<template>
  <div ref="fullscreenContainer">
    <vab-query-form v-if="props.simple">
      <vab-query-form-left-panel :span="20">
        <el-form
          inline
          label-width="50px"
          :model="state.queryForm"
          @submit.prevent
        >
          <el-form-item label="">
            <!-- <el-cascader
              style="width: 180px"
              v-model="state.queryForm.filter.project_id"
              :options="state.projectTypeList"
              :props="state.projectTypeListProps"
              :show-all-levels="false"
              clearable
              filterable
              @change="queryDataSearch"
            /> -->
            <el-select
              placeholder="请选择项目类型"
              v-model="state.queryForm.filter.project_type"
              style="width: 160px"
              clearable
              @change="queryDataSearch"
              filterable
            >
              <el-option
                v-for="item in state.projectTypeList"
                :key="item.keywords"
                :label="item.name"
                :value="item.keywords"
              />
            </el-select>

            <!-- <el-select
              v-model="state.queryForm.filter.project_type"
              placeholder="请选择项目类型"
              style="width: 180px"
              @change="queryDataSearch"
              filterable
            >
              <el-option
                v-for="item in state.projectTypeList"
                :key="item.keyword"
                :label="item.name"
                :value="item.keyword"
              />
            </el-select> -->
          </el-form-item>
          <el-form-item label="">
            <el-select
              v-model="state.selectedStatusCategories"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              placeholder="请选择状态"
              style="width: 220px"
              @change="handleStatusCategoryChange"
            >
              <el-option
                v-for="category in statusCategories"
                :key="category.value"
                :label="category.label"
                :value="category.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <IssuePriority
              v-model:array-value="state.selectedPriorityIds"
              border
              clearable
              style="width: 180px; border: 1px solid var(--el-border-color)"
              @change="selectedPriorityHandler"
              :multiple="true"
            />
          </el-form-item>
          <el-form-item label="" prop="">
            <el-date-picker
              style="width: 250px"
              v-model="state.queryForm.due_date"
              start-placeholder="截止开始日期"
              end-placeholder="截止结束日期"
              format="YYYY-MM-DD"
              value-format="YYYYMMDD"
              range-separator="To"
              type="daterange"
              unlink-panels
              :shortcuts="state.shortcuts"
              @change="queryDataSearch"
            />
          </el-form-item>
          <el-form-item label="">
            <el-select
              v-model="state.queryForm.filter.fixed_version_id"
              placeholder="请选择版本"
              clearable
              style="width: 160px"
              @change="queryDataSearch('fixed_version_selected')"
            >
              <el-option
                v-for="item in state.fixedVersionList"
                :key="item.id"
                :label="item.display_name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="4">
        <el-button :icon="PlusIcon" @click="newIssue">
          <span>新建事项</span>
        </el-button>
      </vab-query-form-right-panel>
    </vab-query-form>
    <vab-query-form v-if="props.showQuery">
      <vab-query-form-left-panel :span="16">
        <el-form
          inline
          label-width="50px"
          :model="state.queryForm"
          @submit.prevent
        >
          <!-- <el-form-item>
            <el-button
              type="default"
              :icon="RefreshIcon"
              @click="handleRefreshOptions"
            >
              重置
            </el-button>
          </el-form-item> -->
          <el-form-item label="" label-width="80px">
            <el-input
              v-model="state.queryForm.filter.subject"
              placeholder="请输入标题搜索"
              clearable
              @keyup.enter="querySubjectUseEnter()"
              @input="queryDataSearch('search_subject')"
              style="width: 160px"
            />
          </el-form-item>

          <el-form-item label="">
            <!-- 状态筛选器 -->
            <StatusCascaderSelector
              v-model="state.selectedStatusIds"
              :project-id="project_id"
              :storage-key="getStatusStorageKey()"
              :show-tracker-types="getShowTrackerTypes()"
              placeholder="请选择状态"
              width="200px"
              @change="handleStatusChange"
            />
          </el-form-item>
          <el-form-item label="">
            <div style="width: 160px">
              <PersonnelSelect
                v-model:value="state.queryForm.assigned"
                :option-list="state.projectMemberList"
                @change="queryDataSearch"
                :is-request="false"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-popover
              :visible="state.visiblePopover"
              ref="popoverRef"
              placement="bottom"
              title="更多筛选"
              :width="350"
              :show-arrow="false"
              content="更多筛选"
            >
              <template #reference>
                <!-- <el-badge
                  :value="filterCount"
                  :hidden="filterCount === 0"
                  type="primary"
                  :max="99"
                > -->
                <el-button
                  :type="'default'"
                  :style="
                    filterCount == 0
                      ? {}
                      : {
                          color: '#3977f3',
                          'border-color': '#3977f314',
                          'background-color': '#3977f314',
                        }
                  "
                  @click.stop="changeQueryFormTopShow"
                  ref="buttonRef"
                >
                  <vab-icon
                    icon="filter"
                    is-custom-svg
                    style="width: 24px; height: 24px"
                  />
                  更多筛选
                </el-button>
                <!-- </el-badge> -->
              </template>
              <div>
                <el-form
                  inline
                  label-width="76px"
                  :model="state.queryForm"
                  @submit.prevent
                  popper-class="el-form-in-popover"
                >
                  <el-form-item
                    label="事项类型"
                    v-if="project_id && props.filterFrom != 'issueClass'"
                  >
                    <el-select
                      v-model="state.selectedTrackerIds"
                      multiple
                      collapse-tags
                      placeholder="请选择事项类型"
                      style="width: 240px"
                      @update:model-value="selectedTrackerIdHandler"
                    >
                      <el-option
                        v-for="item in state.issueTypeList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                    <!-- <el-select
                      v-model="state.queryForm.filter.tracker_id"
                      clearable
                      placeholder="请选择事项类型"
                      style="width: 250px"
                      @change="queryDataHandler"
                    >
                      <el-option
                        v-for="item in state.issueTypeList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select> -->
                  </el-form-item>
                  <el-form-item
                    v-if="
                      props.showCloumn.projectColumn &&
                      shouldShowProjectSelector
                    "
                    label="项目"
                  >
                    <el-cascader
                      style="width: 240px"
                      v-model="state.queryForm.filter.project_id"
                      :options="state.projectList"
                      :props="state.projectListProps"
                      :show-all-levels="false"
                      clearable
                      filterable
                      multiple
                      collapse-tags
                      placeholder="请选择项目"
                      @change="handleProjectChange"
                    />
                  </el-form-item>
                  <el-form-item
                    v-if="
                      props.showCloumn.projectColumn &&
                      (urlProjectId ||
                        (state.queryForm.filter.project_id &&
                          state.queryForm.filter.project_id.length > 0))
                    "
                    label="版本"
                  >
                    <el-select
                      v-model="state.selectedProjectVersionIds"
                      multiple
                      collapse-tags
                      placeholder="请选择版本"
                      style="width: 240px"
                      clearable
                      @change="selectedProjectVersionHandler"
                    >
                      <el-option
                        v-for="item in state.projectVersionList"
                        :key="item.id"
                        :label="
                          getProjectName(item.project_id) +
                          item.name +
                          (item.status === 'closed'
                            ? '[已关闭]'
                            : item.status === 'locked'
                            ? '[已锁定]'
                            : '')
                        "
                        :value="item.id"
                      >
                        <span>
                          {{ getProjectName(item.project_id) }}
                          {{ item.name }}
                        </span>
                        <span
                          v-if="item.status === 'closed'"
                          style="font-size: 12px; color: #f56c6c"
                        >
                          [已关闭]
                        </span>
                        <span
                          v-else-if="item.status === 'locked'"
                          style="font-size: 12px; color: #e6a23c"
                        >
                          [已锁定]
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="优先级">
                    <IssuePriority
                      v-model:array-value="state.selectedPriorityIds"
                      border
                      clearable
                      style="
                        width: 240px;
                        border: 1px solid var(--el-border-color);
                      "
                      @change="selectedPriorityHandler"
                      :multiple="true"
                    />
                  </el-form-item>
                  <el-form-item
                    label="创建人"
                    v-if="props.isMyWork && state.hasPermission"
                  >
                    <el-select
                      v-model="state.queryForm.filter.author_id"
                      clearable
                      placeholder="请选择创建人"
                      style="width: 240px"
                      @change="queryDataSearch"
                      filterable
                    >
                      <el-option
                        v-for="item in state.projectMemberList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="创建人" v-if="project_id">
                    <div style="width: 240px">
                      <PersonnelSelect
                        v-model:value="state.queryForm.filter.author_id"
                        :option-list="state.projectMemberList"
                        @change="queryDataSearch"
                        :is-request="false"
                      />
                    </div>
                  </el-form-item>
                  <el-form-item label="创建时间" prop="">
                    <el-date-picker
                      style="width: 240px"
                      v-model="state.queryForm.created_on"
                      start-placeholder="创建开始日期"
                      end-placeholder="创建结束日期"
                      format="YYYY-MM-DD"
                      value-format="YYYYMMDD"
                      range-separator="To"
                      type="daterange"
                      unlink-panels
                      :shortcuts="state.shortcuts"
                      @change="queryDataSearch"
                    />
                  </el-form-item>
                  <el-form-item label="完成日期" prop="">
                    <el-date-picker
                      style="width: 240px"
                      v-model="state.queryForm.due_date"
                      start-placeholder="截止开始日期"
                      end-placeholder="截止结束日期"
                      format="YYYY-MM-DD"
                      value-format="YYYYMMDD"
                      range-separator="To"
                      type="daterange"
                      unlink-panels
                      :shortcuts="state.shortcuts"
                      @change="queryDataSearch"
                    />
                  </el-form-item>
                </el-form>
              </div>
            </el-popover>
          </el-form-item>
          <!-- <el-form-item label="" size="default">
            <el-radio-group v-model="radio">
              <el-radio-button label="树状" @change="setTreeLike()" />
              <el-radio-button label="平铺" @change="setTile" />
            </el-radio-group>
          </el-form-item> -->
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="8">
        <el-form
          inline
          label-width="50px"
          :model="state.queryForm"
          @submit.prevent
        >
          <el-form-item label="">
            <el-button
              type="default"
              :icon="UploadIcon"
              @click="exportData"
              v-permissions="state.auth_role"
            >
              导出
            </el-button>
          </el-form-item>
          <el-form-item label="">
            <el-popover
              width="220"
              :virtual-ref="button2Ref"
              trigger="click"
              :visible="state.visibleTreeViewPopover"
              :show-arrow="false"
            >
              <template #reference>
                <el-button
                  ref="button2Ref"
                  @click.stop="handleTreeViewPopover($event)"
                >
                  <img
                    :src="
                      state.isTreeView
                        ? require('@/assets/icon_images/child-node.png')
                        : require('@/assets/icon_images/issue-table.png')
                    "
                    alt="Icon"
                    style="width: 16px; height: 16px; margin-right: 8px"
                  />
                  {{ state.isTreeView ? '树状' : '平铺' }}
                </el-button>
              </template>
              <div style="padding: 10px">
                <div
                  class="hoverable-item"
                  @click="setTreeLike"
                  :class="{ active: state.isTreeView }"
                >
                  <vab-icon icon="subtree" is-custom-svg class="common-icon" />
                  <span>树状视图</span>
                  <vab-icon
                    v-if="state.isTreeView"
                    icon="check-fill"
                    class="check-icon"
                  />
                </div>
                <div
                  class="hoverable-item"
                  @click="setTile"
                  :class="{ active: !state.isTreeView }"
                >
                  <vab-icon icon="tile" is-custom-svg class="common-icon" />
                  <span>平铺视图</span>
                  <vab-icon
                    v-if="!state.isTreeView"
                    icon="check-fill"
                    class="check-icon"
                  />
                </div>
                <el-divider v-if="state.isTreeView" />
                <span v-if="state.isTreeView" style="font-size: 12px">
                  树状视图选项
                </span>
                <div
                  v-if="state.isTreeView"
                  class="hoverable-item"
                  :class="{ active: foldSwitchValue }"
                >
                  <vab-icon icon="subtree" is-custom-svg class="common-icon" />

                  <span style="font-size: 13px">
                    {{ foldSwitchValue ? '子事项展开' : '子事项折叠' }}
                  </span>
                  <el-tooltip
                    :content="
                      foldSwitchValue ? '点击折叠子事项' : '点击展开子事项'
                    "
                    :enterable="false"
                    placement="top"
                  >
                    <el-switch
                      v-model="foldSwitchValue"
                      :active-value="true"
                      :inactive-value="false"
                      @change="expandDef(foldSwitchValue, 'isfold', $event)"
                    />
                  </el-tooltip>
                </div>
              </div>
            </el-popover>
          </el-form-item>
          <el-form-item
            v-if="
              !props.isMyWork &&
              !props.isMyCreate &&
              !state.queryForm.filter.watcher &&
              state.unitList.length > 0
            "
          >
            <el-tooltip
              class="box-item"
              effect="dark"
              content="收起或者展开模块"
              placement="top"
            >
              <el-button type="default" @click="handleShowUnitList">
                <CommonIcon
                  :type="state.isShowUnit ? 'arrow_up' : 'arrow_down'"
                  :size="20"
                />
                <span style="margin-left: 3px">
                  {{ state.isShowUnit ? '收起模块' : '展开模块' }}
                </span>
              </el-button>
            </el-tooltip>
          </el-form-item>
          <!-- <el-form-item label="" size="default">
            <el-radio-group v-model="radio">
              <el-radio-button label="展开" @change="expandDef()" />
              <el-radio-button label="收缩" @change="expandDef(false)" />
            </el-radio-group>
          </el-form-item> -->
        </el-form>
      </vab-query-form-right-panel>
      <el-collapse-transition>
        <vab-query-form-bottom-panel
          ref="unitRef"
          id="vab-query-form-bottom-panel-issue-list"
          v-if="
            !props.isMyWork &&
            !props.isMyCreate &&
            !state.queryForm.filter.watcher &&
            state.unitList.length > 0 &&
            state.isShowUnit
          "
          class="custom-bottom-panel-style"
          :style="{ 'margin-bottom': '0px' }"
        >
          <el-form inline>
            <el-form-item v-for="tagItem in state.unitList" :key="tagItem.name">
              <el-button
                :style="{
                  color: isSelectedTag(tagItem) ? '#3977F3' : '#999999',
                  borderColor: isSelectedTag(tagItem) ? '#3977F3' : '#e4e7ed',
                }"
                @click="toggleSelectionTag(tagItem)"
              >
                <!-- @click="queryDataSearch('search_category', tagItem.id)" -->

                {{ tagItem.name }}&nbsp;•&nbsp;{{ tagItem.issues_count ?? 0 }}
              </el-button>
            </el-form-item>
          </el-form>
        </vab-query-form-bottom-panel>
      </el-collapse-transition>
    </vab-query-form>

    <el-table
      v-if="state.assignedSearch"
      v-loading="true"
      :height="props.height"
    />
    <el-table
      v-else
      ref="issueTableRef"
      v-loading="state.listLoading"
      class="issue-list"
      lazy
      :load="loadChildren"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :store="storeRef"
      :border="true"
      :data="listData"
      row-key="id"
      @selection-change="setSelectRows"
      @sort-change="handleChange"
      :height="state.tableHeight"
      :show-header="state.showHeader"
      :class="{ 'out-border': state.outBorder }"
      :default-sort="state.sortRule"
      :cell-style="cellStyleMethod"
    >
      <template #empty>
        <CommonEmpty />
      </template>

      <el-table-column v-if="props.pagination" type="selection" width="58">
        <template #header>
          <el-checkbox
            :model-value="isAllSelected"
            :indeterminate="isIndeterminate"
            @change="handleSelectAll"
          />
        </template>
        <template #default="scope">
          <span v-if="scope.row.isNew">{{ ' ' }}</span>
          <div v-else style="display: flex; gap: 8px; align-items: center">
            <!-- 手动添加勾选框 -->
            <el-checkbox
              :model-value="
                state.selectRows.some((row) => row.id === scope.row.id)
              "
              @change="(val) => handleRowSelect(scope.row, val)"
            />
            <!-- 操作按钮 -->
            <IssueActionMenu
              :row-data="scope.row"
              :is-visible="state.visiblePopoverIds.includes(scope.row.id)"
              :is-selected="
                state.selectRows.some((row) => row.id === scope.row.id)
              "
              @popover-show="handlePopoverShow"
              @popover-hide="handlePopoverHide"
              @copy-title="handleCopyTitle"
              @copy-link="handleCopyLink"
              @copy-issue="handleCopyIssue"
              @delete-issue="handleDeleteIssue"
            />
          </div>
        </template>
      </el-table-column>

      <!-- 动态渲染列 -->
      <el-table-column
        v-for="column in filteredColumns"
        :key="column.prop"
        :prop="column.prop"
        :label="column.label"
        :align="column.align"
        :fixed="column.fixed"
        :min-width="column.minWidth"
        :width="column.width"
        :sortable="column.sortable"
        :show-overflow-tooltip="column.showOverflowTooltip"
      >
        <template #default="scope">
          <!-- 根据不同的模板标识渲染不同内容 -->

          <template
            v-if="
              column.template === 'title' &&
              !scope.row.isNew &&
              !scope.row.subjectEditing
            "
          >
            <div
              style="display: flex; margin-left: 4px"
              class="issue-table-list-subject-colomn"
            >
              <span
                style="
                  overflow: hidden; /* 隐藏超出容器的文本 */
                  text-overflow: ellipsis; /* 超出部分用省略号表示 */
                  white-space: nowrap; /* 禁止换行 */
                "
              >
                <!-- 左侧文本内容（带溢出省略） -->

                <span @click="copyIssueLink(scope.row)">
                  <TrackerTypeTag
                    v-if="scope.row.issue_type?.identifier"
                    :name="scope.row.issue_type.name"
                    :tracker-type="scope.row.issue_type.tracker_type"
                    :color="scope.row.issue_type.color"
                    :identifier="scope.row.issue_type.identifier"
                    variant="filled"
                    size="small"
                    style="
                      display: inline-block;
                      margin: 0px 5px;
                      cursor: pointer;
                    "
                  />
                  <CommonIcon
                    v-else
                    :issue-type="scope.row.icon"
                    style="
                      display: inline-block;
                      margin: 0px 5px;
                      cursor: pointer;
                    "
                  />
                </span>

                <a
                  @click="openIssue(scope.row)"
                  class="common-link"
                  style="
                    position: relative;
                    top: 2px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  "
                >
                  <span class="text-color-secondary">#{{ scope.row.id }}</span>
                  {{ scope.row.subject }}
                </a>

                <span
                  class="text-color-secondary inline-dot"
                  v-if="
                    scope.row.parent_name &&
                    (!state.isTreeView || notInList(scope.row.parent_id))
                  "
                  style="position: relative; top: 2px"
                >
                  •&nbsp;
                </span>

                <span
                  v-if="
                    scope.row.parent_name &&
                    (!state.isTreeView || notInList(scope.row.parent_id))
                  "
                  class="text-color-secondary common-link"
                  style="position: relative; top: 2px"
                  @click="
                    openIssue({
                      project_id: scope.row.project_id,
                      id: scope.row.parent_id,
                    })
                  "
                >
                  {{ scope.row.parent_name }}
                </span>

                <template v-if="props.simple">
                  <span
                    class="text-color-secondary inline-dot"
                    style="position: relative; top: 2px"
                  >
                    &nbsp;•&nbsp;
                  </span>

                  <el-link
                    :href="`/#/project/issue?project_id=${scope.row.project_id}`"
                    :underline="false"
                    style="margin-top: -1px"
                  >
                    <span
                      class="text-color-secondary"
                      style="position: relative; top: 2px"
                    >
                      {{ scope.row?.project_text?.name }}
                    </span>
                  </el-link>
                </template>

                <template v-if="scope.row.category_text">
                  <el-tag
                    type="info"
                    class="module-name"
                    v-if="scope.row.is_top"
                  >
                    {{ scope.row.category_text?.name }}
                  </el-tag>
                </template>
                <template
                  v-if="
                    props.showPublic &&
                    scope.row.issue_ext &&
                    scope.row.issue_ext.is_public == 1
                  "
                >
                  <el-tag type="danger" effect="dark" style="margin-left: 5px">
                    公开问题
                  </el-tag>
                </template>
              </span>
              <vab-icon
                v-if="
                  !(
                    props.showPublic &&
                    scope.row.issue_ext &&
                    scope.row.issue_ext.is_public == 1
                  )
                "
                class="text-operation-button common-icon"
                style="min-width: 24px; margin-top: 3px"
                :style="{ marginRight: (scope.row.depth ?? 0) * 16 + 'px' }"
                icon="edit"
                is-custom-svg
                @click="editIssueSubject(scope.row)"
              />
            </div>
          </template>
          <template
            v-else-if="
              column.template === 'title' &&
              !scope.row.isNew &&
              scope.row.subjectEditing
            "
          >
            <el-input
              ref="inputRef"
              :autofocus="true"
              style="
                width: calc(100% - 50px);
                max-width: 550px;
                margin-left: 4px;
              "
              v-model="state.subjectForEdit"
              @blur="saveSubject(scope.row)"
              @keyup.enter="saveSubject(scope.row)"
              placeholder="请输入"
            />
          </template>

          <template
            v-else-if="column.template === 'status' && !scope.row.isNew"
          >
            <IssueStatus
              v-if="!state.listLoading"
              v-model:value="scope.row.status_id"
              :row-data="scope.row"
              :option="scope.row.status_list"
              @change="handleSave(scope.row, 'status_id')"
            />
          </template>

          <template
            v-else-if="column.template === 'priority' && !scope.row.isNew"
          >
            <IssuePriority
              v-if="state.enumerationList.length > 0"
              v-model:value="scope.row.priority_id"
              :option="state.enumerationList"
              @change="handleSave(scope.row, 'priority_id')"
            />
          </template>

          <template
            v-else-if="column.template === 'assigned' && !scope.row.isNew"
          >
            <IssueAssign
              v-if="state.projectMemberList.length > 0"
              v-model:value="state.batchChangeAssigner"
              :option="state.projectMemberList"
              :row-data="scope.row"
              :use-immediate-option="true"
              :min-width="'195px'"
              @change="handleSave(scope.row, 'assigned')"
            />
            <!-- <el-select
              v-model="scope.row.assigned"
              :class="['none-border-on-common', 'issue-' + scope.row.id]"
              placeholder="未选择处理人"
              filterable
              multiple
              remote
              :remote-method="filterUserList"
              style="width: 100%"
              @click="clickSelectAssigned(scope.row)"
              @visible-change="selectAssigned"
              @change="handleSave(scope.row, 'assigned')"
            >
              <template v-if="props.isTestPlanUse || props.simple">
                <el-option
                  v-for="item in state.allJoinedPorjectMemberList[
                    scope.row.project_id
                  ]"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                >
                  {{ item.name }}
                </el-option>
              </template>
              <template v-else>
                <el-option
                  v-for="item in state.projectMemberList"
                  :key="item.id"
                  :value="item.id"
                  :label="item.name"
                >
                  {{ item.name }}
                </el-option>
              </template>
            </el-select> -->
          </template>

          <template
            v-else-if="column.template === 'author' && !scope.row.isNew"
          >
            <span v-if="scope.row.author_text" style="color: #666666">
              {{ scope.row.author_text.lastname
              }}{{ scope.row.author_text.firstname }}
            </span>
            <span v-else>&nbsp;</span>
          </template>

          <template
            v-else-if="column.template === 'version' && !scope.row.isNew"
          >
            <IssueVersion
              v-model:value="scope.row.fixed_version_id"
              :defalut-label="
                scope.row.version_text ? scope.row.version_text.name : null
              "
              :option="state.projectVersionList"
              @click="reloadProjectVersionList(scope.row)"
              @change="handleSave(scope.row, 'fixed_version_id')"
            />
          </template>

          <template
            v-else-if="column.template === 'dueDate' && !scope.row.isNew"
          >
            <span
              v-if="scope.row.issue_status?.status_category === 'completed'"
              :style="{ 'border-radius': '4px', 'font-size': '14px' }"
            >
              {{ scope.row.closed_on?.slice(0, 10) }}
            </span>
            <el-tag
              type="danger"
              effect="dark"
              v-else-if="
                scope.row.issue_status?.status_category !== 'completed' &&
                scope.row.due_date &&
                new Date(scope.row.due_date).setHours(0, 0, 0, 0) <
                  state.nowDate
              "
              :style="{ 'border-radius': '4px', 'font-size': '14px' }"
            >
              {{
                '已超期' +
                Math.abs(
                  Math.floor(
                    (new Date(scope.row.due_date).setHours(0, 0, 0, 0) -
                      state.nowDate) /
                      (24 * 60 * 60 * 1000)
                  )
                ) +
                '天'
              }}
            </el-tag>
            <el-tag
              type="info"
              v-else-if="
                scope.row.due_date &&
                new Date(scope.row.due_date).setHours(0, 0, 0, 0) >=
                  state.nowDate
              "
              :style="{
                'font-size': '14px',
                border: 'none',
                background: 'none',
              }"
            >
              {{
                '剩余' +
                Math.abs(
                  Math.floor(
                    (new Date(scope.row.due_date).setHours(0, 0, 0, 0) -
                      state.nowDate) /
                      (24 * 60 * 60 * 1000)
                  )
                ) +
                '天'
              }}
            </el-tag>
          </template>

          <template
            v-else-if="column.template === 'project' && !scope.row.isNew"
          >
            <router-link
              :to="`/project/issue?project_id=${scope.row.project_id}`"
            >
              {{ scope.row.project_text.name }}
            </router-link>
          </template>

          <template v-else-if="column.template === 'title' && scope.row.isNew">
            <div @mouseleave="mouseLeaveDiv()">
              <div v-if="!quicklyCreating">
                <el-button @click="addNewRow(scope.row)" :icon="Plus" text>
                  快速创建
                </el-button>
              </div>
              <div v-else>
                <el-select
                  v-model="scope.row.tracker_id"
                  placeholder="事项类型"
                  style="width: 80px; margin-right: 8px"
                  @change="(value) => handleTrackerChange(value, scope.row)"
                >
                  <el-option
                    v-for="item in state.trackerList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>

                <el-input
                  style="margin-right: 8px"
                  v-model="quicklyC.subject"
                  @keyup.enter="
                    !state.hasRequiredCustomFields &&
                      saveQuiklyCreate(scope.row)
                  "
                  :placeholder="
                    state.hasRequiredCustomFields
                      ? '该事项类型请使用完整创建功能'
                      : '请在此输入标题'
                  "
                />

                <el-select
                  :class="[
                    'm-2',
                    'tag-select',
                    'no-background-disabled-selection',
                  ]"
                  v-model="scope.row.priority_id"
                  placeholder=""
                  remote
                  collapse-tags
                  style="width: 90px"
                  :disabled="state.hasRequiredCustomFields"
                >
                  <template #prefix>
                    <el-tag
                      :type="priorityTagType[scope.row.priority_id] ?? 'info'"
                      style="min-width: 44px; border: none"
                    >
                      <span style="font-weight: bold">
                        {{ priorityTagName[scope.row.priority_id] }}
                      </span>
                    </el-tag>
                  </template>
                  <el-option
                    v-for="item in state.enumerationList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                    <div class="flex-algin-center tag-selection-item">
                      <el-tag
                        :type="priorityTagType[item.id] ?? 'info'"
                        style="min-width: 44px; margin-top: 5px; border: none"
                      >
                        <span style="font-weight: bold">
                          {{ priorityTagName[item.id] }}
                        </span>
                      </el-tag>
                    </div>
                  </el-option>
                </el-select>

                <el-button
                  @click="saveQuiklyCreate(scope.row)"
                  type="primary"
                  style="margin-left: 8px"
                  :disabled="state.hasRequiredCustomFields"
                >
                  保存
                </el-button>

                <el-button @click="closeQuiklyCreate(scope.row)">
                  取消
                </el-button>
              </div>
            </div>
          </template>
          <template v-else-if="scope.row.isNew">
            <span></span>
          </template>
        </template>
        <template v-if="column.template === 'setting'" #header>
          <div style="margin-left: -5px">
            <vab-icon
              icon="settings-5-fill"
              @click="headerSetting"
              style="cursor: pointer"
            />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-if="props.pagination && state.pageShow"
      background
      :small="false"
      :current-page="state.queryForm.pageNo"
      :layout="state.layout"
      :page-size="state.queryForm.limit"
      :page-sizes="state.pageSizes"
      :total="state.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <div v-if="state.pageShow === false" class="oper-bar">
      <div>
        <el-checkbox
          v-model="state.checkAll"
          label="批量处理本页事项"
          size="large"
          @change="toggleSelected"
        />
      </div>
      <div>|</div>
      <div>
        <el-select
          v-model="state.params"
          placement="top-start"
          @change="changeMultiHandle"
          style="width: 180px"
        >
          <el-option
            v-for="item in paramsList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>

      <div>&#8594;</div>

      <div>
        <el-select
          v-if="state.params === 'fixed_version_id'"
          v-model="state.changeValue"
          clearable
          style="width: 180px"
        >
          <el-option
            v-for="item in state.projectVersionList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>

        <template v-else-if="state.params === 'priority_id'">
          <IssuePriority
            v-model:value="state.changeValue"
            border
            style="width: 180px"
          />
        </template>

        <!-- <template v-else-if="state.params === 'status_id'">
          <IssueStatus
            v-model:value="state.changeValue"
            border
            is-default
            style="width: 180px"
          />
        </template> -->
        <!-- 这是批量修改状态下面的 -->
        <template v-else-if="state.params === 'status_id'">
          <el-cascader
            v-model="state.changeValue"
            :options="state.workflowStatusList"
            :props="state.statusCascaderPropsSingle"
            clearable
            collapse-tags
            collapse-tags-tooltip
            :show-all-levels="false"
            placeholder="请选择状态"
            style="width: auto"
          />
        </template>

        <template v-else-if="state.params === 'assigned_to_id'">
          <template v-if="project_id">
            <!-- <el-select v-model="state.changeValue" style="width: 160px">
              <el-option
                v-for="item in state.projectMemberList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select> -->
            <el-select
              v-model="state.batchChangeAssigner"
              :class="['multy-issue-' + '-1']"
              placeholder="未选择处理人"
              filterable
              multiple
              collapse-tags
              collapse-tags-tooltip
              remote
              :remote-method="filterUserList"
              style="width: 180px; margin-right: 8px"
              @click="clickSelectAssigned({ id: -1 }, 'multy-issue-')"
              @visible-change="
                (e) => {
                  selectAssigned(e, 'multy-issue-')
                }
              "
            >
              <el-option
                v-for="item in state.projectMemberList"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              >
                {{ item.name }}
              </el-option>
            </el-select>
            <el-button type="primary" @click="patchChangeAssigedId">
              提交
            </el-button>
          </template>
          <template v-else>
            <el-select v-model="state.changeValue" style="width: 160px">
              <el-option
                v-for="item in state.projectMemberList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </template>
        </template>

        <template v-else-if="state.params === 'due_date'">
          <el-date-picker
            v-model="state.changeValue"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder="请选择"
            style="width: 160px"
          />
        </template>

        <template v-else-if="state.params === 'watchers'">
          <el-select v-model="state.changeValue">
            <el-option :value="1" label="是" />
            <el-option :value="0" label="否" />
          </el-select>
        </template>

        <template v-else-if="state.params === 'parent_id'">
          <el-select
            v-model="state.changeValue"
            style="width: 160px"
            filterable
          >
            <el-option
              v-for="item in state.issueList"
              :key="item.id"
              :label="item.subject"
              :value="item.id"
            />
          </el-select>
        </template>

        <template v-else-if="state.params === 'category_id'">
          <el-select
            v-model="state.changeValue"
            style="width: 160px"
            filterable
          >
            <el-option
              v-for="item in state.allUnitList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </template>

        <template v-else-if="state.params === 'class_id'">
          <el-cascader
            v-model="state.changeValue"
            :options="state.issueClassList"
            :show-all-levels="false"
            style="width: 160px"
            :props="{
              value: 'id',
              label: 'name',
              checkStrictly: true,
            }"
          />
        </template>

        <template v-else-if="state.params === 'migration'">
          <el-button type="primary" round @click="handleBatchMigration()">
            进行迁移
          </el-button>
        </template>

        <template v-else-if="state.params === 'copy'">
          <el-button type="primary" round @click="handleBatchCopy()">
            批量复制
          </el-button>
        </template>
      </div>
    </div>

    <!-- <el-dialog
      v-model="state.dialog.visible"
      class="issueEdit-dialog"
      destroy-on-close
      :close-on-click-modal="false"
      top="50px"
      width="80%"
    >
      <template #header>
        <IssueHead :info="state.dialog.form" />
      </template>
      <div>

      </div>
    </el-dialog> -->
    <edit-dialog
      ref="editRef"
      :height="state.dialog.height"
      :open-type="user_settings.issue_open_type"
      @fetch-data="fetchData"
    />
    <edit
      ref="issueCreateRef"
      :info="state.dialog.info"
      :new-issue="true"
      :project-id="project_id"
    />

    <el-drawer
      class="table-header-setting-drawer"
      v-model="state.tableHeaderSettingDrawer"
      :direction="'rtl'"
      :size="'18vw'"
    >
      <template #header>
        <h4>表格显示设置</h4>
      </template>
      <template #default>
        <div class="table-header-setting-form">
          <el-form :model="state.headerSettingForm">
            <el-form-item label-width="100px" label="">
              <div
                style="
                  display: flex;
                  justify-content: flex-end;
                  margin-right: -15px;
                "
              >
                <el-button
                  text
                  type="primary"
                  bg
                  @click="setHeaderSettingForm(true)"
                >
                  恢复当前页面默认
                </el-button>
              </div>
            </el-form-item>
            <div v-for="(column, index) in baseColumns" :key="index">
              <el-form-item
                v-if="!column.notCanDoSwitch"
                :label="column.label"
                :key="column.prop"
                label-width="100px"
              >
                <template #label>
                  <span style="cursor: pointer">{{ column.label }}</span>
                </template>
                <div style="display: flex; justify-content: flex-end">
                  <el-switch
                    v-model="state.headerSettingForm[column.label]"
                    :active-value="true"
                    :inactive-value="false"
                    :disabled="column.notCanDoSwitch"
                  />
                </div>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="headerSettingCancelClick">取消</el-button>
          <el-button type="primary" @click="headerSettingConfirmClick">
            保存
          </el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 批量迁移组件 -->
    <BatchSelectIssueProject
      ref="batchSelectIssueProjectRef"
      :current-project-id="project_id"
      @handle-batch-migration-success="handleBatchMigrationSuccess"
    />
  </div>
</template>

<script setup>
  import {
    doEdit,
    doDelete,
    getList,
    getIssueStatus,
    getMemberList,
    getEnumerationList,
    patchIssueField,
    doWatchersMulti,
    getAllList,
    getCategoryList,
    getIssueType,
    getIssueVersions,
    getProjectCustomFieldList,
  } from '@/api/projectIssue'
  import { queryCategory } from '@/api/category'
  import { queryPersonnel, getRedmineUserList } from '@/api/user'
  import { getList as getProjectList, getUnitList } from '@/api/projectIndex'
  import { getList as getProjectVersionList } from '@/api/projectVersion'
  import { getList as getReleaseVersionList } from '@/api/releaseVersion'
  import { getList as getIssueClassListData } from '~/src/api/projectIssueClass'
  import {
    isCustomWorkflowEnabled,
    getInitialStatuses,
    getProjectStatuses,
  } from '@/api/customWorkflow'
  import { Plus, Search } from '@element-plus/icons-vue'
  import IssueAssign from '@/components/IssueAssign.vue'
  import IssueActionMenu from '@/components/IssueActionMenu.vue'
  import StatusCascaderSelector from '@/components/StatusCascaderSelector.vue'

  import EditDialog from './IssueEditDialog.vue'
  import Edit from '@/views/project/issue/components/IssueEditNew.vue'
  import BatchSelectIssueProject from './BatchSelectIssueProject.vue'
  import _, { cloneDeep } from 'lodash'
  import IssueStatus from '@/components/IssueStatus.vue'
  import IssuePriority from '@/components/IssuePriority.vue'
  import IssueVersion from '@/components/IssueVersion.vue'
  import {
    issueStatusClass,
    priorityTagName,
    priorityTagType,
  } from '@/json/issues'
  import {
    Upload as UploadIcon,
    Refresh as RefreshIcon,
    Plus as PlusIcon,
  } from '@element-plus/icons-vue'
  import {
    IssueIconParent,
    IssueIconChild,
    getTrackerIcon,
  } from '@/utils/index'
  import moment from 'moment'
  import { useUserStore } from '@/store/modules/user'

  import { export_json_to_excel } from '@/utils/excel'

  import CommonIcon from '~/src/components/CommonIcon.vue'
  import CommonEmpty from '~/src/components/CommonEmpty.vue'
  import TrackerTypeTag from '~/src/components/TrackerTypeTag.vue'
  import { hasPermission } from '~/src/utils/permission'
  import PersonnelSelect from '~/src/components/PersonnelSelect.vue'

  import { charAt } from '@/utils/common'

  const buttonRef = ref()
  const button2Ref = ref()
  const popoverRef = ref()
  const switchValue = ref(true)
  const fullscreenContainer = ref(null)

  const $baseConfirm = inject('$baseConfirm')
  const $baseMessage = inject('$baseMessage')
  const $pub = inject('$pub')
  const $sub = inject('$sub')
  const $unsub = inject('$unsub')
  const router = useRouter()
  const route = useRoute()
  const urlProjectId = ref(route.query.project_id || null)

  const editRef = ref(null)
  const issueCreateRef = ref(null)
  const batchSelectIssueProjectRef = ref(null)

  let tmpIssueTableRef = null
  const issueTableRef = ref(null)
  const storeRef = ref(null)
  const unitRef = ref(null)
  /**
   * 列配置数据
   */
  const baseColumns = ref([
    {
      prop: 'subject',
      label: '标题',
      align: 'left',
      fixed: true,
      minWidth: 430,
      showOverflowTooltip: true,
      show: (jud = true) => {
        return jud
      },
      template: 'title', // 使用模板标识
    },
    {
      prop: 'issue_status.name',
      label: '状态',
      align: 'left',
      width: 120,
      show: (jud = true) => state.showCloumn.status && jud, // 动态显示条件
      template: 'status', // 使用模板标识
    },
    {
      prop: 'priority_id',
      label: '优先级',
      align: 'left',
      width: 96,
      sortable: 'custom',
      show: (jud = true) => state.showCloumn.priority && jud, // 动态显示条件
      template: 'priority', // 使用模板标识
    },
    {
      prop: 'assigned_to_id',
      label: '处理人',
      align: 'left',
      width: 254,
      sortable: 'custom',
      showOverflowTooltip: true,
      show: (jud = true) => state.showCloumn.assignedColumn && jud, // 动态显示条件
      template: 'assigned', // 使用模板标识
    },
    {
      prop: 'author_id',
      label: '创建人',
      align: 'left',
      width: 100,
      sortable: 'custom',
      showOverflowTooltip: true,
      show: (jud = false) => {
        return jud
      }, // 动态显示条件
      template: 'author', // 使用模板标识
    },
    {
      prop: 'enumeration.name',
      label: '版本',
      align: 'left',
      width: 110,
      showOverflowTooltip: true,
      show: (jud = true) =>
        state.showCloumn.versionColumn && showCloumnBasisOfProjectType() && jud, // 动态显示条件
      template: 'version', // 使用模板标识
    },
    {
      prop: 'due_date',
      label: '完成日期',
      align: 'left',
      width: 130,
      sortable: 'custom',
      showOverflowTooltip: true,
      show: (jud = true) => state.showCloumn.dueDateColumn && jud, // 动态显示条件
      template: 'dueDate', // 使用模板标识
    },
    {
      prop: 'created_on',
      label: '创建时间',
      align: 'left',
      width: 150,
      sortable: 'custom',
      showOverflowTooltip: true,
      show: (jud = true) => state.showCloumn.createdOnColumn && jud, // 动态显示条件
    },
    {
      prop: 'project_text.name',
      label: '项目',
      align: 'left',
      width: 150,
      showOverflowTooltip: true,
      show: (jud = true) => state.showCloumn.projectColumn && jud, // 动态显示条件
      template: 'project', // 使用模板标识
    },
    {
      prop: '',
      label: '设置按钮',
      align: 'left',
      width: 30,
      show: (jud = true) => {
        return jud
      },
      template: 'setting', // 使用模板标识
      notCanDoSwitch: true,
    },
  ])

  const baseColumnsOld = ref([])
  baseColumnsOld.value = cloneDeep(baseColumns.value)

  /**
   * 过滤后的列配置
   */
  const filteredColumns = computed(() =>
    baseColumns.value.filter((column) => !column.show || column.show())
  )

  /**
   * 触发 headerSetting展示
   *  */
  const headerSetting = () => {
    setHeaderSettingForm()
    state.tableHeaderSettingDrawer = true
  }

  /**
   * 取消 取消表头显示设置抽屉页
   *  */
  const headerSettingCancelClick = () => {
    state.tableHeaderSettingDrawer = false
  }

  /**
   * 保存 表头显示设置抽屉页表单确定
   *  */
  const headerSettingConfirmClick = () => {
    localStorage.setItem(
      route.path + 'issueTableHeaderSettingForm',
      JSON.stringify(state.headerSettingForm)
    )
    setTableActualHeader()
    $baseMessage('修改成功', 'success', 'vab-hey-message-success')
    state.tableHeaderSettingDrawer = false
  }

  /**
   * 设置HeaderSetting 的 Form
   *  */
  const setHeaderSettingForm = (isInit = false) => {
    const savedSettings = localStorage.getItem(
      route.path + 'issueTableHeaderSettingForm'
    )
    if (savedSettings && !isInit) {
      state.headerSettingForm = JSON.parse(savedSettings)
      return
    }

    baseColumnsOld.value.reduce((acc, column) => {
      state.headerSettingForm[column.label] = column.show()
      return acc // 返回累积结果以便下一次迭代使用
    }, {}) // 初始化累积对象
  }

  /**
   * 设置表头渲染
   */
  const setTableActualHeader = () => {
    baseColumns.value.forEach((column) => {
      const isVisible = state.headerSettingForm[column.label]
      column.show = (jud = true) => isVisible && jud
    })
  }

  const userStore = useUserStore()
  let { redmine, user_settings, user_id, roles } = userStore
  let isSelectedHandler = null
  const props = defineProps({
    myself: { type: Boolean, default: false },
    showQuery: { type: Boolean, default: true },
    showCloumn: {
      type: Object,
      default() {
        return {
          indexColumn: false,
          projectColumn: true,
          assignedColumn: true,
          authorColumn: true,
          createdOnColumn: false,
          dueDateColumn: true,
          versionColumn: true,
          releaseVersionColumn: false,
          issueIdColumn: true,
        }
      },
    },
    queryFormFilter: {
      type: Object,
      default() {
        return {}
      },
    },
    isMyCreate: {
      type: Boolean,
      default: false,
    },
    isMyWork: {
      type: Boolean,
      default: false,
    },
    queryFormOp: {
      type: Object,
      default() {
        return {}
      },
    },
    pageSize: {
      type: Number,
      default: 20,
    },
    // 是否显示分页及多选，false表示不显示
    pagination: {
      type: Boolean,
      default: true,
    },

    //表格高度
    height: {
      type: Number,
      default: null,
    },

    border: {
      type: Boolean,
      default: false,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    // 简单模式，隐藏表头，状态归到一列
    simple: {
      type: Boolean,
      default: false,
    },
    isIssueMy: {
      type: Boolean,
      default: false,
    },
    // 是否显示公开问题
    showPublic: {
      type: Boolean,
      default: false,
    },
    // 是否使用记忆状态
    memoryStatus: {
      type: Boolean,
      default: true,
    },
    //是否是测试计划页面引用
    isTestPlanUse: {
      type: Boolean,
      default: false,
    },
    // 传入的filter来源于
    filterFrom: {
      type: String,
      default: '',
    },
  })
  const { isMyWork, queryFormFilter, pageSize } = toRefs(props)
  const project_id = parseInt(queryFormFilter.value.project_id)
  const emit = defineEmits([
    'selection-change',
    'refresh',
    'project-member-got',
  ])

  // 状态大类定义
  const statusCategories = [
    { value: 'not_started', label: '未开始' },
    { value: 'in_progress', label: '进行中' },
    { value: 'awaiting_acceptance', label: '待验收' },
    { value: 'completed', label: '已完成' },
  ]

  // 添加计算属性来判断是否显示项目选择器
  const shouldShowProjectSelector = computed(() => {
    return !urlProjectId.value
  })

  const setTreeLike = () => {
    state.isTreeView = true
    if (state.queryForm.filter.is_tree_like === false) {
      state.queryForm.filter.is_tree_like = true

      fetchData()
    }
  }

  const setTile = () => {
    state.isTreeView = false
    if (state.queryForm.filter.is_tree_like === true) {
      state.queryForm.filter.is_tree_like = false

      fetchData()
    }
  }

  const handleMemberList = async (hasProject = true) => {
    // 存在项目，所有成员列表都获取项目成员列表
    if (project_id && project_id > 0) {
      const {
        data: { data },
      } = await getMemberList({
        limit: 999,
        filter: { project_id: project_id, user: { type: 'User', status: 1 } },
        op: { user: { status: '=' } },
      })
      if (data) {
        state.projectMemberList = [{ id: -1, name: '未指定' }]
        data.forEach((item) => {
          state.projectMemberList.push({
            id: item.user_id,
            name: item.user.lastname + item.user.firstname,
            user: item.user,
          })
        })
      }
    } else {
      // 没有项目时，获取所有
      const res = await getRedmineUserList({
        filter: { status: 1, auth: true },
        limit: 200,
      })
      state.projectMemberList = [{ id: -1, name: '未指定' }, ...res.data.data]
    }
  }

  const handleProject = async (node = null, keyword = null) => {
    let filter = {},
      op = {}
    if (keyword) {
      filter.name = keyword
      op.name = 'LIKE'
    }
    const { data } = await getProjectList(filter, op)
    if (data && data.length > 0) {
      state.projectList = data.filter(
        (item) =>
          !item.projects_ext ||
          item.projects_ext.project_type !== 'product_type'
      )
    }
  }

  const dialogHeight = computed(() => {
    const h2 = document.body.clientHeight * 0.75
    return h2
  })
  let defaultShowUnitList = localStorage.getItem(
    'issueListShowUnitList' + user_id
  )

  const state = reactive({
    subjectForEdit: false, // 标题快速编辑相关
    visiblePopoverIds: [], // 当前显示popover的行ID列表
    hasRequiredCustomFields: false, // 是否有必填自定义字段
    customFieldsLoading: false, // 自定义字段加载状态
    customFieldsError: null, // 自定义字段错误信息
    trackerList: [], // 将从 getIssueTypeList 动态获取
    fixedVersionList: [], // 版本列表
    allJoinedPorjectMemberList: {}, // 以项目id为键，获取各项目成员
    memberListCache: {}, // 缓存项目成员列表，避免重复查询
    headerSettingForm: {},
    tableHeaderSettingDrawer: false, // 表头是否展示的设置drawer
    showCloumn: { ...props.showCloumn }, // 要展示的列
    selectedPriorityIds: [], // 优先级筛选 （多选）已选中的
    selectedTrackerIds: [], // 事项类型筛选 已选中的
    selectedStatusIds: [], // 三层级联状态筛选 已选中的
    selectedStatusCategories: [], // 状态大类筛选 已选中的（保留用于兼容）
    previousStatusId: null, // 保存选择状态大类前的 status_id，用于取消时恢复
    previousStatusOp: null, // 保存选择状态大类前的 status_id 操作符
    workflowStatusList: [], // 工作流配置的状态列表（用于批量修改）
    statusCascaderProps: {
      value: 'value',
      label: 'label',
      children: 'children',
      multiple: true,
      checkStrictly: false,
      emitPath: false,
    },
    issueIds: [], // 所有列表的id
    issueTypeList: [], // 任务类型列表
    tableHeight: props.height,
    sortRule: { prop: 'id', order: 'descending' },
    projectTypeList: [],
    tempTreeLikSetting: null,
    hasPermission: hasPermission(['Admin', 'Manager']) ?? false,
    nowDate: new Date().setHours(0, 0, 0, 0),
    localStorageKey: 'isExpandIssueTreeView_' + redmine.id,
    selectedTags: [],
    unitList: [],
    allUnitList: [],
    issueClassList: [],
    isTreeView: true,
    queryFormTopShow: false,
    visiblePopover: false,
    visibleTreeViewPopover: false,
    defaultExpandAll: false,
    assignedSearch: false,
    isExpand: false,
    isMyWork: isMyWork,
    issueTableRef: null,
    list: [],
    list_backup: [],
    list_all: [],
    listLoading: true,
    statusIsLoad: false,
    isSearch: false,
    layout: 'total, prev, pager, next',
    pageSizes: [15, 30, 50, 100],
    total: 0,
    selectRows: [],
    temp_assigned_to_id: queryFormFilter.value?.assigned_to_id
      ? queryFormFilter.value?.assigned_to_id
      : null,
    queryForm: {
      created_on: null,
      pageNo: 1,
      limit: pageSize,
      title: '',
      filter: {
        project_id: project_id ? [project_id] : [],
        parent_id: 'IS NULL',
        assigned_to_id: null,
        issueAssigned: {
          user_id: null,
        },
        created_on: null,
        is_tree_like: true,
      },
      op: {
        issueAssigned: {
          user_id: '=',
        },
        subject: 'LIKE',
        parent_id: 'IS NULL',
        fixed_version_id: 'IN',
      },
      assigned: null,
    },
    activeName: 'issue',
    showQueryForm: false,
    issueList: [],
    issueStatusList: [],
    enumerationList: [],
    projectMemberList: [], //全局成员
    memberLoading: {}, //项目成员加载状态
    memberPidObj: {}, //根据项目id存放 成员列表，避免重新加载
    allStaff: [], //公司全部成员
    staffProps: {
      value: 'third',
      label: 'name',
      emitPath: false,
      expandTrigger: 'hover',
    },
    projectList: [],
    projectListProps: {
      value: 'id',
      label: 'name',
      emitPath: false,
      expandTrigger: 'hover',
      checkStrictly: false,
      multiple: true,
    },
    projectInfo: null,

    dialog: {
      height: dialogHeight,
      form: null,
      visible: false,
      title: '编辑',
    },
    projectVersionList: [],
    releaseVersionList: [],
    pageShow: computed(() => {
      return state.selectRows.length === 0
    }),
    listMaps: new Map(),
    checkAll: true, //是否全选
    //允许批量操作的字段
    params: 'priority_id',
    // 定义一个数组，包含了一些时间段的快捷选择
    shortcuts: [
      {
        // 快捷选择的文本
        text: '最近一周',

        // 快捷选择的值，返回一个包含起始日期和结束日期的数组
        value: () => {
          return [
            moment().subtract(7, 'days').format('YYYYMMDD'),
            moment().add(1, 'days').format('YYYYMMDD'),
          ]
        },
      },
      {
        text: '最近一个月',
        value: () => {
          return [
            moment().subtract(30, 'days').format('YYYYMMDD'),
            moment().add(1, 'days').format('YYYYMMDD'),
          ]
        },
      },
      {
        text: '最近三个月',
        value: () => {
          return [
            moment().subtract(90, 'days').format('YYYYMMDD'),
            moment().add(1, 'days').format('YYYYMMDD'),
          ]
        },
      },
    ],

    //批量保存可选择的值下拉，动态
    changeValue: '',
    batchChangeAssigner: [], // 批量保存多指派人

    cacheVerListObj: {}, //缓存的版本列表，键为projectid,值为list
    cacheReleaseVerListObj: {}, //缓存的版本列表，键为projectid,值为list
    // height: document.body.scrollHeight - 306,
    showHeader: true,
    outBorder: false,
    followParentImg: null,
    isShowUnit: defaultShowUnitList
      ? defaultShowUnitList == 'show'
        ? true
        : false
      : true, //是否显示模块列表
    unitHeitht: 0,
    selectAssginedClass: ['none-border-on-common'],
    currentSelectAssigned: null,
    // 高度监听相关
    queryFormObserver: null,
    resizeHandler: null,
    selectedProjectVersionIds: [], // 选中的项目版本ID数组
    projectVersionsByProject: {}, // 按项目ID缓存版本列表
  })

  // 计算属性：根据props.simple动态生成批量操作选项
  const paramsList = computed(() => {
    const allParams = [
      {
        label: '完成日期',
        value: 'due_date',
      },
      {
        label: '状态',
        value: 'status_id',
      },
      {
        label: '优先级',
        value: 'priority_id',
      },
      {
        label: '版本',
        value: 'fixed_version_id',
      },
      {
        label: '处理人',
        value: 'assigned_to_id',
      },
      {
        label: '关注',
        value: 'watchers',
      },
      {
        label: '父级任务',
        value: 'parent_id',
      },
      {
        label: '模块',
        value: 'category_id',
      },
      {
        label: '分类',
        value: 'class_id',
      },
      {
        label: '迁移项目',
        value: 'migration',
      },
    ]

    // 如果不是简单模式，添加复制选项
    if (!props.simple) {
      allParams.push({
        label: '复制',
        value: 'copy',
      })
    }

    return allParams
  })

  // localStorage.getItem拿到的数据是字符串类型
  const foldSwitchValue = ref(
    localStorage.getItem(state.localStorageKey) !== null &&
      localStorage.getItem(state.localStorageKey) == 'false'
      ? false
      : true
  )

  // 计算属性：统计"更多筛选"中已选筛选项的数量
  const filterCount = computed(() => {
    let count = 0

    // 1. 事项类型
    if (state.selectedTrackerIds && state.selectedTrackerIds.length > 0) {
      count++
    }

    // 2. 项目
    if (
      state.queryForm.filter.project_id &&
      Array.isArray(state.queryForm.filter.project_id) &&
      state.queryForm.filter.project_id.length > 0
    ) {
      count++
    }

    // 3. 版本
    if (
      state.selectedProjectVersionIds &&
      state.selectedProjectVersionIds.length > 0
    ) {
      count++
    }

    // 4. 优先级
    if (state.selectedPriorityIds && state.selectedPriorityIds.length > 0) {
      count++
    }

    // 5. 创建人
    if (state.queryForm.filter.author_id) {
      count++
    }

    // 6. 创建时间
    if (state.queryForm.created_on && state.queryForm.created_on.length === 2) {
      count++
    }

    // 7. 完成日期
    if (state.queryForm.due_date && state.queryForm.due_date.length === 2) {
      count++
    }

    return count
  })

  let allChild = []
  const getAllChildIssue = (item) => {
    allChild.push(item)
    if (item.hasChildren) {
      let childrenArr =
        issueTableRef.value.store.states.lazyTreeNodeMap.value[item.id]
      if (childrenArr) {
        childrenArr.forEach((it) => {
          getAllChildIssue(it)
        })
      }
    }
    return
  }

  let previousSelection = []
  const setSelectRows = (val) => {
    state.selectRows = val.filter((item) => item.id !== -1)
    if (val.length > 0) {
      state.checkAll = true
    }

    emit('selection-change', val) //选择行数据时通讯父组件，供父组件调用

    // 勾选父事项同时勾选所有子事项
    // 取消选中的行
    const removedRows = previousSelection.filter(
      (prevRow) => !val.includes(prevRow)
    )
    // 新选中的行
    const newSelectedRows = val.filter(
      (row) => !previousSelection.includes(row)
    )

    // 更新前一次的 selection 数组
    previousSelection = [...val]

    if (removedRows.length > 0) {
      allChild = []
      removedRows.forEach((newItem) => {
        if (newItem.hasChildren) {
          getAllChildIssue(newItem)
        }
      })
      if (allChild != undefined && allChild.length > 0) {
        allChild.forEach((item) => {
          issueTableRef.value.toggleRowSelection(item, false)
        })
      }
    }
    if (newSelectedRows.length > 0) {
      allChild = []
      newSelectedRows.forEach((newItem) => {
        if (newItem.hasChildren) {
          getAllChildIssue(newItem)
        }
      })
      if (allChild != undefined && allChild.length > 0) {
        allChild.forEach((item) => {
          issueTableRef.value.toggleRowSelection(item, true)
        })
      }
    }
  }
  ///打开事项方式
  const openIssue = (row) => {
    if (user_settings.issue_open_type == 'dialog') {
      editRef.value.showEdit({ id: row.id }, 'dialog')
    } else if (user_settings.issue_open_type == 'blank') {
      window.open(
        `/#/project/detail?project_id=${row.project_id}&issue_id=${row.id}`
      )
    } else if (user_settings.issue_open_type == 'drawer') {
      editRef.value.showEdit({ id: row.id }, 'drawer')
    }
  }
  let followParentImg = null
  async function findParent(item, row) {
    // 先检查当前项是否是你正在寻找的 parent
    if (item.id === row.parent_id) {
      state.followParentImg = item
      return
    }

    // 检查当前项是否有 children
    if (item.children && item.children.length > 0) {
      // 在 children 中搜索
      for (const child of item.children) {
        await findParent(child, row)

        // 如果找到 parent，则跳出循环
        if (followParentImg) {
          break
        }
      }
    }
  }

  // const getImgByIssueTypeAndExistParent = (row) => {
  //   if (row.parent_id && row.parent_id > 0) {
  //     // let parent = state.list_all.find((item) => item.id == row.parent_id)
  //     state.list_all.forEach((item) => {
  //       findParent(item, row)
  //     })
  //     let parent = followParentImg
  //     if (parent) {
  //       return childIssueIcon[parent.tracker_id] || childIssueIcon[0]
  //     } else {
  //       return getImgByIssueType(row.tracker_id)
  //     }
  //   } else {
  //     return getImgByIssueType(row.tracker_id)
  //   }
  // }

  // ///获取任务图标，现在设定是只有两级，暂只根据parentid判断
  // const getIssueTypeIcon = (row) => {
  //   if (row.parent_id) {
  //     //只要是子issue，都返回父的icon
  //     state.list_all.forEach((item, key) => {
  //       findParent(item, row)
  //     })
  //     if (state.followParentImg) {
  //       state.followParentImg = null
  //       return IssueIconChild[parent.tracker_id] ?? IssueIconParent[11]
  //     } else {
  //       return IssueIconParent[row.tracker_id] ?? IssueIconParent[11]
  //     }
  //   }
  //   return IssueIconParent[row.tracker_id] ?? IssueIconParent[11]
  // }

  /**
   * @description 根据字段更新
   */
  const handleSave = async (row, field, value) => {
    let fieldArr = field.split('.')
    let save = { id: row['id'] }
    if (fieldArr.length == 2) {
      if (typeof row[fieldArr[0]][fieldArr[1]] != 'undefined') {
        if (
          !row[fieldArr[0]][fieldArr[1]] ||
          row[fieldArr[0]][fieldArr[1]] === '' ||
          row[fieldArr[0]][fieldArr[1]] === null
        ) {
          return
        }
        save[fieldArr[0]] = {
          [fieldArr[1]]: value ? value : row[fieldArr[0]][fieldArr[1]],
        }
      }
    } else {
      if (field == 'assigned_to_id' && value == -1) {
        save[field] = ''
      } else {
        save[field] = value ? value : row[field]
      }
    }

    // 修改版本时，默认将处理时间（开始&结束）同步为版本设置时间
    let msg_versionChange = null
    if (field == 'fixed_version_id') {
      let version = state.projectVersionList.find((item) => {
        return item.id == row.fixed_version_id
      })
      if (version) {
        save['start_date'] = version.created_on
        save['due_date'] = version.effective_date
        msg_versionChange =
          version.id == 0
            ? '修改成功'
            : '版本修改成功！已与当前版本同步(开始&完成)日期'
      }
    }
    // if (field == 'fixed_version_id') {
    //   state.listLoading = true
    // }

    doEdit(save)
      .then(() => {
        $baseMessage(
          msg_versionChange ? msg_versionChange : '修改成功',
          'success',
          'vab-hey-message-success'
        )
        // state.listLoading = false
        $pub('issue-edit-success', [row['id']]) //广播其他组件刷新数据
      })
      .finally(() => {
        if (row) {
          partialRefresh(row, field)
        }
      })
  }

  const getIssueTypeList = async () => {
    const { data: trackerType } = await getIssueType({
      filter: { 'projects_trackers.project_id': project_id },
    })
    state.issueTypeList = []
    state.issueTypeList = trackerType

    // 同时更新快速创建的 trackerList
    state.trackerList = trackerType.map((item) => ({
      id: item.id,
      name: item.name,
    }))
  }

  const getProjectInfo = async () => {
    if (project_id) {
      const {
        data: [projectInfo],
      } = await getProjectList({ filter: { id: project_id } })
      state.projectInfo = projectInfo
    }
  }

  /**
   * 任务类型的项目，列表不显示版本列
   */
  const showCloumnBasisOfProjectType = () => {
    if (project_id) {
      if (state.projectInfo?.projects_ext?.project_type == 'task_type') {
        return false
      }
    }
    return true
  }

  /**
   * 部分刷新函数，用于更新指定行的数据。
   * @param {Object} row - 要刷新的行数据对象。
   * @param {string} field - 触发刷新的字段。
   */
  const partialRefresh = async (row, field) => {
    if (!row && !row.id) {
      return
    }
    let rowIndex = issueTableRef.value.data.findIndex(
      (item) => item.id == row.id
    )

    // 子级事项在issueTableRef的所在位置
    let rowIndex2 =
      row.parent_id > 0
        ? issueTableRef.value.store.states.lazyTreeNodeMap.value[
            row.parent_id
          ]?.findIndex((item) => item.id == row.id)
        : -1

    if (rowIndex !== -1 || rowIndex2 !== -1) {
      // 修改的是版本则刷新数据，主要是在规划列表，更改了版本，是否要从该版本列表删除
      if (field == 'fixed_version_id') {
        fetchData()
      } else {
        // 单独替换该行数据
        const response = await getList({ filter: { id: row.id } })
        let data = response.data.data[0]
        Object.assign(
          rowIndex > -1
            ? issueTableRef.value.data[rowIndex]
            : issueTableRef.value.store.states.lazyTreeNodeMap.value[
                row.parent_id
              ][rowIndex2],
          data
        )
      }
    }
    state.listLoading = false
  }

  const notInList = (parentId) => {
    let parentItem = state.issueIds.find((item) => {
      return item == parentId
    })
    return !parentItem && !!user_settings.issue_show_parent_name
  }

  const handleMultiAssign = (row, uid) => {
    if (row.custom_fields_multi_assign_id.includes(uid)) {
      row.custom_fields_multi_assign_id.splice(
        row.custom_fields_multi_assign_id.indexOf(uid)
      )
    } else {
      row.custom_fields_multi_assign_id.push(uid)
    }
    if (row.custom_fields) {
      row.custom_fields.forEach((field) => {
        if (field.id == 10) {
          field.value = row.custom_fields_multi_assign_id
        }
      })
    }
    handleSave(row, 'custom_fields_multi_assign_id')
  }

  /**
   *
   * @param {array} assignIds
   * @param {*} id
   */
  const activeDropdownItem = (assignIds, id) => {
    return assignIds && assignIds.includes(id) ? true : false
  }

  /**
   * @description 跳转至项目详情
   */
  const handletoProjectDetail = (row) => {
    if (row.id)
      router.push({
        path: '/project/Issue',
        query: {
          project_id: row.project_id,
          name: row.project_text.name,
          timestamp: new Date().getTime(), //允许同一个详情页同时打开多次，否则会触发路由被缓存下次无法刷新的bug
        },
      })
    else {
      $baseMessage('请选择一行进行详情页跳转', 'error', 'vab-hey-message-error')
    }
  }

  const handleDelete = (row) => {
    if (row.id) {
      $baseConfirm('你确定要删除当前项吗', null, async () => {
        const { msg } = await doDelete({ ids: row.id })
        $baseMessage(msg, 'success', 'vab-hey-message-success')
        await fetchData()
      })
    } else {
      if (state.selectRows.length > 0) {
        const ids = state.selectRows.map((item) => item.id).join()
        $baseConfirm('你确定要删除选中项吗', null, async () => {
          const { msg } = await doDelete({ ids })
          $baseMessage(msg, 'success', 'vab-hey-message-success')
          await fetchData()
        })
      } else {
        $baseMessage('未选中任何行', 'error', 'vab-hey-message-error')
      }
    }
  }
  const handleSizeChange = (val) => {
    state.queryForm.limit = val
    fetchData()
  }
  const handleCurrentChange = (val) => {
    state.queryForm.pageNo = val
    fetchData()
  }

  /**
   * 监听页数的变化来替换url
   */
  watch(
    () => state.queryForm.pageNo,
    (newVal) => {
      route.query.pageNo = newVal + ''
      replaceUrl()
    },
    true
  )

  /**
   * 版本选择后，重置页码
   */
  watch(
    () => state.queryForm.filter.fixed_version_id,
    () => {
      state.queryForm.pageNo = 1
    },
    {
      immediate: true,
    }
  )

  /**
   * 任务类型选择后（多选），要做的预处理
   */
  const selectedTrackerIdHandler = () => {
    let tmp = state.selectedTrackerIds.join(',')
    if (tmp === '') {
      // 取消筛选时，删除tracker_id相关参数，避免空字符串查询
      delete state.queryForm.filter.tracker_id
      delete state.queryForm.op.tracker_id
    } else {
      state.queryForm.filter.tracker_id = tmp
      state.queryForm.op.tracker_id = 'IN'
    }
    // 直接调用fetchData，避免影响isSelectedHandler
    fetchData()
  }

  /**
   * 优先级筛选多选后的预处理
   */
  const selectedPriorityHandler = () => {
    let tmp =
      state.selectedPriorityIds != null
        ? Array.from(state.selectedPriorityIds).join(',')
        : null
    if (tmp === null || tmp === '') {
      // 取消筛选时，删除priority_id相关参数
      delete state.queryForm.filter.priority_id
      delete state.queryForm.op.priority_id
    } else {
      state.queryForm.filter.priority_id = tmp
      state.queryForm.op.priority_id = 'IN'
    }
    // 直接调用fetchData，避免影响isSelectedHandler
    fetchData()
  }

  /**
   * 三层级联状态筛选变化处理
   */
  const handleStatusChange = (selectedStatusIds) => {
    state.selectedStatusIds = selectedStatusIds
    updateCombinedStatusFilter()
  }

  /**
   * 获取状态存储键名
   */
  const getStatusStorageKey = () => {
    if (props.memoryStatus) {
      return project_id
        ? `projectIssueTableStatusSelected_${project_id}`
        : 'myworkIssueTableStatusSelected_'
    }
    return null
  }

  /**
   * 获取要显示的事项类型
   */
  const getShowTrackerTypes = () => {
    if (props.filterFrom == 'issueClass') {
      return ['requirement'] // 需求页面只显示需求筛选
    }
    return ['requirement', 'bug', 'task'] // 默认显示所有类型
  }

  /**
   * 状态大类筛选变化处理（个人工作台使用）
   */
  const handleStatusCategoryChange = (selectedCategories) => {
    state.selectedStatusCategories = selectedCategories

    if (selectedCategories && selectedCategories.length > 0) {
      // 保存当前的状态筛选，以便取消时恢复
      state.previousStatusId = state.queryForm.filter.status_id
      state.previousStatusOp = state.queryForm.op.status_id

      // 设置状态大类筛选条件，后端会根据 status_categories 查询对应的具体状态
      state.queryForm.filter.status_categories = selectedCategories.join(',')
      state.queryForm.op.status_categories = 'IN'

      // 清除具体状态筛选，避免冲突
      delete state.queryForm.filter.status_id
      delete state.queryForm.op.status_id
    } else {
      // 清除状态大类筛选
      delete state.queryForm.filter.status_categories
      delete state.queryForm.op.status_categories

      // 恢复之前保存的状态筛选
      if (state.previousStatusId) {
        state.queryForm.filter.status_id = state.previousStatusId
        state.queryForm.op.status_id = state.previousStatusOp

        // 清除保存的状态
        state.previousStatusId = null
        state.previousStatusOp = null
      }
    }

    queryDataSearch('status_category_selected')
  }

  /**
   * 更新合并的状态筛选条件
   */
  const updateCombinedStatusFilter = () => {
    // 如果有状态大类筛选，则不处理具体状态筛选，避免冲突
    if (
      state.selectedStatusCategories &&
      state.selectedStatusCategories.length > 0
    ) {
      return
    }

    // 使用新的三层级联选择器的结果
    const allSelectedStatusIds = state.selectedStatusIds || []

    if (allSelectedStatusIds.length > 0) {
      state.queryForm.filter.status_id = allSelectedStatusIds.join(',')
      state.queryForm.op.status_id = 'IN'
    } else {
      state.queryForm.filter.status_id = null
      delete state.queryForm.op.status_id
    }
    queryDataSearch('status_selected')
  }

  // 依据处理人作筛选
  const queryDataHandler = async () => {
    state.queryForm.pageNo = 1
    // 清空缓存
    issueTableRef.value.store.states.treeData = {}

    issueTableRef.value.store.states.treeData = tmpIssueTableRef

    state.defaultExpandAll = true
    // 只有在真正有处理人筛选时才设置isSelectedHandler
    if (
      state.queryForm.filter.assigned_to_id &&
      state.queryForm.filter.assigned_to_id !== ''
    ) {
      isSelectedHandler = true
    } else {
      isSelectedHandler = null
    }
    state.list_backup = []

    if (state.queryForm.assigned == '' || state.queryForm.filter.author == '') {
      isSelectedHandler = null
    }

    if (state.queryForm.assigned == '') {
      delete state.queryForm.filter.issueAssigned['user_id']
      delete state.queryForm.op.issueAssigned['user_id']
    }

    if (state.changeAttrTimer && state.changeAttrTimer !== null) {
      clearTimeout(state.changeAttrTimer)
      state.changeAttrTimer = null
    }
    state.changeAttrTimer = setTimeout(async () => {
      await fetchData()
      // 筛选条件时广播到父组件，重新触发统计待规则事项数量
      $pub('fetch-no-plan-issue-count', state.queryForm)
    }, 850)
  }

  // 去重
  const filteredDataList = (data) => {
    // 创建一个 Set 用于存储已经出现过的 id
    const seenIds = new Set()

    // 创建一个新的数组，用于存储处理后的数据
    const filteredData = []

    // 使用 forEach 循环遍历数据数组
    data.data.forEach((item) => {
      // 遍历当前项的 children 数组
      item.children.forEach((child) => {
        // 检查子项的 id 是否已经出现过 如果没有出现过，将子项的 id 添加到 seenIds 集合中
        if (!seenIds.has(child.id)) {
          seenIds.add(child.id)
        }
      })
    })
    // 使用 forEach 循环遍历数据数组
    data.data.forEach((item) => {
      let hasDuplicateInChildren = false

      if (seenIds.has(item.id)) {
        hasDuplicateInChildren = true
      }

      // 如果当前项不在 children 数组中出现过，将其添加到 filteredData 中
      if (!hasDuplicateInChildren) {
        filteredData.push(item)
      }
    })
    return new Proxy(filteredData, {})
  }

  /**
   * 模块（分类）按钮是否已被选中判断
   */
  const isSelectedTag = (tagItem) => {
    return state.selectedTags.includes(tagItem.id)
  }

  /**
   * 模块（分类）按钮被选中处理
   */
  const toggleSelectionTag = async (cItem) => {
    const index = state.selectedTags.indexOf(cItem.id)
    if (index === -1) {
      state.selectedTags.push(cItem.id)
    } else {
      state.selectedTags.splice(index, 1)
    }
    state.queryForm.filter.category_id = Object.values(state.selectedTags).join(
      ','
    )
    state.queryForm.op.category_id = 'in'
    if (state.selectedTags.length === 0) {
      state.queryForm.filter.category_id = null
      state.queryForm.op.category_id = null
    }
    queryDataSearch('search_category')
  }

  /**
   * 事项列表 我的事项与项目详情里的事项列表分别本地存储筛选后的事项状态
   * 注意：状态筛选的存储现在由StatusCascaderSelector组件自动处理
   */
  const statusIdSave = async () => {
    const projectId = route.query.project_id
    if (props.memoryStatus) {
      // 只保存状态大类（兼容旧功能）
      const statusCategoriesToSave =
        state.selectedStatusCategories.length > 0
          ? state.selectedStatusCategories.join(',')
          : ''

      if (projectId && props.memoryStatus) {
        localStorage.setItem(
          `projectIssueTableStatusCategoriesSelected_${projectId}`,
          statusCategoriesToSave
        )
        // 状态筛选的存储由StatusCascaderSelector组件处理，这里不再重复存储
      } else {
        localStorage.setItem(
          'myworkIssueTableStatusCategoriesSelected_',
          statusCategoriesToSave
        )
        // 状态筛选的存储由StatusCascaderSelector组件处理，这里不再重复存储
      }
    }
    const formData = Object.assign({}, state.queryForm)
    $pub('fetch-no-plan-issue-count', formData)
  }

  /**
   * 使用enter搜查标题
   */
  const querySubjectUseEnter = async () => {
    const t = state.queryForm.filter.subject.replace(/\s/, '')
    let text2 = null
    if (/^#\d+$/.test(t)) {
      text2 = t.substring(1)
    } else if (/^\d{1,7}$/.test(t)) {
      // 匹配小于7位的数字 即事项编号
      text2 = t
    } else {
      return
    }

    let formRule = _.cloneDeep(state.queryForm)
    formRule.filter.subject = null
    formRule.filter.id = parseInt(text2)

    const response = await getList(formRule)
    let data = response.data.data
    if (data.length == 0 || !data) {
      return
    }
    // state.list = [...data, ...state.list]
    data = data[0]
    let routeUrl = router.resolve({
      path: '/project/detail',
      query: { issue_id: data.id, project_id: data.project_id },
    })
    window.open(routeUrl.href, '_blank')
  }

  // 处理项目选择变化
  const handleProjectChange = async () => {
    // 清空之前选择的版本
    state.selectedProjectVersionIds = []
    state.queryForm.filter.fixed_version_id = null

    // 如果有选择项目，则获取对应的版本列表
    if (
      state.queryForm.filter.project_id &&
      state.queryForm.filter.project_id.length > 0
    ) {
      await handleProjectVersionListByProjects()
    } else {
      state.projectVersionList = []
    }

    // 执行搜索
    queryDataSearch()
  }

  const queryDataSearch = async (v = 0, key = null) => {
    // 设置 pageNo 和重置列表
    state.queryForm.pageNo = 1
    state.list_backup = []

    if (
      state.queryForm.filter.subject !== undefined &&
      state.queryForm.filter.subject !== null &&
      state.queryForm.filter.subject !== ''
    ) {
      state.isSearch = true
      if (state.tempTreeLikSetting == null) {
        state.tempTreeLikSetting = state.queryForm.filter.is_tree_like
      }
      state.queryForm.filter.is_tree_like = false
    } else {
      if (state.tempTreeLikSetting != null) {
        state.queryForm.filter.is_tree_like = state.tempTreeLikSetting
      }
      state.isSearch = false
    }
    // 如果没有输入关键词，直接进行查询
    if (state.changeAttrTimer) {
      clearTimeout(state.changeAttrTimer)
      state.changeAttrTimer = null
    }
    if (v == 'status_selected' || v == 'search_subject') {
      state.queryForm.filter.status_selected = true
    } else {
      state.queryForm.filter.status_selected = null
    }
    if (v == 'status_selected') {
      statusIdSave()
    }
    state.changeAttrTimer = setTimeout(async () => {
      // fetchData2()
      state.listLoading = true

      await fetchDataFromSearch()
    }, 850)
  }

  const fetchDataFromSearch = async () => {
    // 日期条件筛选
    state.queryForm.filter.created_on =
      state.queryForm.created_on && state.queryForm.created_on.length > 1
        ? `${moment(state.queryForm.created_on[0]).format(
            'YYYY-MM-DD 00:00:00'
          )} - ${moment(state.queryForm.created_on[1]).format(
            'YYYY-MM-DD 23:59:59'
          )}`
        : null
    state.queryForm.filter.due_date =
      state.queryForm.due_date && state.queryForm.due_date.length > 1
        ? `${moment(state.queryForm.due_date[0]).format(
            'YYYY-MM-DD'
          )} - ${moment(state.queryForm.due_date[1]).format('YYYY-MM-DD')}`
        : null

    // 重置需要清空的属性
    if (state.queryForm.filter.created_on) {
      state.queryForm.op.created_on = 'DATETIME'
    }

    if (state.queryForm.filter.due_date) {
      state.queryForm.op.due_date = 'DATE'
    }

    state.queryForm.filter.parent_id = null
    state.queryForm.op.parent_id = null
    // if (!isSelectedHandler) {
    //   // 我的事项 搜索时候不要清空默认处理人
    //   if (!props.isMyWork && !props.isMyCreate) {
    //     state.queryForm.assigned = null
    //   }
    // }

    await fetchData()
  }

  // 根据选择的项目获取版本列表
  const handleProjectVersionListByProjects = async () => {
    const projectIds = state.queryForm.filter.project_id
    if (!projectIds || projectIds.length === 0) {
      state.projectVersionList = []
      return
    }

    try {
      const {
        data: { data },
      } = await getProjectVersionList({
        limit: 100,
        filter: {
          project_id: projectIds.join(','), // 多个项目ID用逗号分隔
          // show_default: 1,
          // 'versions.status': 'open,unstarted',
        },
        op: {
          project_id: 'IN', // 使用IN操作符支持多个项目
          // 'versions.status': 'in',
        },
      })
      state.projectVersionList = data || []
    } catch (error) {
      state.projectVersionList = []
    }
  }

  const getProjectName = (projectId) => {
    // 如果路由中有project_id参数 不需要显示项目名称
    if (route.query.project_id) {
      return ''
    }

    // 如果是待规划版本（ID为0），返回空字符串
    if (projectId == 0) {
      return ''
    }

    for (const project of state.projectList) {
      if (project.id == projectId) {
        return project.name + ' - ' || '未知项目 - '
      }

      if (project.children && Array.isArray(project.children)) {
        for (const child of project.children) {
          if (child.id == projectId) {
            return child.name + ' - ' || '未知项目 - '
          }
        }
      }
    }

    return '未知项目'
  }

  // 处理版本选择变化
  const selectedProjectVersionHandler = () => {
    const currentSelections = [...state.selectedProjectVersionIds]
    const hasWaitingPlan = currentSelections.includes(0)
    const otherVersions = currentSelections.filter((id) => id !== 0)

    if (hasWaitingPlan && otherVersions.length > 0) {
      // 用户同时选择了待规划和其他版本
      // 检查是否是新增的待规划版本
      const previousSelections = state.queryForm.filter.fixed_version_id
      const wasWaitingPlanSelected = previousSelections === 'IS NULL'

      if (!wasWaitingPlanSelected) {
        // 之前没有选择待规划，现在选择了待规划，清除其他版本
        state.selectedProjectVersionIds = [0]
        state.queryForm.filter.fixed_version_id = 'IS NULL'
        state.queryForm.op.fixed_version_id = 'IS NULL'
      } else {
        // 之前选择了待规划，现在选择了其他版本，清除待规划
        state.selectedProjectVersionIds = otherVersions
        state.queryForm.filter.fixed_version_id = otherVersions
        state.queryForm.op.fixed_version_id = null
      }
    } else if (hasWaitingPlan) {
      // 只选择待规划
      state.queryForm.filter.fixed_version_id = 'IS NULL'
      state.queryForm.op.fixed_version_id = 'IS NULL'
    } else if (otherVersions.length > 0) {
      // 只选择具体版本
      state.queryForm.filter.fixed_version_id = otherVersions
      state.queryForm.op.fixed_version_id = null
    } else {
      // 没有选择
      state.queryForm.filter.fixed_version_id = null
      state.queryForm.op.fixed_version_id = null
    }

    queryDataSearch('version_selected')
  }

  // // 平铺视图下是否获取子事项
  // if (!state.isTreeView) {
  //   state.queryForm.filter.parent_id = switchValue.value ? null : 'IS NULL'
  //   state.queryForm.op.parent_id = switchValue.value ? null : 'IS NULL'
  // }
  let _expandTimer //展开子事项
  const fetchData = async (showLoading = true) => {
    quicklyCreating.value = false
    await nextTick()
    if (showLoading) {
      state.listLoading = true
    }

    state.queryForm.filter.is_my_work = props.isMyWork ? 1 : null

    // 树形列表以父事项为分页基准
    const shouldResetParentId =
      state.isSearch || // 按照标题搜索的时候
      props.isMyWork || // 我的事项也需要显示子工作项
      props.isMyCreate ||
      (state.queryForm.filter.watcher &&
        state.queryForm.filter.watcher.watchable_type == 'Issue') || // 我的关注 要显示子事项
      isSelectedHandler || // 选择了事项处理人后置null
      (!state.isTreeView && switchValue.value) || // 平铺视图下是否获取子事项
      state.queryForm.filter.subject // 搜索标题时需要搜索子事项
    state.queryForm.filter.parent_id = shouldResetParentId ? null : 'IS NULL'
    state.queryForm.op.parent_id = shouldResetParentId ? null : 'IS NULL'

    setSortForWatcher()

    const formData = _.cloneDeep(state.queryForm)
    // //取消下拉选择时，如果不设置为null，字段值会变成空字符串，导致找不出记录
    if (!formData.filter.status_id) {
      delete formData.filter.status_id
    }
    if (!formData.filter.project_id) {
      delete formData.filter.project_id
    }

    // 旧属性，直接删除
    if (formData.filter.assigned_to_id) {
      delete formData.filter.assigned_to_id
    }

    if (formData.assigned) {
      formData.filter.issueAssigned = {}
      formData.op.issueAssigned = { user_id: '=' }
      formData.filter.issueAssigned['user_id'] = formData.assigned
      formData.op.issueAssigned['user_id'] = '='
    } else {
      delete formData.filter.issueAssigned
    }

    delete formData.filter.pageNo //加这个会出错
    //级联选择框，值是数组，获取最后一项值
    // const assigned_to_id = formData.assigned
    // if (Array.isArray(assigned_to_id)) {
    //   formData.assigned = assigned_to_id[assigned_to_id.length - 1]
    // }

    // 处理 project_id 多选
    if (
      formData.filter.project_id &&
      Array.isArray(formData.filter.project_id)
    ) {
      if (formData.filter.project_id.length > 0) {
        formData.filter.project_id = formData.filter.project_id.join(',')
        formData.op.project_id = 'IN'
      } else {
        delete formData.filter.project_id
        delete formData.op.project_id
      }
    }

    // 处理 fixed_version_id 多选
    if (
      Array.isArray(formData.filter.fixed_version_id) &&
      formData.filter.fixed_version_id.length > 0
    ) {
      formData.filter.fixed_version_id =
        formData.filter.fixed_version_id.join(',')
      formData.op.fixed_version_id = 'IN'
    } else if (
      Array.isArray(formData.filter.fixed_version_id) &&
      formData.filter.fixed_version_id.length === 0
    ) {
      delete formData.filter.fixed_version_id
      delete formData.op.fixed_version_id
    }

    // 清除之前的计时器
    clearTimeout(state.changeAttrTimer)
    state.changeAttrTimer = null

    state.listMaps = new Map()
    state.list = []
    state.list_backup = [] // 需要清空，否则会传给eltable的tree组件多余执行报错

    // 查询模块列表时没有显确的需求时不需要有parent_id
    // let cateFormData = Object.assign({}, formData)
    let cateFormData = _.cloneDeep(formData)
    //测试计划使用会传入计划id来筛选，数据表没有该属性，需要删除
    if (props.isTestPlanUse) {
      delete cateFormData.filter.plan_id
      formData.filter.parent_id = null
      formData.filter.is_tree_like = false
    }
    if (
      typeof cateFormData.filter.parent_id != 'undefined' &&
      cateFormData.filter.parent_id == 'IS NULL'
    ) {
      delete cateFormData.filter.parent_id
    }

    if (
      props.simple &&
      (state.queryForm.filter.watcher || state.queryForm.sort == 'watch_timing')
    ) {
      formData.sort =
        state.queryForm.filter.watcher &&
        state.queryForm.filter.watcher.watchable_type == 'Issue'
          ? formData.sort
          : null
      delete cateFormData.sort
    }

    getCategoryList(cateFormData).then((res) => {
      state.unitList = res.data
    })

    state.changeAttrTimer = setTimeout(async () => {
      const response = await getList(formData)
      if (response !== undefined) {
        state.issueIds = state.isTreeView ? getIssueIds(response.data.data) : []
        let list = transformListData(response.data.data)
        state.list = list
        state.list_all = cloneDeep(state.list)
        state.total = response.data.total
      }

      // 测试计划中的事项列表
      if (props.isTestPlanUse || props.simple) {
        getAllJoinedPorjectMemberList()
      }

      if (props.showPublic) {
        // 是否载入公开问题
        let query = {
          filter: {
            issueExt: { is_public: 1 },
            status_id: '3,4,13',
          },
          op: {
            issueExt: { is_public: '=' },
            status_id: 'IN',
          },
          limit: process.env.VUE_CNF_ISSUE_PUBLIC_NUM ?? 3,
          sort: 'created_on',
          order: 'desc',
        }
        const publicList = await getList(query)
        if (
          publicList.data &&
          publicList.data.data &&
          publicList.data.data.length > 0
        ) {
          let pList = handleList(publicList.data.data)
          state.list = [...pList, ...state.list]
        }
      }

      state.listLoading = false
      state.changeAttrTimer = null
      state.isExpand = true
    }, 150)

    setTimeout(() => {
      state.isExpand = false
    }, 70)
  }

  /**
   * 为关注列表设置默认以关注时间（行数据自增id）排序事项列表
   */
  const setSortForWatcher = () => {
    if (
      state.queryForm.filter.watcher &&
      state.queryForm.filter.watcher.watchable_type == 'Issue' &&
      (!state.queryForm.sort || state.queryForm.sort == 'id')
    ) {
      state.queryForm.sort = 'watch_timing'
    }
  }

  /**
   * 迭代的方式获取事项所有id
   * @param {Array} list - 事项列表
   * @returns {Array} - 所有事项的id
   */
  const getIssueIds = (list) => {
    const ids = []
    const stack = [...list]

    while (stack.length > 0) {
      const item = stack.pop()
      ids.push(item.id)

      if (item.children) {
        stack.push(...item.children)
      }
    }

    return ids
  }

  /**
   * 重置选项
   */
  const handleRefreshOptions = () => {
    state.selectedTags = []
    state.isTreeView = true
    switchValue.value = true
    state.queryForm.created_on = null
    state.queryForm.filter = Object.assign({}, props.queryFormFilter)
    state.queryForm.op = Object.assign({}, props.queryFormOp)
    state.queryForm.filter['priority_id'] = null
    state.queryForm.filter['is_tree_like'] = true
    isSelectedHandler = null
    fetchData()
  }

  const markHasChildren = (items) => {
    items.forEach((item) => {
      if (!isEmpty(item['children'])) {
        item['hasChildren'] = true
        markHasChildren(item['children'])
        state.list_backup[[item['id']]] = item['children']
        delete item['children']
      } else {
        state.list_backup[[item['id']]] = item['children']
        delete item['hasChildren']
        delete item['children']
      }
    })
  }

  const transformListData = (list) => {
    if (list) {
      // 如果需要处理列表数据在这里面处理
      list = handleList(list)
      markHasChildren(list)
      return list
    }
  }

  /**
   *
   * @param {*} list
   * @param {*} level
   */
  const handleList = (list, level = 1, parentRow = null) => {
    list.forEach((item) => {
      if (level == 1) {
        // 顶级事项使用 parent 图标，支持 tracker_type 和 tracker_id
        const trackerInfo = {
          tracker_type: item.issue_type?.tracker_type,
          tracker_id: item.tracker_id,
        }
        item['icon'] = getTrackerIcon(trackerInfo, 'parent')
        item['is_top'] = true
      } else {
        // 子级事项使用 child 图标，基于自己的 tracker 信息
        const childTrackerInfo = {
          tracker_type: item.issue_type?.tracker_type,
          tracker_id: item.tracker_id,
        }
        item['icon'] = getTrackerIcon(childTrackerInfo, 'child')
        item['is_top'] = false
      }
      if (item['children']) {
        item['top_tracker_id'] = parentRow
          ? parentRow['top_tracker_id']
          : item['tracker_id']
        item['children'] = handleList(item['children'], level + 1, item)
      }
    })
    return list
  }

  function isEmpty(array) {
    if (Array.isArray(array)) {
      return array.length === 0
    }
    return !array
  }

  // const maps = new Map()
  // 懒加载load
  const loadChildren = (row, treeNode, resolve) => {
    try {
      if (
        state.isSearch ||
        typeof row.hasChildren == 'undefined' ||
        row.hasChildren != true
      ) {
        // 手动删除懒加载最后一个元素
        issueTableRef.value.store.states.lazyTreeNodeMap.value[row.id] = []
        resolve([])
        return
      }
      treeNode.loaded = false
      const parent_id = row.id
      state.listMaps.set(parent_id, { row, treeNode, resolve })
      if (
        state.list_backup[row.id] == undefined ||
        state.list_backup[row.id].length == 0
      ) {
        issueTableRef.value.store.states.lazyTreeNodeMap.value[row.id] = []
        delete row.hasChildren
        resolve([])
        return
      }
      // setTimeout(() => {
      // }, 0)

      // 如果默认打开子事项列表则不设置延时
      if (foldSwitchValue.value) {
        resolve(state.list_backup[row.id])
        return
      } else {
        setTimeout(() => {
          resolve(state.list_backup[row.id])
        }, 0)
        return
      }
    } catch (e) {
      console.log(e)
    }
  }

  const classObj = issueStatusClass

  const handleIssueStatusList = async () => {
    let data = []

    // 筛选器需要显示项目中所有可能的状态（包括自定义状态）
    if (project_id) {
      try {
        // 直接调用 custom-workflow/statuses 获取项目的所有状态
        const statusResult = await getProjectStatuses({
          project_id: project_id,
        })
        data = statusResult.data || []
      } catch (error) {
        // 出错时使用默认状态
        const result = await getIssueStatus()
        data = result.data || []
      }
    } else {
      // 没有项目ID时使用默认状态
      const result = await getIssueStatus()
      data = result.data || []
    }

    state.issueStatusList.length = 0

    data.forEach((item) => {
      const statusItem = {
        label: item.name,
        value: item.id,
        className: classObj[item.id],
        status_category: item.status_category, // 添加 status_category 字段
        tracker_type: item.tracker_type, // 添加 tracker_type 字段
      }
      state.issueStatusList.push(statusItem)
    })
    buildStatusHierarchy()
  }

  // 构建状态层级数据（基于后端 status_category 字段动态分组）
  const buildStatusHierarchy = () => {
    // 状态分组配置（基于 status_category 字段）
    const statusGroups = [
      {
        value: 'not_started',
        label: '未开始',
        category: 'not_started',
      },
      {
        value: 'in_progress',
        label: '进行中',
        category: 'in_progress',
      },
      {
        value: 'awaiting_acceptance',
        label: '待验收',
        category: 'awaiting_acceptance',
      },
      {
        value: 'completed',
        label: '已完成',
        category: 'completed',
      },
    ]

    // 根据 status_category 字段进行动态分组
    const groupedStatuses = statusGroups.map((group) => {
      const matchedStatuses = state.issueStatusList.filter((item) => {
        // 优先使用 status_category 字段，如果没有则使用兜底逻辑
        if (item.status_category) {
          return item.status_category === group.category
        }

        // 兜底逻辑：为没有 status_category 的旧数据提供默认分组
        return getDefaultCategoryForStatus(item.value) === group.category
      })

      return {
        value: group.value,
        label: group.label,
        children: matchedStatuses.map((item) => ({
          value: item.value,
          label: item.label,
          className: item.className,
        })),
      }
    })

    // 处理没有匹配到任何分组的状态（兜底逻辑）
    const allGroupedStatusIds = groupedStatuses.flatMap((group) =>
      group.children.map((child) => child.value)
    )
    const ungroupedStatuses = state.issueStatusList.filter(
      (item) => !allGroupedStatusIds.includes(item.value)
    )

    // 如果有未分组的状态，创建"其他"分组
    if (ungroupedStatuses.length > 0) {
      groupedStatuses.push({
        value: 'other',
        label: '其他',
        children: ungroupedStatuses.map((item) => ({
          value: item.value,
          label: item.label,
          className: item.className || 'status-custom',
        })),
      })
    }

    // 过滤掉空的分组
    state.issueStatusHierarchy = groupedStatuses.filter(
      (group) => group.children.length > 0
    )
  }

  // 兜底逻辑：为没有 status_category 的状态提供默认分类
  const getDefaultCategoryForStatus = (statusId) => {
    // 基于原有的硬编码规则作为兜底
    const notStartedIds = [1, 11]
    const inProgressIds = [2, 7, 6, 9, 10, 14, 12]
    const completedIds = [3, 4, 5, 13]

    if (notStartedIds.includes(statusId)) return 'not_started'
    if (inProgressIds.includes(statusId)) return 'in_progress'
    if (completedIds.includes(statusId)) return 'completed'

    // 默认归类到进行中
    return 'in_progress'
  }

  // 通用的状态层级构建函数（不分组，直接显示所有状态）
  const buildStatusHierarchyByType = (statusList) => {
    // 直接返回状态列表，不进行分组
    return statusList.map((item) => ({
      value: item.value,
      label: item.label,
      className: item.className,
    }))
  }

  const handleProjectVersionList = async () => {
    const {
      data: { data },
    } = await getProjectVersionList({
      limit: 100,
      filter: {
        project_id: project_id,
        show_default: 1,
        'versions.status': 'open,unstarted',
      },
      op: {
        'versions.status': 'in',
      },
    })
    state.projectVersionList = data
  }

  /**
   * 获取参与的项目的版本列表汇总
   */
  const handleIssueVersions = async () => {
    const { data } = await getIssueVersions()
    state.fixedVersionList = data
  }

  const fetchReleaseVersionList = async () => {
    const {
      data: { data },
    } = await getReleaseVersionList({
      limit: 100,
      filter: {
        project_id: project_id,
        status: 'open',
      },
    })
    state.releaseVersionList = data
  }

  const handleEnumerationList = async () => {
    const { data } = await getEnumerationList()
    state.enumerationList = data
  }

  const cancelVersion = (row) => {
    if (row.fixed_version_id) {
      row.fixed_version_id = 0
    }
    handleSave(row, 'fixed_version_id', 0)
  }

  queryPersonnel().then((res) => {
    state.allStaff = res.data
  })

  //切换全选
  const toggleSelected = (v) => {
    if (
      state.selectRows.length > 0 &&
      state.selectRows.length < state.list.length
    ) {
      state.checkAll = !v //相当于不对checkbox进行状态更改
    }
    issueTableRef.value.toggleAllSelection()
  }

  /**
   * 获取顶级任务列表
   */
  const loadParentIssueList = async (projectId) => {
    const { data } = await getList({
      filter: {
        id: props.issueId,
        project_id: projectId ?? props.projectId,
        // parent_id: 'IS NULL',
      },
      op: {
        // parent_id: 'IS NULL',
        id: '<>',
        just_need_issue_pure_list: 'yes',
      },
      limit: 9999,
    }).catch(() => {
      return { data: null }
    })
    if (typeof data.data != 'undefined') {
      state.parentIssueList = data.data
      state.parentIssueListCopy = data.data
    }
  }

  const getParentIssueList = async () => {
    state.issueList.length = 0

    const { data } = await getList({
      filter: {
        project_id: project_id,
        parent_id: 'IS NULL',
      },
      op: {
        parent_id: 'IS NULL',
        // id: '<>',
        just_need_issue_pure_list: 'yes',
      },
      limit: 9999,
    }).catch(() => {
      return { data: null }
    })

    if (typeof data.data != 'undefined') {
      state.issueList = data.data
      // 在数组的开头插入元素
      state.issueList.unshift({
        subject: '【取消所选中事项父任务关系】',
        id: '',
      })
    }

    // getAllList({
    //   filter: { project_id: project_id, parent_id: 'IS NULL' },
    // }).then((res) => {
    //   state.issueList = res.data
    //   // 在数组的开头插入元素
    //   state.issueList.unshift({
    //     subject: '【取消所选中事项父任务关系】',
    //     id: '',
    //   })
    // })
  }

  // 检查选中事项是否属于同一工作流
  const checkSameWorkflow = () => {
    if (!state.selectRows.length) return true

    const firstItem = state.selectRows[0]
    const firstProjectId = firstItem.project_id
    const firstTrackerId = firstItem.tracker_id

    return state.selectRows.every(
      (item) =>
        item.project_id === firstProjectId && item.tracker_id === firstTrackerId
    )
  }

  // 获取当前事项类型的所有可用状态（使用与新建事项相同的逻辑）
  const loadTrackerConfiguredStatuses = async () => {
    try {
      const firstItem = state.selectRows[0]
      const projectId = firstItem.project_id
      const trackerId = firstItem.tracker_id

      // 使用getInitialStatuses获取所有可用状态（与新建事项时保持一致）
      const response = await getInitialStatuses({
        project_id: projectId,
        tracker_id: trackerId,
      })

      const availableStatuses = response.data || []

      if (!Array.isArray(availableStatuses)) {
        $baseMessage('状态数据格式错误', 'error')
        state.params = ''
        return
      }

      if (availableStatuses.length === 0) {
        $baseMessage('暂无可用状态', 'warning')
        state.params = ''
        return
      }

      // 构建工作流状态列表（按status_category分组）
      state.workflowStatusList = buildWorkflowStatusHierarchy(availableStatuses)
    } catch (error) {
      console.error('获取可用状态失败:', error)
      $baseMessage('获取可用状态失败', 'error')
      state.params = ''
    }
  }

  // 构建工作流状态层级数据（复用筛选状态的分组逻辑）
  const buildWorkflowStatusHierarchy = (configuredStatuses) => {
    // 状态分组配置（与筛选状态保持一致）
    const statusGroups = [
      {
        value: 'not_started',
        label: '未开始',
        category: 'not_started',
      },
      {
        value: 'in_progress',
        label: '进行中',
        category: 'in_progress',
      },
      {
        value: 'awaiting_acceptance',
        label: '待验收',
        category: 'awaiting_acceptance',
      },
      {
        value: 'completed',
        label: '已完成',
        category: 'completed',
      },
    ]

    // 将配置的状态转换为统一格式
    const statusList = configuredStatuses.map((status) => ({
      value: status.id,
      label: status.name,
      status_category: status.status_category,
    }))

    // 根据 status_category 字段进行动态分组
    const groupedStatuses = statusGroups.map((group) => {
      const matchedStatuses = statusList.filter((item) => {
        // 优先使用 status_category 字段，如果没有则使用兜底逻辑
        if (item.status_category) {
          return item.status_category === group.category
        }

        // 兜底逻辑：为没有 status_category 的旧数据提供默认分组
        return getDefaultCategoryForStatus(item.value) === group.category
      })

      return {
        value: group.value,
        label: group.label,
        children: matchedStatuses.map((item) => ({
          value: item.value,
          label: item.label,
        })),
      }
    })

    // 过滤掉空的分组
    return groupedStatuses.filter((group) => group.children.length > 0)
  }

  /**
   * 为 simple 模式加载工作流状态数据
   */
  const loadWorkflowStatusForSimple = async () => {
    try {
      // 等待状态数据加载完成
      await new Promise((resolve) => {
        const checkStatusList = () => {
          if (state.issueStatusList && state.issueStatusList.length > 0) {
            resolve()
          } else {
            setTimeout(checkStatusList, 100)
          }
        }
        checkStatusList()
      })

      // 将 issueStatusList 转换为 configuredStatuses 格式
      const configuredStatuses = state.issueStatusList.map((status) => ({
        id: status.value,
        name: status.label,
        status_category: status.status_category,
      }))

      // 构建工作流状态列表
      state.workflowStatusList =
        buildWorkflowStatusHierarchy(configuredStatuses)
    } catch (error) {
      console.error('加载 simple 模式工作流状态失败:', error)
    }
  }

  const changeMultiHandle = async (v) => {
    state.changeValue = null

    // 状态修改时的特殊处理
    if (v === 'status_id') {
      if (!state.selectRows.length) {
        $baseMessage('请先选择事项', 'error')
        state.params = ''
        return
      }

      if (!checkSameWorkflow()) {
        $baseMessage('选中事项包含不同事项类型，无法批量修改状态', 'error')
        state.params = ''
        return
      }

      // 获取当前事项类型配置的状态
      await loadTrackerConfiguredStatuses()
      return
    }

    switch (v) {
      case 'parent_id':
        if (state.issueList.length == 0) {
          if (project_id) {
            getParentIssueList()
          } else {
            state.issueList.unshift({
              subject: '【取消所选中事项父任务关系】',
              id: '',
            })
          }
        }
        break
      case 'category_id':
        if (state.issueList.length == 0) {
          state.allUnitList = project_id ? state.allUnitList : []
          // 在数组的开头插入元素
          // 检查是否已经存在相同元素，如果不存在则插入
          const hasElement = state.allUnitList.some(
            (item) => item.name === '【解除所选中事项所属模块】'
          )
          if (!hasElement) {
            // 在数组的开头插入元素
            state.allUnitList.unshift({
              name: '【解除所选中事项所属模块】',
              id: '',
            })
          }
        }
        break
      case 'class_id':
        getIssueClassList()
        break
    }
  }

  const reloadProjectVersionList = async (row) => {
    //假如myself为true,该版本下拉需动态加载
    if (!props.myself) {
      return
    }
    const project_id = row.project_id

    const list = state.cacheVerListObj[project_id]
    if (list !== undefined) {
      state.projectVersionList = list
      return
    }
    const {
      data: { data },
    } = await getProjectVersionList({
      limit: 100,
      filter: { project_id: project_id },
    })

    state.projectVersionList = data

    if (data.length === 0) {
      $baseMessage(`该项目无版本列表`)
    } else {
      state.cacheVerListObj[project_id] = data
    }
  }

  const reloadReleaseVersionList = async (row) => {
    //假如myself为true,该版本下拉需动态加载
    if (!props.myself) {
      return
    }
    const project_id = row.project_id

    const list = state.cacheReleaseVerListObj[project_id]
    if (list !== undefined) {
      state.releaseVersionList = list
      return
    }
    const {
      data: { data },
    } = await getReleaseVersionList({
      limit: 100,
      filter: { project_id: project_id, status: 'open' },
    })

    state.releaseVersionList = data

    if (data.length === 0) {
      $baseMessage(`该项目无版本列表`)
    } else {
      state.cacheReleaseVerListObj[project_id] = data
    }
  }

  const reloadMemberList = async (row) => {
    // 假如myself为true, 代表该人员下拉需要动态加载
    if (!props.myself) {
      return
    }
    const list = state.memberPidObj[row.project_id]
    if (list !== undefined) {
      state.projectMemberList = list
      return
    }
    state.memberLoading[row.id] = true
    state.projectMemberList = [{ id: -1, name: '未指定' }] //这里假如置为空数组，会被认为高度为0，默认在底部弹出
    const {
      data: { data },
    } = await getMemberList({
      limit: 100,
      filter: { project_id: row.project_id, user: { type: 'User' } },
    })

    data.forEach((item) => {
      state.projectMemberList.push({
        id: item.user_id,
        name: item.user.lastname + item.user.firstname,
      })
    })
    state.memberLoading[row.id] = false
    state.memberPidObj[row.project_id] = Object.assign(
      [],
      state.projectMemberList
    )
  }

  const changeAllStaff = (row) => {
    if (
      row &&
      typeof row.third_user_id != 'undefined' &&
      row.third_user_id > 0
    ) {
      state.queryForm.assigned = row.third_user_id
      isSelectedHandler = true
    } else {
      state.queryForm.assigned = null
      isSelectedHandler = null
    }
  }

  /**
   * 表排序时候的触发事件
   */
  const handleChange = (e) => {
    let { column, prop, order } = e
    //不同表格字段，对应后台字段排序可能不一致
    const obj = {
      created_on_text: 'created_on',
      priority_id: 'priority_id',
      assigned_to_id: 'assigned_to_id',
    }
    // order为null 代表取消当前所使用的排序
    state.queryForm.sort = obj[prop] || order ? obj[prop] ?? prop : ''
    let orderStr = ''
    if (order === 'ascending') {
      orderStr = 'ASC'
    } else if (order === 'descending') {
      orderStr = 'DESC'
    } else {
      state.queryForm.sort = 'id'
    }
    state.queryForm.order = orderStr
    fetchData()

    state.sortRule = {
      prop: state.queryForm.sort ?? 'id',
      order: order ?? 'descending',
    }
    // 将排序信息保存到LocalStorage中
    localStorage.setItem('tableSort', JSON.stringify(state.sortRule))
  }

  const handleClick = (event) => {
    changeQueryFormTopShow('otherClick')
    handleTreeViewPopover('otherClick')
  }

  /**
   * 获取项目下的模块
   */
  const handleGetUnitList = async () => {
    const categoryListData = await getUnitList({
      filter: { project_id: project_id },
    })

    state.allUnitList = categoryListData.data
  }

  const getIssueClassList = async () => {
    if (!project_id) {
      return
    }

    const { data } = await getIssueClassListData({
      pageNo: 1,
      limit: 9999,
      filter: {
        project_id: project_id,
      },
      order: 'ASC',
    })

    state.issueClassList = data.data[0].children
    state.issueClassList[0].id = 0
  }

  watch(issueTableRef, async (newVal, oldVal) => {
    await nextTick()
    if (!oldVal && newVal && !props.simple) {
      // 初始化表格高度计算
      calculateTableHeight()
      // 启动动态高度监听
      startHeightMonitoring()
    }
  })

  watch(unitRef, async (newVal, oldVal) => {
    await nextTick()
    if (!oldVal && newVal) {
      // 模块面板加载完成后，重新计算高度
      calculateTableHeight()
    }
  })

  const customSleep = (delay) => {
    return new Promise((resolve) => {
      setTimeout(resolve, delay)
    })
  }

  /**
   * 获取项目类型列表
   */
  const handleGetProjectTypeList = async () => {
    const projectTypeListData = await queryCategory({
      filter: { pid: 0 },
      op: { pid: '>' },
      type: 'projects_type',
    })
    state.projectTypeList = projectTypeListData.data
  }

  const handleStorageEvent = (event) => {
    if (event.key === 'user_settings_' + user_id + '_') {
      // 从 localStorage 获取最新的 user_settings
      user_settings = JSON.parse(
        localStorage.getItem('user_settings_' + user_id + '_')
      )
    }
  }

  const tableColumnMaxWidth = (row) => {
    if (issueTableRef.value) {
      // 获取table第二个列标题的宽度
      const colgroup = document.querySelector('colgroup')
      const colElements = colgroup.querySelectorAll('col')
      const secondColWidth = colElements[1].getAttribute('width')

      // 动态减去（项目名 + 分类模块名）的宽度
      // tmp = calcText.value * 10
      let tmp =
        charAt(
          props.simple ? row.project_text?.name : 0 + row.category_text?.name
        ) + 70
      return secondColWidth ? secondColWidth - tmp - 70 + 'px' : ''
    }
    return ''
  }

  const initAllList = () => {
    state.projectTypeList.length = 0
    state.issueStatusList.length = 0
    state.projectMemberList.length = 0
    state.issueTypeList.length = 0
    state.projectList.length = 0
    state.unitList.length = 0
    issueTableRef.value = null
  }

  /**
   * 替换当前url
   */
  const replaceUrl = async () => {
    const path = route.path
    let queryParams = ''
    for (const [key, value] of Object.entries(route.query)) {
      if (key && value) {
        queryParams += `${encodeURIComponent(key)}=${encodeURIComponent(
          value
        )}&`
      }
    }
    const newUrl = `#${path}?${queryParams}`
    window.history.replaceState(null, '', newUrl)
    await nextTick()
  }

  const handleShowUnitList = () => {
    state.isShowUnit = !state.isShowUnit
    localStorage.setItem(
      'issueListShowUnitList' + user_id,
      state.isShowUnit ? 'show' : 'hide'
    )

    // 模块展开/收起后，重新计算表格高度
    nextTick(() => {
      calculateTableHeight()
    })
  }

  // 智能表格高度计算函数
  const calculateTableHeight = () => {
    if (!fullscreenContainer.value) return

    // 容器到视口顶部的距离
    const containerRect = fullscreenContainer.value.getBoundingClientRect()
    const containerTop = containerRect.top

    // 分页器的高度
    const paginationHeight = props.pagination ? 60 : 0

    // 筛选表单区域的实际高度
    const queryFormEl = document.querySelector('.vab-query-form')
    const queryFormHeight = queryFormEl
      ? queryFormEl.getBoundingClientRect().height
      : 0

    // 计算可用高度：视口高度 - 容器顶部距离 - 筛选表单高度 - 分页器高度 - 边距
    const availableHeight =
      window.innerHeight -
      containerTop -
      queryFormHeight -
      paginationHeight -
      30

    // 确保最小高度
    if (availableHeight > 200) {
      state.tableHeight = availableHeight

      // // 调试信息
      // console.log('[IssueList] 高度计算:', {
      //   windowHeight: window.innerHeight,
      //   containerTop: containerTop,
      //   queryFormHeight: queryFormHeight,
      //   paginationHeight: paginationHeight,
      //   availableHeight: availableHeight,
      //   tableHeight: state.tableHeight,
      // })
    }
  }

  // 启动高度监听
  const startHeightMonitoring = () => {
    // 监听窗口大小变化
    const handleResize = () => {
      if (issueTableRef.value && !props.simple) {
        calculateTableHeight()
      }
    }

    // 监听筛选表单区域的高度变化
    const queryFormEl = document.querySelector('.vab-query-form')
    if (queryFormEl) {
      const queryFormObserver = new ResizeObserver(() => {
        calculateTableHeight()
      })
      queryFormObserver.observe(queryFormEl)

      // 保存 observer 引用，以便后续清理
      state.queryFormObserver = queryFormObserver
    }

    // 添加窗口大小变化监听器
    window.addEventListener('resize', handleResize)

    // 保存事件监听器引用，以便后续清理
    state.resizeHandler = handleResize
  }

  // 清理高度监听
  const cleanupHeightMonitoring = () => {
    if (state.queryFormObserver) {
      state.queryFormObserver.disconnect()
    }
    if (state.resizeHandler) {
      window.removeEventListener('resize', state.resizeHandler)
    }
  }

  //批量迁移
  const handleBatchMigration = () => {
    if (state.selectRows.length === 0) {
      $baseMessage('请先选择要迁移的事项', 'warning')
      return
    }

    // 获取选中行的完整信息，包含ID、标题、项目名称和父项ID
    const selectedIssues = state.selectRows.map((row) => ({
      id: row.id,
      subject: row.subject,
      project_name: row.project ? row.project.name : '',
      parent_id: row.parent_id || null,
    }))

    // 调用批量迁移组件，传入完整的事项信息
    batchSelectIssueProjectRef.value.showComponent(selectedIssues)
  }

  // 处理批量复制事项
  const handleBatchCopy = () => {
    if (state.selectRows.length === 0) {
      $baseMessage('请先选择要复制的事项', 'warning')
      return
    }

    // 获取选中事项的ID列表
    const issueIds = state.selectRows.map((issue) => issue.id).join(',')

    // 跳转到复制页面，传递事项ID和项目ID
    router.push({
      path: '/project/issueCopy',
      query: {
        ids: issueIds,
        project_id: route.query.project_id || urlProjectId.value,
      },
    })
  }

  /**
   * 复制单个事项
   * @param {object} row - 当前行数据
   */
  const handleCopyIssue = (row) => {
    // 跳转到事项复制页面，传递单个事项ID和项目ID
    router.push({
      path: '/project/issueCopy',
      query: {
        ids: row.id.toString(),
        project_id: row.project_id.toString(),
      },
    })
  }

  // 批量迁移成功后的处理
  const handleBatchMigrationSuccess = () => {
    // 清空选中的行
    state.selectRows = []

    setTimeout(() => {
      // 刷新列表
      fetchData()
    }, 1500)
  }

  /**
   * 批量修改多指派人
   */
  const patchChangeAssigedId = async () => {
    state.changeValue = state.batchChangeAssigner
    await nextTick()
    state.batchChangeAssigner = []
  }

  //监听需要保存得值，批量进行保存
  watch(
    () => state.changeValue,
    (v) => {
      if (v === null) {
        return
      }

      // 20250108 分类批量修改
      let changeIssueClassId = 0
      if (state.params == 'class_id' && Array.isArray(state.changeValue)) {
        changeIssueClassId = state.changeValue[state.changeValue.length - 1]
      }

      const { selectRows } = state
      if (!selectRows || selectRows.length === 0) {
        $baseMessage(`请选择至少一条记录`, 'error', 'vab-hey-message-error')
        state.changeValue = null
        return
      }

      // 状态修改时检查工作流一致性
      if (state.params === 'status_id') {
        const firstItem = selectRows[0]
        const hasDifferentWorkflow = selectRows.some(
          (item) =>
            item.project_id !== firstItem.project_id ||
            item.tracker_id !== firstItem.tracker_id
        )

        if (hasDifferentWorkflow) {
          $baseMessage('选中事项包含不同事项类型，无法批量修改状态', 'error')
          state.changeValue = null
          return
        }
      }

      if (state.params == 'assigned_to_id' && v === 0) {
        v = ''
      }

      // 修改的是父级任务
      if (state.params == 'parent_id') {
        for (let s in selectRows) {
          if (
            typeof selectRows[s].hasChildren != 'undefined' &&
            selectRows[s].hasChildren == true
          ) {
            $baseMessage(
              `禁止修改存在父子关系的事项`,
              'error',
              'vab-hey-message-error'
            )
            state.changeValue = null
            return
          }
          if (v == selectRows[s].id) {
            $baseMessage(`父级任务不能为自己`, 'error', 'vab-hey-message-error')
            state.changeValue = null
            return
          }
        }
      }
      const ids = []
      selectRows.forEach((item) => {
        ids.push(item.id)
      })
      if (state.params == 'watchers') {
        state.listLoading = true
        doWatchersMulti({
          ids: ids.join(','),
          watcher: v,
        })
          .then((res) => {
            if (res.data) {
              $baseMessage(res.msg, 'success', 'vab-hey-message-success')
              setTimeout(async () => {
                await fetchData() //刷新数据
              }, 1000)
              emit('refresh') //广播父组件刷新
            }
          })
          .finally(() => {
            state.changeValue = null
            state.listLoading = false
          })
      } else {
        const formData = {
          ids: ids.join(','),
          params: {},
        }
        formData.params[state.params] =
          state.params === 'fixed_version_id' && v == 0 ? null : v
        state.listLoading = true
        if (state.params == 'class_id') {
          formData.params['class_id'] = changeIssueClassId
        }
        patchIssueField(formData)
          .then(async (res) => {
            if (res.data) {
              if (state.params == 'class_id') {
                $pub('update-issue-class-list')
              }
              $baseMessage(res.msg, 'success', 'vab-hey-message-success')
              setTimeout(async () => {
                if (
                  state.params == 'priority_id' ||
                  state.params == 'status_id' ||
                  state.params == 'assigned_to_id'
                ) {
                  let Rows = cloneDeep(selectRows)

                  state.selectRows.length = 0
                  issueTableRef.value.clearSelection()

                  Rows.forEach((row) => {
                    partialRefresh(row, state.params)
                  })
                } else {
                  await fetchData() //刷新数据
                }
              }, 1000)
              emit('refresh') //广播父组件刷新
            }
          })
          .finally(() => {
            state.changeValue = null
            state.listLoading = false
          })
      }
    }
  )

  watch(
    () => state.listLoading,
    (newLoadingState) => {
      if (!newLoadingState) {
        // 当加载状态结束时执行的操作
        // 这里可以放置您希望在加载结束时执行的代码
      }
    }
  )
  watch(
    () => state.isExpand,
    (v) => {
      if (v && foldSwitchValue.value) {
        setTimeout(() => {
          expandDef()
        }, 0)
      }
    }
  )
  // 监听 props.showCloumn 的变化并同步到 state.showCloumn
  watch(
    () => props.showCloumn,
    (newVal) => {
      state.showCloumn = { ...newVal } // 同步更新
    },
    { immediate: true } // 在初始加载时立即执行一次
  )

  // 监听选中事项变化，自动重置批量操作状态
  watch(
    () => state.selectRows,
    (newSelectRows) => {
      // 如果当前正在进行批量状态修改
      if (state.params === 'status_id') {
        if (newSelectRows.length === 0) {
          // 没有选中任何事项，重置
          state.params = ''
          state.changeValue = null
          state.workflowStatusList = []
        } else if (!checkSameWorkflow()) {
          // 选择了不同工作流的事项，重置并提示
          state.params = ''
          state.changeValue = null
          state.workflowStatusList = []
          $baseMessage('选择事项包含不同工作流，已重置批量操作', 'warning')
        }
        // 如果还是同一工作流，保持当前状态
      }
    },
    { deep: true }
  )

  /**
   * 展开子事项操作
   */
  const expandDef = (expand = true, mode = null, $event) => {
    // 浏览器本地存储是否展开子事项
    if (mode == 'isfold') {
      if (typeof $event == 'undefined') {
        return
      }
      localStorage.setItem(state.localStorageKey, expand)
    }

    const els1 = document.getElementsByClassName(
      'el-table__expand-icon el-table__expand-icon--expanded'
    )
    let els = document.getElementsByClassName('el-table__expand-icon')
    // 已经有展开的项
    // if (els1.length != 0) {
    //   return
    // }
    // 创建一个空数组来存储差异
    const difference = []

    if (!expand) {
      for (let i = 0; i < els1.length; i++) {
        difference.push(els[i])
      }
      for (let i = 0; i < difference.length; i++) {
        const clickEvent = new Event('click', {
          bubbles: true,
          cancelable: true,
        })
        difference[i].dispatchEvent(clickEvent)
      }
      return
    }

    // 循环遍历 els 中的每个元素
    for (let i = 0; i < els.length; i++) {
      let isDuplicate = false
      // 检查当前元素是否存在于 els1 中
      for (let j = 0; j < els1.length; j++) {
        if (els[i] === els1[j]) {
          isDuplicate = true
          break
        }
      }
      // 如果当前元素不是 els1 中的重复项，则将其添加到差异数组中
      if (!isDuplicate) {
        difference.push(els[i])
      }
    }
    els = difference

    for (let i = 0; i < els.length; i++) {
      const clickEvent = new Event('click', {
        bubbles: true,
        cancelable: true,
      })
      els[i].dispatchEvent(clickEvent)
    }
  }

  const changeQueryFormTopShow = (value) => {
    if (value == 'otherClick') {
      state.queryFormTopShow = false
      state.visiblePopover = false
      return
    }
    state.queryFormTopShow = !state.queryFormTopShow
    state.visiblePopover = !state.visiblePopover
  }

  const handleTreeViewPopover = (e) => {
    if (e == 'otherClick') {
      state.visibleTreeViewPopover = false
      return
    }
    state.visibleTreeViewPopover = !state.visibleTreeViewPopover
  }

  const flattenChildren = (data) => {
    const result = []

    data.forEach((item) => {
      // 创建一个新对象，用于存储扁平化后的属性
      const flattenedItem = {}
      // 遍历当前对象的属性
      Object.entries(item).forEach(([key, value]) => {
        // 如果属性值是一个对象且不为空数组，则递归调用 flattenChildren 函数
        if (Array.isArray(value) && value.length > 0) {
          flattenedItem[key] = flattenChildren(value)
        } else {
          // 否则，将属性添加到扁平化后的对象中
          flattenedItem[key] = value
        }
      })

      // 将扁平化后的对象添加到结果数组中
      result.push(flattenedItem)
    })
    return result
  }

  const exportData = () => {
    const columns = [
      {
        label: '#',
        prop: 'id',
        align: 'center',
        width: '100',
      },
      {
        label: 'BI链接',
        prop: 'link',
        align: 'center',
        width: '100',
      },
      {
        label: '项目',
        prop: 'project_text.name',
        align: 'center',
        width: '100',
      },
      {
        label: '事项类型',
        prop: 'issue_type.name',
        align: 'center',
        width: '100',
      },
      {
        label: '标题',
        prop: 'subject',
        align: 'center',
        width: '100',
      },
      {
        label: '处理人',
        prop: 'author_name',
        align: 'center',
        width: '100',
      },
      {
        label: '创建人',
        prop: 'author_text.name',
        align: 'center',
        width: '100',
      },
      {
        label: '创建于',
        prop: 'created_on',
        align: 'center',
        width: '100',
      },
      {
        label: '更新于',
        prop: 'updated_on',
        align: 'center',
        width: '100',
      },
      {
        label: '开始日期',
        prop: 'start_date',
        align: 'center',
        width: '100',
      },
      {
        label: '完成日期',
        prop: 'due_date',
        align: 'center',
        width: '100',
      },
      {
        label: '状态',
        prop: 'issue_status.name',
        align: 'center',
        width: '100',
      },
      {
        label: '优先级',
        prop: 'priority_text',
        align: 'center',
        width: '100',
      },
      {
        label: '版本',
        prop: 'version_text.name',
        align: 'center',
        width: '100',
      },
      {
        label: '类别(模块)',
        prop: 'category_text.name',
        align: 'center',
        width: '100',
      },
    ]
    const tHeader = columns
      .map((item) => {
        return item.prop
      })
      .filter((item) => {
        if (item) {
          return item
        }
      })
    const herderLabel = columns
      .map((item) => {
        return item.label
      })
      .filter((item) => {
        if (item) {
          return item
        }
      })
    let tipMsg = ''
    if (state.selectRows.length === 0) {
      tipMsg = '是否导出当前筛选条件下所有数据'
    } else {
      let l = state.selectRows.length
      tipMsg = `是否确定导出勾选的${l}条数据`
    }
    let queryFormTmp = cloneDeep(state.queryForm)
    if (queryFormTmp.assigned) {
      queryFormTmp.filter.issueAssigned = {}
      queryFormTmp.op.issueAssigned = { user_id: '=' }
      queryFormTmp.filter.issueAssigned['user_id'] = queryFormTmp.assigned
      queryFormTmp.op.issueAssigned['user_id'] = '='
    } else {
      delete queryFormTmp.filter.issueAssigned
    }

    queryFormTmp.pageNo = 1 // limit设置过大时pageNo非第一页则拿不到数据导出
    $baseConfirm(tipMsg, null, async () => {
      let needFormatData = {}
      if (state.selectRows.length === 0) {
        queryFormTmp.limit = 99999
        queryFormTmp.filter.is_tree_like = false
        queryFormTmp.filter.parent_id = null

        const { data } = await getList(queryFormTmp)
        needFormatData = data.data
      } else {
        state.selectRows.forEach((r) => {
          // 接口使用getList时使用oa_product_borrow.id参数
          // 接口使用getDetailsList时使用oa_product_borrow_details.id参数
          queryFormTmp.filter['id'] = !queryFormTmp.filter['id']
            ? r.id
            : queryFormTmp.filter['id'] + ',' + r.id
        })
        queryFormTmp.op['id'] = 'IN'

        needFormatData = flattenChildren(state.selectRows)
      }
      const host = process.env.VUE_APP_URL ?? 'http://bi.t-firefly.com:2101'
      needFormatData.forEach((item) => {
        item.subject = `=HYPERLINK("${host}/#/project/detail?project_id=${project_id}&issue_id=${item.id}","${item.subject}")`
        let url = `${host}/#/project/detail?project_id=${project_id}&issue_id=${item.id}`
        item.link = `=HYPERLINK("${url}","${url}")`

        // 重新获取多人指派，指派人的名称
        item.author_name = item.assigned_text?.name || ''
        if (
          typeof item.issue_assigned != 'undefined' &&
          item.issue_assigned.length > 0
        ) {
          item.author_name = ''
          item.issue_assigned.forEach((iaItem) => {
            item.author_name =
              item.author_name == ''
                ? iaItem.username
                : `${item.author_name}, ${iaItem.username}`
          })
        }
      })
      queryFormTmp = null // 确保不被长时间持有，释放内存
      state.exportLoading = true
      const { rows, merges } = formatJson(tHeader, needFormatData)
      export_json_to_excel({
        header: herderLabel,
        data: rows,
        filename:
          '事项导出-' +
          moment().format('YYYY-MM-DD') +
          '-' +
          Math.floor(Math.random() * 9000 + 1000),
        merges: merges,
      })
      state.exportLoading = false
    })
    return
  }

  /**
   * 获取需要导出的字段数据
   * @param {*} tHeader 需要导出的字段数据
   * @param {*} rows 导出的原数据
   */
  const formatJson = (tHeader, rows, mergesFields = []) => {
    let newRows = rows.map((item) => ({ ...item }))

    // 筛选需要导出的字段数据
    let retRows = []
    let retMerges = []

    let num = 0
    newRows.forEach((n) => {
      let item = []

      tHeader.forEach((t) => {
        let propertyValue = []
        if (t.includes('.')) {
          // 使用 split 将字符串拆分为两层 需要访问类似issue_status.name这种
          const [firstLevel, secondLevel] = t.split('.')
          // 使用动态属性访问
          propertyValue = newRows[num][firstLevel]?.[secondLevel]
        } else {
          propertyValue = newRows[num][t]
        }
        item.push(propertyValue)
      })
      num += 1
      retRows.push(item)
    })

    return { rows: retRows, merges: retMerges }
  }

  ///仅删除表格数据，通过issueid
  const deleteIssueByIds = (ids) => {
    state.list = state.list.filter((ele) => {
      if (!ids.includes(ele.id)) {
        return ele
      }
    })
  }

  // 删除事项
  const deleteIssue = async (row) => {
    $baseConfirm('确认是否删除该事项?', null, async () => {
      try {
        const { msg } = await doDelete({ ids: row.id })
        $baseMessage(msg, 'success', 'vab-hey-message-success')
        // 刷新列表数据
        await fetchData()
      } catch (error) {
        console.error('删除事项失败:', error)
      }
    })
  }

  /**
   * 事项列表中存在各项目的不同成员，获取所有项目成员列表并做区分
   */
  const getAllJoinedPorjectMemberList = async () => {
    state.allJoinedPorjectMemberList = {}

    // 提取 project_id 字段并去重
    const projects = [...new Set(state.list_all.map((item) => item.project_id))]

    // 使用 Promise.all 处理多个异步请求
    const promises = projects.map(async (projectId) => {
      const {
        data: { data },
      } = await getMemberList({
        limit: 999,
        filter: { project_id: projectId, user: { type: 'User', status: 1 } },
        op: { user: { status: '=' } },
      })

      // 将结果写入响应式对象
      if (data) {
        state.allJoinedPorjectMemberList[projectId] = [
          { id: -1, name: '未指定' },
        ]
        data.forEach((item) => {
          state.allJoinedPorjectMemberList[projectId].push({
            id: item.user_id,
            name: item.user.lastname + item.user.firstname,
            user: item.user,
          })
        })
      }
    })

    // 等待所有请求完成
    await Promise.all(promises)

    // 确保所有数据已经写入 state 后，再 emit 事件
    emit('project-member-got', cloneDeep(state.allJoinedPorjectMemberList))
  }

  /**
   * 新建事项
   * @param tId 对应分类id
   * @param name 弹窗标题
   */
  const newIssue = (tId, name, project_type = '') => {
    issueCreateRef.value.showEdit()
  }

  const filterUserList = () => {}

  /**
   * 选择处理人下拉获取与失去焦点时触发
   * @param {*} b
   */
  const selectAssigned = (b, className = 'issue-') => {
    // 主要处理失去焦点后，取消显示删除样式
    let active = 'assigned-active'
    if (b == true) {
      // state.selectAssginedClass.push(active)
    } else if (state.currentSelectAssigned) {
      let dom = document.querySelector(
        '.' + className + state.currentSelectAssigned.id
      )

      dom.classList.remove(active)
    }
  }

  /**
   * 点击选择处理人时触发
   */
  const clickSelectAssigned = (row, className = 'issue-') => {
    // 主要处理获取焦点后显示删除样式
    let active = 'assigned-active'
    state.currentSelectAssigned = row
    let dom = document.querySelector('.' + className + row.id)
    dom.classList.add(active)
  }

  const quicklyC = reactive({
    subject: '',
  })

  /**
   * 快速创建-行数据默认添加首行
   */
  const listData = computed(() => {
    const newRow = {
      id: -1,
      subject: '',
      assigned: '',
      priority_id: 3,
      // status_id: 1, //移除硬编码 让后端智能处理自定义工作流的状态
      isNew: true, // 标记该行是新增行
      fixed_version_id: Number.isInteger(
        state.queryForm.filter.fixed_version_id
      )
        ? state.queryForm.filter.fixed_version_id
        : null,
      issue_assigned: [],
      project_id: parseInt(project_id),

      tracker_id: 11,
      class_id: route.query.class_id ? parseInt(route.query.class_id) : 0, // 需求事项继承事项分类
    }
    // userVisibleForOverflow.value = 'hidden'
    // 仅在项目列表那边在首行插入
    if (
      route.path.slice(0, 9) == '/project/' && // 属于项目管理菜单项
      route.path.slice(0, 15) !== '/project/myWork' && // 不属于事项列表菜单项
      !/^\/project\/testplandetail/.test(route.path) && // 不属于测试计划详情相关事项
      (!state.list[0] || state.list[0]?.id > 0) // 首行不是新增行,防止重复添加首行
    ) {
      state.list.unshift(newRow)
      userVisibleForOverflow.value = 'visible !important'
    }

    return state.list
  })

  const quicklyCreating = ref(false)
  const showAllOverFlowTooltip = ref('inherit')
  const userVisibleForOverflow = ref('hidden')

  /**
   * 快速创建-tooltip关闭
   */
  watch(quicklyCreating, async () => {
    if (quicklyCreating.value) {
      showAllOverFlowTooltip.value = 'none'
    }
  })

  /**
   * 检查自定义字段是否有必填项
   * @param {number} project_id - 项目ID
   * @param {number} tracker_id - 事项类型ID
   */
  const checkCustomFields = async (project_id, tracker_id) => {
    if (!project_id || !tracker_id) {
      state.hasRequiredCustomFields = false
      return
    }

    try {
      state.customFieldsLoading = true
      state.customFieldsError = null

      const params = {
        project_id: project_id,
        tracker_id: tracker_id,
      }

      const { data } = await getProjectCustomFieldList(params)

      // 检查是否有必填字段
      const hasRequired = data && data.some((field) => field.is_required === 1)
      state.hasRequiredCustomFields = hasRequired

      // 如果有必填字段，显示消息提示
      if (hasRequired) {
        $baseMessage(
          '该事项类型包含必填字段，无法快速创建，请使用完整创建功能',
          'warning',
          'vab-hey-message-warning'
        )
      }
    } catch (error) {
      console.error('检查自定义字段失败:', error)
      state.customFieldsError = error.message || '检查自定义字段失败'
      state.hasRequiredCustomFields = false
    } finally {
      state.customFieldsLoading = false
    }
  }

  /**
   * 处理事项类型变化
   * @param {number} trackerId - 选中的事项类型ID
   * @param {object} row - 当前行数据
   */
  const handleTrackerChange = async (trackerId, row) => {
    if (row.isNew && project_id) {
      await checkCustomFields(project_id, trackerId)
    }
  }

  /**
   * 快速创建-启动快速创建
   */
  const addNewRow = async (row) => {
    await checkCustomFields(project_id, row.tracker_id)
    quicklyCreating.value = true
  }

  /**
   * 快速创建-关闭快速创建
   */
  const closeQuiklyCreate = () => {
    quicklyCreating.value = false
    quicklyC.subject = ''

    // baseColumns.value = cloneDeep(baseColumnsOld.value)
  }

  /**
   * 快速创建-tooltip重新开启
   */
  const mouseLeaveDiv = async () => {
    if (!quicklyCreating.value) {
      showAllOverFlowTooltip.value = 'inherit'
    }
  }

  /**
   * 快速创建-保存
   */
  const saveQuiklyCreate = async (row) => {
    let newRow = cloneDeep(row)
    delete newRow['isNew']

    newRow.subject = quicklyC.subject

    doEdit(newRow)
      .then(() => {
        $baseMessage('创建成功', 'success', 'vab-hey-message-success')
        $pub('issue-add-success')
        if (route.path.slice(0, 9) == '/project/') {
          $pub('update-issue-class-list')
        }
      })
      .finally(() => {
        closeQuiklyCreate()
      })
  }

  const inputRef = ref(null) //快速编辑-创建对 el-input 的引用

  /**
   * 快速编辑-标题
   */
  const editIssueSubject = (currentRow) => {
    state.subjectForEdit = currentRow.subject
    listData.value.forEach((row) => {
      row.subjectEditing = false
    })
    // 将当前行设置为编辑状态
    currentRow.subjectEditing = true
    setFocus()
  }

  /**
   * 快速编辑-保存
   */
  const saveSubject = async (row) => {
    if (row.subjectEditing) {
      // 存在编辑差异需要保存
      if (row.subject != state.subjectForEdit) {
        row.subject = state.subjectForEdit
        await doEdit({ id: row['id'], subject: row.subject })

        $baseMessage('修改成功', 'success', 'vab-hey-message-success')
      }
      row.subjectEditing = false
    }
  }

  /**
   * 快速编辑-自动设置焦点
   */
  const setFocus = async () => {
    await new Promise((resolve) => setTimeout(resolve, 100))
    inputRef.value?.[0].focus() // 获取 input 元素并设置焦点
  }

  const cellStyleMethod = ({ column, row }) => {
    if (column.property === 'subject') {
      let paddingLeft = 16 * (row.depth ?? 0) + 'px'

      return {
        paddingLeft: paddingLeft,
      }
    }
    return ''
  }

  defineExpose({ deleteIssueByIds, fetchData })

  /**
   * 处理popover显示
   * @param {number} rowId - 行ID
   */
  const handlePopoverShow = (rowId) => {
    if (!state.visiblePopoverIds.includes(rowId)) {
      state.visiblePopoverIds.push(rowId)
    }
  }

  /**
   * 处理popover隐藏
   * @param {number} rowId - 行ID
   */
  const handlePopoverHide = (rowId) => {
    const index = state.visiblePopoverIds.indexOf(rowId)
    if (index > -1) {
      state.visiblePopoverIds.splice(index, 1)
    }
  }

  /**
   * 复制事项链接到剪贴板
   * @param {object} row - 当前行数据
   */
  const copyIssueLink = async (row) => {
    const issueId = row.id
    const projectId = row.project_id

    // 在 List.vue 中，我们统一使用 /project/detail 路径，与 openIssue 行为保持一致
    const text = `${window.location.origin}/#/project/detail?project_id=${projectId}&issue_id=${issueId}`

    if (typeof navigator.clipboard !== 'undefined') {
      try {
        await navigator.clipboard.writeText(text)
        $baseMessage('链接已复制', 'success', 'vab-hey-message-success')
      } catch (err) {
        console.error('复制失败:', err)
        $baseMessage(
          `无法复制: ${err.message || err}`,
          'error',
          'vab-hey-message-error'
        )
      }
    } else {
      const textarea = document.createElement('textarea')
      textarea.value = text
      textarea.style.position = 'fixed' // Prevent scrolling to bottom of page in MS Edge.
      textarea.style.top = '0'
      textarea.style.left = '-9999px' // Move out of screen and make it invisible
      textarea.style.opacity = '0' // Make it invisible

      document.body.appendChild(textarea)
      textarea.focus()
      textarea.select()

      let success = false
      try {
        success = document.execCommand('copy')
      } catch (err) {
        console.error('复制失败 (fallback):', err)
        $baseMessage(
          `无法复制: ${err.message || err}`,
          'error',
          'vab-hey-message-error'
        )
        document.body.removeChild(textarea)
        return
      }

      if (success) {
        $baseMessage('链接已复制', 'success', 'vab-hey-message-success')
      } else {
        $baseMessage('复制失败', 'error', 'vab-hey-message-error')
      }
      document.body.removeChild(textarea)
    }
  }

  /**
   * 处理复制标题事件 - 组件事件回调
   * @param {object} row - 行数据
   */
  const handleCopyTitle = (row) => {
    // 可以在这里添加额外的逻辑，比如统计等
    console.log('复制标题:', row.subject)
  }

  /**
   * 处理复制链接事件 - 组件事件回调
   * @param {object} row - 行数据
   */
  const handleCopyLink = (row) => {
    // 可以在这里添加额外的逻辑，比如统计等
    console.log('复制链接:', row.id)
  }

  /**
   * 处理删除事项事件 - 组件事件回调
   * @param {object} row - 行数据
   */
  const handleDeleteIssue = (row) => {
    // 调用原有的删除方法
    deleteIssue(row)
  }

  /**
   * 处理单行选择
   * @param {object} row - 当前行数据
   * @param {boolean} selected - 是否选中
   */
  const handleRowSelect = (row, selected) => {
    if (selected) {
      issueTableRef.value.toggleRowSelection(row, true)
    } else {
      issueTableRef.value.toggleRowSelection(row, false)
    }
  }

  /**
   * 处理全选
   * @param {boolean} selected - 是否全选
   */
  const handleSelectAll = (selected) => {
    if (selected) {
      // 选中所有非新增行
      listData.value.forEach((row) => {
        if (!row.isNew) {
          issueTableRef.value.toggleRowSelection(row, true)
        }
      })
    } else {
      issueTableRef.value.clearSelection()
    }
  }

  /**
   * 计算是否全选
   */
  const isAllSelected = computed(() => {
    const validRows = listData.value.filter((row) => !row.isNew)
    return validRows.length > 0 && state.selectRows.length === validRows.length
  })

  /**
   * 计算是否为半选状态
   */
  const isIndeterminate = computed(() => {
    const validRows = listData.value.filter((row) => !row.isNew)
    return (
      state.selectRows.length > 0 && state.selectRows.length < validRows.length
    )
  })

  //监听父组件传递参数，改变列表数据
  watch(
    () => props.queryFormFilter,
    async (v) => {
      state.queryForm.pageNo = 1
      if (props.isMyWork) {
        //我的issue，筛选方式不一样
        state.queryForm.filter = Object.assign({}, v)
        state.queryForm.op = Object.assign({}, props.queryFormOp)
        if (
          (typeof v.assigned_to_id == 'undefined' || v.assigned_to_id <= 0) &&
          (typeof v.issueAssigned == 'undefined' ||
            typeof v.issueAssigned.user_id == 'undefined' ||
            typeof v.issueAssigned.user_id <= 0)
        ) {
          state.queryForm.assigned = null
        } else {
          state.queryForm.assigned =
            v.assigned_to_id || v.issueAssigned?.user_id
        }
        await fetchData()
        return
      }
      //用Object.assing会把对象的对象直接替换，需要的是深层次合并
      state.queryForm = _.merge(state.queryForm, {
        filter: props.queryFormFilter,
        op: props.queryFormOp,
      })
      await fetchData()
    },
    { deep: true }
  )

  onMounted(() => {
    //刷新带上路由的页码
    const pageNo = parseInt(route.query.pageNo ?? 1, 10)
    state.queryForm.pageNo = pageNo

    setHeaderSettingForm()
    setTableActualHeader()

    tmpIssueTableRef = issueTableRef.value.store.states.treeData
    // setTreeLike()
    //用_.merge深拷贝，避免替换

    _.merge(state.queryForm, {
      filter: props.queryFormFilter,
      op: props.queryFormOp,
    })

    if (state.queryForm.filter.assigned_to_id) {
      state.queryForm.assigned = state.queryForm.filter.assigned_to_id
      delete state.queryForm.filter.assigned_to_id
    } else if (
      state.queryForm.filter.issueAssigned &&
      typeof state.queryForm.filter.issueAssigned.user_id != 'undefined'
    ) {
      state.queryForm.assigned = state.queryForm.filter.issueAssigned.user_id
    }

    handleIssueStatusList()
    handleEnumerationList()
    // 不是mywork，需要加载人员列表
    if (props.memoryStatus) {
      if (project_id) {
        // 恢复状态大类
        const savedStatusCategories = localStorage.getItem(
          `projectIssueTableStatusCategoriesSelected_${project_id}`
        )
        if (savedStatusCategories) {
          state.selectedStatusCategories = savedStatusCategories.split(',')
          // 如果有状态大类，则应用状态大类筛选
          handleStatusCategoryChange(state.selectedStatusCategories)
        } else {
          // 状态筛选的恢复由StatusCascaderSelector组件自动处理
          // 这里不需要手动恢复状态筛选
        }
        handleMemberList()
      } else if (!props.isIssueMy) {
        // 恢复状态大类
        const savedStatusCategories = localStorage.getItem(
          'myworkIssueTableStatusCategoriesSelected_'
        )
        if (savedStatusCategories) {
          state.selectedStatusCategories = savedStatusCategories.split(',')
          // 如果有状态大类，则应用状态大类筛选
          handleStatusCategoryChange(state.selectedStatusCategories)
        } else {
          // 状态筛选的恢复由StatusCascaderSelector组件自动处理
          // 这里不需要手动恢复状态筛选
        }
        handleMemberList(false)
      }
    }
    handleMemberList(props.isIssueMy)
    handleProject()

    if (!props.myself) {
      handleProjectVersionList()
    }

    const tableSort = JSON.parse(localStorage.getItem('tableSort'))
    if (tableSort) {
      handleChange(tableSort)
    } else {
      fetchData()
    }

    // 简易模式，用于首页、项目首页、产品首页展示
    if (props.simple) {
      // state.showHeader = false
      // state.outBorder = true
      handleGetProjectTypeList()
      handleMemberList(false)
      handleIssueVersions()
      // 加载工作流状态数据用于状态筛选
      loadWorkflowStatusForSimple()
    }

    $sub('issue-add-success', (value) => {
      if (value && value.length > 0) {
        state.queryForm.filter.fixed_version_id = value[0].fixed_version_id
      }

      //成功编辑后，刷新列表
      fetchData()
      if (state.issueList.length > 0) {
        getParentIssueList()
      }
    })

    $sub('version-edit-success', () => {
      //监听到新增了版本记录，重新刷新列表
      handleProjectVersionList()
    })

    $sub('project-unit-edit-success', () => {
      //监听到新增了模块修改，重新获取模块
      handleGetUnitList()

      fetchData()
    })

    // value是数组类型，为了在一个位置能传更多参数
    $sub('issue-edit-success', async (value) => {
      if (value && value.length > 0) {
        const row = value[0]
        const attribute = value[1]
        if (row && attribute != 'tracker_id') {
          partialRefresh(row, attribute ?? null)
        } else {
          fetchData()
        }
      }
    })

    $sub('project-version-edit-success', () => {
      //监听到新增了版本记录，重新刷新列表
      handleProjectVersionList()
    })

    handleGetUnitList()
    getIssueClassList()
    fullscreenContainer.value.addEventListener('click', handleClick)

    // 如果已经有表格引用，启动高度监听
    if (issueTableRef.value && !props.simple) {
      startHeightMonitoring()
    }

    getProjectInfo()
    getIssueTypeList()
    // handleGetProjectTypeList()

    user_settings = localStorage.getItem('user_settings_' + user_id + '_')
      ? JSON.parse(localStorage.getItem('user_settings_' + user_id + '_'))
      : user_settings
    // 监听localstorage变化的事件
    window.addEventListener('storage', handleStorageEvent)

    const formData = Object.assign({}, state.queryForm)
    $pub('fetch-no-plan-issue-count', formData)

    // 监听项目列表变化，重新加载版本列表
    if (
      state.queryForm.filter.project_id &&
      state.queryForm.filter.project_id.length > 0
    ) {
      handleProjectVersionListByProjects()
    }
  })

  onBeforeUnmount(() => {
    window.removeEventListener('storage', handleStorageEvent)

    // 清理高度监听
    cleanupHeightMonitoring()

    fullscreenContainer.value.removeEventListener('click', handleClick)
  })

  onUnmounted(() => {
    //必需，否则会重复监听
    // $unsub('issue-edit-success')
    // $unsub('issue-edit-close')
    $unsub('version-edit-success')
    $unsub('project-unit-edit-success')
    $unsub('issue-edit-success')
    $unsub('project-version-edit-success')
    initAllList()
  })
</script>

<style lang="scss">
  /*修复内容高度过高，外部弹窗可滚动问题*/
  .issueEdit-dialog {
    min-width: 1000px;
    // margin: 10px auto !important;
    .el-dialog__body {
      padding-top: 0px !important;
    }
  }
  .table-header-setting-form {
    .el-form-item__content {
      display: flex;
      justify-content: flex-end;
    }
  }
  .table-header-setting-drawer {
    .el-drawer__header {
      margin-bottom: 0px !important;
    }
  }
  .custom-cell-style {
    color: #67c23a;
    background: #f0f9eb;
  }
</style>

<style lang="scss" scoped>
  ////
  // 快捷创建相关
  :deep() {
    .el-popper {
      display: v-bind('showAllOverFlowTooltip');
    }

    .el-table__body tr:first-child td:nth-child(2) .cell {
      overflow: v-bind('userVisibleForOverflow');
    }

    .el-cascader__search-input {
      min-width: 0;
      max-width: 10px;
    }

    .el-cascader .el-input {
      height: 32px;
    }
  }

  .issue-table-list-subject-colomn:hover {
    .text-operation-button {
      opacity: 1;
      transition: opacity 0.3s;
    }
  }

  // 快捷修改标题icon按钮
  .text-operation-button {
    color: #999;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .text-operation-button:hover {
    color: #6192f5;
    cursor: pointer;
  }
  ////
  .el-popper.is-light {
    background: var(--el-bg-color-overlay) !important;
    border: 1px solid var(--el-border-color-light) !important;
    box-shadow: var(--el-box-shadow-light) !important;
  }
  .hoverable-item {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    font-size: 15px;
    cursor: pointer;

    /* 初始背景颜色 */
    background-color: white;
    border-radius: 6px;
    transition: background-color 0.3s;

    &:hover {
      color: $base-color-primary;
      background-color: #f0f0f0;
    }

    &.active {
      color: #3977f7; /* 点击后的字体颜色 */
    }

    .check-icon {
      margin-left: auto;
    }
  }

  // 更多筛选按钮高亮样式
  .filter-active {
    position: relative;

    .filter-badge {
      position: absolute;
      top: -8px;
      right: -8px;
      z-index: 10;
    }
  }

  :deep() {
    /* 移除表格四周的边框 */
    .el-table::before {
      display: none;
    }

    // 移除表格行内左右的border
    .el-table--enable-row-transition .el-table__body td.el-table__cell {
      border-right: none;
      border-left: none;
    }
    .el-table .cell {
      padding: 0px 6px;
    }

    /* 移除表格头部的分隔线 */
    .el-table__header-wrapper {
      border-bottom: none !important;
    }

    /* 移除表格四周外层边框 */
    .el-table__body-wrapper {
      border: none !important;
    }

    // el-table border 上边界去除
    .el-table--border .el-table__inner-wrapper::after {
      height: 0px !important;
    }

    // el-table border 左边界去除
    .el-table__border-left-patch {
      width: 0px !important;
    }

    // el-table border 右边界去除
    .el-table--border::after {
      width: 0px !important;
    }

    // el-table border 下边界去除
    .el-table__inner-wrapper::before {
      height: 0px !important;
    }

    .vab-query-form .bottom-panel {
      border-top: none;
    }
    .el-popover {
      --el-popover-title-font-size: 14px;
    }
    .expand-mode-radio-class {
      position: fixed;
      right: 28px;
    }
    .el-form-item__label {
      justify-content: left;
    }

    .el-switch {
      height: 0px;
      padding-left: 21px;
    }

    .el-table .el-table__cell {
      padding: 6px 0;
    }

    .el-pagination {
      justify-content: left;
    }

    a + a {
      margin-left: 0 !important;
    }
    .dropdown-active {
      color: var(--el-dropdown-menuItem-hover-color);
      background-color: var(--el-dropdown-menuItem-hover-fill);
    }

    .issue-subject {
      .el-tooltip {
        display: flex;
        align-items: center;
        a {
          // width: calc(100% - 30px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
        }
      }
    }
    .issue-list {
      .el-select {
        .el-select__selection {
          max-width: 100% !important;
          .el-select__selected-item {
            .is-closable {
              background-color: unset;
            }
          }
          .el-select__selected-item ~ .el-select__input {
            display: none;
          }
          .el-select__tags-text {
            font-size: 14px;
          }
          .el-tag .el-tag__close {
            // width: 0;
            margin-left: 2px;
            // opacity: 0;
            transition: width 0.3s;
          }
          // .el-tag {
          //   padding-left: 3px;
          // }
          // .el-tag.is-closable {
          //   padding-right: 3px;
          // }
          .el-select__selected-item .is-closable {
            border: 1px solid var(--el-border-color-light);
          }
        }
        .el-select__selected-item:hover .el-tag .el-tag__close {
          width: 14px !important;
          opacity: 1;
          transition: width 0.3s;
        }
      }
      .el-select.assigned-active {
        .el-select__selected-item {
          .el-tag .el-tag__close {
            width: 14px;
            opacity: 1;
          }
        }
      }
    }
  }

  :deep(.el-table__placeholder) {
    display: none; //不隐藏会导致标题列和th未对齐
  }

  .out-border {
    border-top: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    border-bottom: 1px solid #ebeef5;
    border-left: 1px solid #ebeef5;
    border-radius: $base-border-radius;
  }

  .row-select .el-scrollbar__wrap {
    min-height: 100px; /*避免下拉菜单在加载得时候，无法计算高度导致在下方弹出*/
  }

  .el-dropdown-menu {
    z-index: 9999;
  }

  //下拉菜单文字垂直居中
  .vertical-td {
    position: relative;
    align-items: center;
    width: 100%;
    height: 100%;
    min-height: 30px;
    vertical-align: middle !important;
    cursor: pointer;
    .arrow-down-icon {
      visibility: hidden;
    }
    & > div {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 8px;

      //把日期input隐藏掉
      .el-date-editor {
        position: absolute; //绝对定位
        top: 0;
        left: 0;
        opacity: 0;
      }
      .el-input__prefix {
        display: none;
      }
      .el-input__wrapper,
      .el-input__inner {
        cursor: pointer;
      }
    }
    &:hover {
      background-color: rgba(133, 146, 166, 0.1);
      border-radius: 2.5px;
      .arrow-down-icon {
        visibility: visible;
      }
    }
  }

  .oper-bar {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #fff;
    & > div {
      margin-right: 10px;
    }
  }

  :deep(.el-table__indent) {
    // padding-left: 32px !important;
    position: absolute;
  }

  :deep(.el-table__expand-icon) {
    position: absolute;
    top: 38%;
    margin-right: 2px !important;
    margin-left: -12px !important;
  }

  .module-name {
    margin-left: 10px;
    color: #999;
    background: #fff;
    border-color: #dcdfe6;
    border-radius: 6px;
  }
  // :deep(.el-link) {
  //   display: inline-flex;
  //   width: 100%;
  // }
  .color-danger {
    color: #f56c6c;
  }

  .color-warning {
    color: #e6a23c;
  }
</style>
