<template>
  <div>
    <firefly-dialog
      v-model="state.dialogFormVisible"
      :title="state.title"
      top="50px"
      width="600"
      show-default-button
      :modal="false"
      @confirm="handleBatchSave"
      :confirm-btn-disabled="isConfirmDisabled"
    >
      <el-form style="margin: 30px 0px">
        <el-form-item label="当前所属项目为：" label-width="124px">
          <span>{{ state.currentProject.name }}</span>
        </el-form-item>
        <el-form-item label="选中的事项：" label-width="124px">
          <div class="selected-issues">
            <div v-if="state.selectedIssues.length === 0" class="loading-text">
              正在加载事项信息...
            </div>
            <div v-else class="issues-list">
              <div
                v-for="issue in state.selectedIssues"
                :key="issue.id"
                class="issue-item"
              >
                <span class="issue-id">#{{ issue.id }}</span>
                <span class="issue-subject">{{ issue.subject }}</span>
                <span class="issue-project">{{ issue.project_name }}</span>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="迁移到的项目：" label-width="124px">
          <div>
            <el-select
              v-model="state.targetProjectId"
              @change="handleProjectChange"
              style="width: 400px"
              filterable
              placeholder="请选择要迁移到的项目"
            >
              <el-option
                v-for="(pItem, pKey) in state.projectList"
                :key="pKey"
                :value="pItem.id"
                :label="pItem.name"
              >
                {{ pItem.name }}
              </el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item
          v-if="state.targetProjectId"
          label="选择版本："
          label-width="124px"
        >
          <div>
            <el-select
              v-model="state.targetVersionId"
              @change="handleVersionChange"
              style="width: 400px"
              filterable
              clearable
              placeholder="请选择版本（可选）"
            >
              <el-option
                v-for="(vItem, vKey) in state.versionList"
                :key="vKey"
                :value="vItem.id"
                :label="
                  vItem.status === 'closed'
                    ? vItem.name + '（已关闭）'
                    : vItem.status === 'locked'
                    ? vItem.name + '（已锁定）'
                    : vItem.name
                "
                :disabled="
                  vItem.status === 'closed' || vItem.status === 'locked'
                "
              >
                {{
                  vItem.status === 'closed'
                    ? vItem.name + '（已关闭）'
                    : vItem.status === 'locked'
                    ? vItem.name + '（已锁定）'
                    : vItem.name
                }}
              </el-option>
            </el-select>
          </div>
        </el-form-item>

        <!-- 事项类型选择器 -->
        <el-form-item
          v-if="state.targetTrackers.length > 0"
          label="目标事项类型："
          label-width="124px"
        >
          <div>
            <el-select
              v-model="state.selectedTrackerId"
              placeholder="请选择事项类型"
              style="width: 400px"
              clearable
              @change="handleTrackerChange"
            >
              <el-option
                v-for="tracker in state.targetTrackers"
                :key="tracker.id"
                :label="tracker.name"
                :value="tracker.id"
              />
            </el-select>
            <div style="font-size: 12px; color: #909399; margin-top: 4px">
              必须选择目标事项类型
            </div>
          </div>
        </el-form-item>

        <!-- 状态选择器 -->
        <el-form-item
          v-if="state.targetStatuses.length > 0"
          label="目标状态："
          label-width="124px"
        >
          <div>
            <el-select
              v-model="state.selectedStatusId"
              placeholder="请选择状态"
              style="width: 400px"
              clearable
            >
              <el-option
                v-for="status in state.targetStatuses"
                :key="status.id"
                :label="status.name"
                :value="status.id"
              />
            </el-select>
            <div style="font-size: 12px; color: #909399; margin-top: 4px">
              必须选择目标状态
            </div>
          </div>
        </el-form-item>

        <!-- <el-form-item v-if="state.targetProjectId" label="目标项目：" label-width="124px">
          <span class="target-project-name">{{ state.targetProjectName }}</span>
        </el-form-item> -->
      </el-form>

      <!-- 迁移进度 -->
      <div v-if="state.migrationProgress.show" class="migration-progress">
        <el-divider>迁移进度</el-divider>
        <el-progress
          :percentage="state.migrationProgress.percentage"
          :status="state.migrationProgress.status"
        >
          <template #default="{ percentage }">
            <span class="percentage-value">{{ percentage }}%</span>
            <span class="progress-text">
              {{ state.migrationProgress.text }}
            </span>
          </template>
        </el-progress>

        <!-- 迁移结果详情 -->
        <div v-if="state.migrationResults.length > 0" class="migration-results">
          <div
            v-for="result in state.migrationResults"
            :key="result.id"
            class="result-item"
            :class="result.success ? 'success' : 'error'"
          >
            <vab-icon
              :icon="result.success ? 'check-circle' : 'close-circle'"
              :class="result.success ? 'success-icon' : 'error-icon'"
            />
            <span class="result-text">
              #{{ result.id }} {{ result.subject }} -
              {{ result.success ? '迁移成功' : result.error }}
            </span>
          </div>
        </div>
      </div>
    </firefly-dialog>
  </div>
</template>

<script setup>
  import { batchMigrateIssueProject } from '@/api/projectIssue'
  import { getList } from '@/api/projectIndex'
  import { getList as getVersionList } from '@/api/projectVersion'
  import { getInitialStatuses } from '@/api/customWorkflow'
  import { getProjectTrackers } from '@/api/projectTracker'
  import { ElMessageBox } from 'element-plus'

  const $baseMessage = inject('$baseMessage')
  const emit = defineEmits(['handle-batch-migration-success'])
  const $pub = inject('$pub')

  const props = defineProps({
    currentProjectId: {
      type: [Number, String],
      default: null,
    },
  })

  // 计算属性：确定按钮是否禁用
  const isConfirmDisabled = computed(() => {
    // 没有选择项目或没有选择事项类型或没有选择状态时禁用
    return (
      state.loading ||
      !state.targetProjectId ||
      !state.selectedTrackerId ||
      !state.selectedStatusId
    )
  })

  const state = reactive({
    dialogFormVisible: false,
    title: '批量迁移事项',
    loading: false,
    selectedIssueIds: [],
    selectedIssues: [],
    projectList: [],
    currentProject: { name: '加载中...' },
    targetProjectId: null,
    targetProjectName: '',
    versionList: [],
    targetVersionId: null,
    targetVersionName: '',

    // 用户选择的目标事项类型和状态
    selectedTrackerId: null,
    selectedStatusId: null,

    // 目标项目的事项类型和状态列表
    targetTrackers: [],
    targetStatuses: [],

    migrationProgress: {
      show: false,
      percentage: 0,
      status: '',
      text: '',
    },
    migrationResults: [],
  })

  /**
   * 获取项目列表
   */
  const loadProjectList = async () => {
    try {
      const { data } = await getList({
        pageNo: 1,
        pageSize: 100,
        sort: 'created_on',
        filter: {
          name: '',
          type: 'getAllProject',
        },
        op: {
          name: 'LIKE',
        },
      })
      state.projectList = data
      state.projectList = data.filter(
        (item) =>
          !item.projects_ext ||
          item.projects_ext.project_type !== 'product_type'
      )
      // 设置当前项目信息
      state.currentProject = state.projectList.find((item) => {
        return item.id == props.currentProjectId
      }) || { name: '未知项目' }
    } catch (error) {
      console.error('加载项目列表失败:', error)
      $baseMessage('加载项目列表失败', 'error')
    }
  }

  /**
   * 设置选中事项的详细信息
   */
  const setSelectedIssues = (issues) => {
    state.selectedIssues = issues
  }

  const handleProjectChange = async () => {
    const selectedProject = state.projectList.find(
      (item) => item.id === state.targetProjectId
    )
    state.targetProjectName = selectedProject ? selectedProject.name : ''

    // 清空版本选择
    state.targetVersionId = null
    state.targetVersionName = ''
    state.versionList = []

    // 清空事项类型和状态选择
    state.selectedTrackerId = null
    state.selectedStatusId = null
    state.targetTrackers = []
    state.targetStatuses = []

    // 加载选中项目的版本列表和事项类型列表
    if (state.targetProjectId) {
      await Promise.all([
        loadVersionList(state.targetProjectId),
        loadTargetTrackers(state.targetProjectId),
      ])
    }
  }

  const handleVersionChange = () => {
    const selectedVersion = state.versionList.find(
      (item) => item.id === state.targetVersionId
    )
    state.targetVersionName = selectedVersion ? selectedVersion.name : ''
  }

  /**
   * 获取目标项目的事项类型列表
   */
  const loadTargetTrackers = async (projectId) => {
    try {
      const response = await getProjectTrackers(projectId)
      state.targetTrackers = response.data || []
    } catch (error) {
      state.targetTrackers = []
      $baseMessage('加载事项类型失败', 'error')
    }
  }

  /**
   * 处理事项类型选择变化
   */
  const handleTrackerChange = async () => {
    // 清空状态选择
    state.selectedStatusId = null
    state.targetStatuses = []

    if (state.selectedTrackerId && state.targetProjectId) {
      await loadTargetStatuses(state.targetProjectId, state.selectedTrackerId)
    }
  }

  /**
   * 获取指定事项类型的状态列表
   */
  const loadTargetStatuses = async (projectId, trackerId) => {
    try {
      const response = await getInitialStatuses({
        project_id: projectId,
        tracker_id: trackerId,
      })
      state.targetStatuses = response.data || []
    } catch (error) {
      console.error('加载状态列表失败:', error)
      state.targetStatuses = []
      $baseMessage('加载状态列表失败', 'error')
    }
  }

  /**
   * 获取项目版本列表
   */
  const loadVersionList = async (projectId) => {
    try {
      const { data } = await getVersionList({
        pageNo: 1,
        pageSize: 100,
        sort: 'status',
        order: 'desc',
        filter: {
          project_id: projectId,
        },
      })
      state.versionList = data.data || []
    } catch (error) {
      console.error('加载版本列表失败:', error)
      state.versionList = []
    }
  }

  /**
   * 检测子项是否选择了父项
   */
  const checkChildParentRelation = () => {
    const childrenWithoutParent = []

    // 遍历所有选中的事项
    state.selectedIssues.forEach((issue) => {
      // 如果事项有父项ID
      if (issue.parent_id && issue.parent_id > 0) {
        // 检查父项是否也在选中列表中
        const parentSelected = state.selectedIssues.some(
          (selectedIssue) => selectedIssue.id === issue.parent_id
        )

        // 如果父项未被选中，记录这个子项
        if (!parentSelected) {
          childrenWithoutParent.push({
            id: issue.id,
            subject: issue.subject,
            parent_id: issue.parent_id,
          })
        }
      }
    })

    return childrenWithoutParent
  }

  /**
   * 批量迁移处理
   */
  const handleBatchSave = async () => {
    if (!state.targetProjectId) {
      $baseMessage('请选择目标项目', 'warning')
      return
    }

    if (state.selectedIssueIds.length === 0) {
      $baseMessage('没有选中的事项', 'warning')
      return
    }

    // 检测子项是否选择了父项
    const childrenWithoutParent = checkChildParentRelation()

    if (childrenWithoutParent.length > 0) {
      const childrenList = childrenWithoutParent
        .map((child) => `#${child.id} ${child.subject}`)
        .join('\n')

      const confirmed = await ElMessageBox.confirm(
        `检测到以下子项未选择其父项：\n\n${childrenList}\n\n迁移时将自动解除这些子项与父项的关联关系，是否继续？`,
        '确认迁移',
        {
          confirmButtonText: '继续迁移并解除关联',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: false,
        }
      ).catch(() => false)

      if (!confirmed) {
        return
      }
    }

    state.loading = true
    state.migrationProgress.show = true
    state.migrationProgress.percentage = 0
    state.migrationProgress.status = 'active'
    state.migrationProgress.text = '开始迁移...'
    state.migrationResults = []

    const totalCount = state.selectedIssueIds.length
    let successCount = 0
    let errorCount = 0

    try {
      // 检测需要解除关联的子项
      const childrenWithoutParent = checkChildParentRelation()

      // 准备批量迁移数据
      const migrationData = {
        issueIds: state.selectedIssues.map((issue) => issue.id),
        projectId: state.targetProjectId,
      }

      // 如果选择了版本，添加版本参数
      if (state.targetVersionId) {
        migrationData.versionId = state.targetVersionId
      }

      // 如果选择了事项类型，添加事项类型参数
      if (state.selectedTrackerId) {
        migrationData.trackerId = state.selectedTrackerId
      }

      // 如果选择了状态，添加状态参数
      if (state.selectedStatusId) {
        migrationData.statusId = state.selectedStatusId
      }

      // 如果有需要解除关联的子项，添加到迁移数据中
      if (childrenWithoutParent.length > 0) {
        migrationData.unlinkChildrenIds = childrenWithoutParent.map(
          (child) => child.id
        )
      }

      console.log('批量迁移数据:', migrationData)

      // 更新进度
      state.migrationProgress.percentage = 25
      state.migrationProgress.text = '正在执行批量迁移...'

      // 调用批量迁移接口（只调用一次）
      await batchMigrateIssueProject(migrationData)

      // 迁移成功，所有事项都成功
      successCount = state.selectedIssues.length
      state.selectedIssues.forEach((issue) => {
        state.migrationResults.push({
          id: issue.id,
          subject: issue.subject || `事项${issue.id}`,
          success: true,
        })
      })

      // 更新进度到100%
      state.migrationProgress.percentage = 100

      // 迁移完成
      state.migrationProgress.status = errorCount === 0 ? 'success' : 'warning'
      state.migrationProgress.text = `迁移完成：成功 ${successCount} 个，失败 ${errorCount} 个`

      if (successCount > 0) {
        $baseMessage(
          `批量迁移完成：成功 ${successCount} 个，失败 ${errorCount} 个`,
          errorCount === 0 ? 'success' : 'warning'
        )

        // 通知父组件刷新
        emit('handle-batch-migration-success')
        $pub('handle-batch-migration-success', {
          successCount,
          errorCount,
          targetProjectId: state.targetProjectId,
          targetProjectName: state.targetProjectName,
        })

        // // 批量迁移成功后刷新页面，确保筛选条件等数据更新
        // setTimeout(async () => {
        //   try {
        //     await ElMessageBox.confirm(
        //       `批量迁移完成！成功 ${successCount} 个，失败 ${errorCount} 个。\n是否刷新页面以更新筛选条件？`,
        //       '迁移完成',
        //       {
        //         confirmButtonText: '刷新页面',
        //         cancelButtonText: '稍后手动刷新',
        //         type: errorCount === 0 ? 'success' : 'warning',
        //       }
        //     )
        //     window.location.reload()
        //   } catch {
        //     // 用户点击取消，不做任何操作
        //   }
        // }, 2000) // 延迟2秒，让用户看到完整的迁移结果
      }
    } catch (error) {
      console.error('批量迁移失败:', error)
      state.migrationProgress.status = 'exception'
      state.migrationProgress.text = '迁移过程中发生错误'
      $baseMessage('批量迁移失败', 'error')
    } finally {
      //3秒后自动关闭弹窗
      setTimeout(() => {
        state.loading = false
        if (errorCount === 0) {
          close()
        }
      }, 2000)
    }
  }

  const close = () => {
    state.dialogFormVisible = false
    state.migrationProgress.show = false
    state.migrationResults = []
    state.selectedIssues = []
    state.targetProjectId = null
    state.targetProjectName = ''
    state.versionList = []
    state.targetVersionId = null
    state.targetVersionName = ''
  }

  /**
   * 显示组件
   * @param {Array} issueIds 选中的事项ID数组
   */
  const showComponent = async (issueData) => {
    if (!issueData || issueData.length === 0) {
      $baseMessage('请先选择要迁移的事项', 'warning')
      return
    }

    // 重置所有选择状态
    state.targetProjectId = null
    state.targetProjectName = ''
    state.targetVersionId = null
    state.targetVersionName = ''
    state.selectedTrackerId = null
    state.selectedStatusId = null
    state.targetTrackers = []
    state.targetStatuses = []
    state.migrationProgress.show = false
    state.migrationProgress.percentage = 0
    state.migrationResults = []

    // 如果传入的是事项对象数组，直接使用；如果是ID数组，则需要转换
    if (typeof issueData[0] === 'object') {
      state.selectedIssues = issueData
      state.selectedIssueIds = issueData.map((issue) => issue.id)
    } else {
      // 兼容旧的调用方式（只传ID数组）
      state.selectedIssueIds = issueData
      state.selectedIssues = []
    }

    state.dialogFormVisible = true

    // 加载项目列表
    await loadProjectList()
  }

  const hideComponent = () => {
    close()
  }

  defineExpose({ showComponent, hideComponent })
</script>

<style lang="scss" scoped>
  .selected-issues {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    padding: 8px;

    .loading-text {
      text-align: center;
      color: var(--el-text-color-secondary);
      padding: 20px;
    }

    .issues-list {
      .issue-item {
        display: flex;
        align-items: center;
        padding: 4px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);

        &:last-child {
          border-bottom: none;
        }

        .issue-id {
          font-weight: bold;
          color: var(--el-color-primary);
          min-width: 60px;
          margin-right: 8px;
        }

        .issue-subject {
          flex: 1;
          margin-right: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .issue-project {
          font-size: 12px;
          color: var(--el-text-color-secondary);
          background: var(--el-fill-color-light);
          padding: 2px 6px;
          border-radius: 2px;
        }
      }
    }
  }

  .target-project-name {
    font-weight: bold;
    color: var(--el-color-success);
  }

  .migration-progress {
    margin-top: 20px;

    .percentage-value {
      margin-right: 8px;
    }

    .progress-text {
      font-size: 12px;
      color: var(--el-text-color-secondary);
    }
  }

  .migration-results {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;

    .result-item {
      display: flex;
      align-items: center;
      padding: 4px 0;

      &.success {
        .success-icon {
          color: var(--el-color-success);
        }
      }

      &.error {
        .error-icon {
          color: var(--el-color-error);
        }
      }

      .result-text {
        margin-left: 8px;
        font-size: 12px;
      }
    }
  }

  :deep() {
    .el-form-item .el-form-item__label {
      margin-top: 0px !important;
    }
  }
</style>
