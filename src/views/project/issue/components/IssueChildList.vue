<template>
  <div v-if="list">
    <el-row
      class="new-child-row"
      :style="{ display: state.isAdd ? 'flex' : 'none' }"
    >
      <el-col :span="14">
        <el-input
          ref="elInputRef"
          v-model="state.form.subject"
          placeholder="子工作项标题（按回车键快速创建）,默认截止时间为版本结束时间"
          class="new-child-input"
          :style="{ border: 'none', 'box-shadow': 'none' }"
          @keydown="onKeyupSave"
        />
      </el-col>
      <el-col :span="10">
        <div class="child-col-right">
          <el-button @click="doAdd(false)">取消</el-button>
          <el-button @click="save" type="primary">创建</el-button>
        </div>
      </el-col>
    </el-row>
    <el-row v-if="state.addRelation" class="new-child-row">
      <el-col :span="14">
        <el-select
          ref="elInputRef"
          v-model="state.form.subject"
          placeholder="子工作项标题（按回车键快速创建）,默认截止时间为版本结束时间"
          :style="{ border: 'none', 'box-shadow': 'none' }"
          @keydown="onKeyupSave"
        />
        <el-select
          v-model="state.form.issue_ext.relation_project_id"
          @change="handleChangeProject"
          class="new-child-input"
          style="width: 100%"
          filterable
          clearable
          placeholder="请选择项目"
          :filter-method="filterMethod"
        >
          <el-option
            v-for="pItem in state.projectList"
            :key="pItem.id"
            :value="pItem.id"
            :label="pItem.name || ''"
          >
            {{ pItem.name }}
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="10">
        <div class="child-col-right">
          <el-button @click="doAdd(false)">取消</el-button>
          <el-button @click="save" type="primary">创建</el-button>
        </div>
      </el-col>
    </el-row>
    <el-row v-for="(citem, ckey) in list" :key="ckey" class="child-issue">
      <el-col
        :span="12"
        style="display: flex; align-items: center; color: #333"
      >
        <!-- <img
          :src="getImgByIssueTypeAndExistParent(citem)"
          style="height: 20px; margin-right: 8px"
        /> -->
        <div v-for="(item, index) in citem.level" :key="index">
          <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        </div>
        <div style="height: 20px; margin-right: 8px">
          <CommonIcon
            v-if="props.listType == 'child'"
            :issue-type="getChildIssueImg(info.tracker_id)"
          />
          <CommonIcon
            v-if="props.listType == 'relation'"
            :issue-type="getRelationIssueImg(info.tracker_id)"
          />
        </div>
        <a class="custom-link" @click="toIssuePage(citem.id, citem.project_id)">
          {{ citem.subject }}
        </a>
      </el-col>
      <el-col :span="12">
        <div class="child-col-right">
          <div style="width: 115px; background: white">
            <el-tag
              type="danger"
              size="default"
              effect="dark"
              v-if="
                citem.issue_status?.status_category !== 'completed' && // 挂起状态
                citem.due_date &&
                new Date(citem.due_date).setHours(0, 0, 0, 0) < state.nowDate
              "
              :style="{ 'border-radius': '4px', 'font-size': '14px' }"
            >
              {{
                '已超期' +
                Math.abs(
                  Math.floor(
                    (new Date(citem.due_date).setHours(0, 0, 0, 0) -
                      state.nowDate) /
                      (24 * 60 * 60 * 1000)
                  )
                ) +
                '天'
              }}
            </el-tag>
            <el-tag
              type="info"
              v-else-if="
                citem.due_date &&
                new Date(citem.due_date).setHours(0, 0, 0, 0) >= state.nowDate
              "
              :style="{
                'font-size': '14px',
                border: 'none',
                background: 'none',
              }"
            >
              {{
                '剩余' +
                Math.abs(
                  Math.floor(
                    (new Date(citem.due_date).setHours(0, 0, 0, 0) -
                      state.nowDate) /
                      (24 * 60 * 60 * 1000)
                  )
                ) +
                '天'
              }}
            </el-tag>
            <!-- <el-tag
            type="danger"
            size="default"
            effect="dark"
            v-if="citem.due_date && Date.parse(citem.due_date) < new Date()"
            :style="{ 'border-radius': '4px', 'font-size': '14px' }"
          >
            {{ citem.due_date }}已截止
          </el-tag>
          <span
            style="width: 115px"
            v-else-if="
              citem.due_date && Date.parse(citem.due_date) > new Date()
            "
          >
            {{ citem.due_date }}截止
          </span> -->
            <span v-else style="width: 100%">&nbsp;&nbsp;</span>
          </div>

          <IssueAssign
            :key="'issue-assign-' + citem.id"
            v-if="state.assignList.length > 0"
            v-model:value="citem.assigned"
            :option="state.assignList"
            :row-data="citem"
            @change="handleSave(citem, 'assigned')"
          />

          <!-- <div
            style="
              display: flex;
              align-items: center;
              margin: 0px 10px;
              width: 55px;
            "
          >
            <img
              :src="getPriorityClass(citem.priority_id)"
              style="height: 18px; margin-right: 6px"
            />
            <span>
              <template
                v-for="(eitem, ekey) in state.enumerationList"
                :key="ekey"
              >
                <template v-if="eitem.id == citem.priority_id">
                  {{ eitem.name }}
                </template>
              </template>
            </span>
          </div> -->
          <IssuePriority
            :key="'issue-priority-' + citem.id"
            v-if="state.enumerationList.length > 0"
            v-model:value="citem.priority_id"
            :option="state.enumerationList"
            @change="handleSave(citem, 'priority_id')"
          />

          <!-- <el-tag
            class="mx-1"
            size="large"
            :type="getStatusClass(citem.status_id)"
            style="width: 61px; border: none"
          >
            <template v-for="(sitem, skey) in citem.status_list" :key="skey">
              <span v-if="sitem.id == citem.status_id">{{ sitem.name }}</span>
            </template>
          </el-tag> -->
          &nbsp;
          <IssueStatus
            style="width: 80px"
            :key="'issue-status-' + citem.id"
            v-if="!state.listLoading"
            v-model:value="citem.status_id"
            :row-data="citem"
            :option="citem.status_list"
            @change="handleSave(citem, 'status_id')"
          />
          &nbsp;
          <el-tooltip
            content="解除父子关系"
            :hide-after="50"
            placement="top-start"
          >
            <a @click="unLinkChild(citem)" style="color: #999; cursor: pointer">
              <CommonIcon :type="'release'" />
            </a>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <edit-dialog ref="editRef" :height="dialogHeight - 100" />
  </div>
</template>

<script setup>
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import IssueStatus from '@/components/IssueStatus.vue'
  import IssuePriority from '@/components/IssuePriority.vue'
  import IssueAssign from '@/components/IssueAssign.vue'
  import { doEdit, getEnumerationList } from '@/api/projectIssue'
  import EditDialog from './IssueEditDialog.vue'
  import { getList as getVersionList } from '@/api/projectVersion'

  import {
    getImgByIssueType,
    IssueIconParent,
    IssueIconChild,
    IssueIconRelation,
  } from '@/utils/index'
  import { stat } from 'fs'

  const emit = defineEmits(['fetch-overview'])

  const props = defineProps({
    info: {
      type: Object,
      default: () => {
        return {}
      },
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    parentId: {
      type: Number,
      default: null,
    },
    openType: {
      type: String,
      default: 'page',
    },
    listType: {
      type: String,
      // child=子事项, relation=关联事项
      default: 'child',
    },
    fixedVersionId: {
      type: Number,
      default: null,
    },
    assignOptions: {
      type: Array,
      default: () => {
        return {}
      },
    },
    extendWatcher: {
      type: Number,
      default: 0,
    },
  })

  const { info, assignOptions, list, extendWatcher } = toRefs(props)
  const extend = ref(extendWatcher)
  watch(info, (v) => {
    // info.value = v
    state.form.parent_id = v.id
    state.form.tracker_id = v.tracker_id
    state.form.project_id = v.project_id
    state.form.watcher_count = v.watcher_count
    state.form.watchers = v.watchers
  })
  watch(assignOptions, (v) => {
    // info.value = v
    state.assignList = v
  })
  watch(extendWatcher, (v) => {
    extend.value = v
  })
  const childIssueIcon = {
    0: require('@/assets/icon_images/demand.png'),
    11: require('@/assets/icon_images/childissue-requirement.png'),
    12: require('@/assets/icon_images/childissue-task.png'),
    20: require('@/assets/icon_images/childissue-bug.png'),
  }

  const getImgByIssueTypeAndExistParent = (row) => {
    if (row.parent_id && row.parent_id > 0) {
      let parent = info.value
      if (parent) {
        return childIssueIcon[parent.tracker_id] || childIssueIcon[0]
      } else {
        return getImgByIssueType(row.tracker_id)
      }
    } else {
      return getImgByIssueType(row.tracker_id)
    }
  }

  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')
  const $pub = inject('$pub')

  /**
   * 子事项图标
   */
  const getChildIssueImg = (id) => {
    return IssueIconChild[id] || IssueIconChild[0]
  }

  /**
   * 相关事项图标
   */
  const getRelationIssueImg = (id) => {
    return IssueIconRelation[id] || IssueIconRelation[0]
  }

  const dialogHeight = computed(() => {
    const h = document.body.clientHeight - 40
    return h
  })
  const state = reactive({
    addRelation: false,
    assignList: props.assignOptions ?? [],
    nowDate: new Date().setHours(0, 0, 0, 0),
    isAdd: false,
    form: {
      subject: null,
      parent_id: props.info.id,
      tracker_id: props.info.tracker_id,
      project_id: props.info.project_id,
      status_id: 1,
      priority_id: 3,
      description: '',
      category_id: props.info.category_id,
      watcher_count: props.info.watcher_count,
      watchers: props.info.watchers,
      class_id: props.info.class_id,
    },
    enumerationList: [],
    statusClassList: {
      1: '',
      2: '',
      6: '',
      7: '',
      8: 'warning',
      9: 'warning',
      10: 'warning',
      3: 'success',
      4: 'success',
      5: 'success',
    },
    priorityClassList: {
      2: require('@/assets/icon_images/low.png'),
      3: require('@/assets/icon_images/normal.png'),
      4: require('@/assets/icon_images/high.png'),
      5: require('@/assets/icon_images/urgent.png'),
      6: require('@/assets/icon_images/immediate.png'),
    },
  })

  const editRef = ref(null)
  const elInputRef = ref(null)

  /**
   * 问题详情页面
   */
  const toIssuePage = (id, project_id) => {
    // if (props.openType == 'page') {
    //   let row = info.value.children.find((r) => {
    //     if (r.id == id) {
    //       return r
    //     }
    //   })
    //   editRef.value.showEdit(row)
    // } else {
    window.open(
      '/#/project/detail?issue_id=' + id + '&project_id=' + project_id
    )
    // }
  }

  const getStatusClass = (value) => {
    return typeof state.statusClassList[value] != 'undefined'
      ? state.statusClassList[value]
      : ''
  }
  const getPriorityClass = (value) => {
    return typeof state.priorityClassList[value] != 'undefined'
      ? state.priorityClassList[value]
      : ''
  }

  const handleEnumerationList = async () => {
    const { data } = await getEnumerationList()
    state.enumerationList = data
  }

  const doAdd = (value) => {
    if (value === false) {
      state.form.subject = null
      // state.form = {
      //   subject: null,
      //   parent_id: null,
      //   tracker_id: null,
      //   project_id: null,
      //   status_id: 1,
      //   priority_id: 3,
      //   description: '',
      //   children: null,
      // }
    }
    state.isAdd = value
    if (value == true) {
      elInputRef.value.focus()
    }
  }

  /**
   * 解除关联
   */
  const unLinkChild = (row) => {
    $baseConfirm('是否确定取消关联', null, () => {
      doEdit({ id: row['id'], parent_id: '' }).then(() => {
        $pub('issue-edit-success', [null, 'parent_id'])
        emit('fetch-overview')
        $baseMessage('修改成功', 'success', 'vab-hey-message-success')
      })
    })
  }

  const save = async () => {
    // 子事项没填写标题
    if (!state.form.subject) {
      $baseMessage('标题不能为空！', 'error', 'vab-hey-message-error')
      return
    }
    // 新增的子事项跟随父事项版本
    if (props.fixedVersionId != null) {
      state.form.fixed_version_id = props.fixedVersionId
      const pId = state.form.project_id
      // 新建事项，如果有版本，预计完成时间默认为版本结束时间
      const {
        data: { data },
      } = await getVersionList({
        limit: 100,
        filter: { project_id: pId, 'versions.status': 'open' },
      })
      data.forEach((item) => {
        if (item.id == state.form.fixed_version_id) {
          state.form.due_date = item.effective_date
        }
      })
    }
    let params = state.form
    if (!extend.value) {
      delete params.watcher_count
      delete params.watchers
    }
    // console.log('新增子事项', state.form)
    doEdit(params)
      .then((res) => {
        // console.log('issue-edit-success add-child-success', props.openType)
        // if (props.openType == 'window') {
        //   // 1.窗口页面时
        //   // 订阅刷新列表页面
        //   $pub('issue-edit-success', [null, null])
        // } else {
        //   // 2.页面模式时，刷新详情数据
        $pub('add-child-success')
        emit('fetch-overview')
        // }
        $baseMessage(res.msg, 'success', 'vab-hey-message-success')
        // doAdd(false)
        state.form.subject = ''
      })
      .catch(() => {
        state.btnLoading = false
      })
      .finally(() => {
        // state.formDisabled = true
        // //保存完后，变为初始查看状态
        // state.btnLoading = false
      })
  }

  const onKeyupSave = (e) => {
    if (
      typeof e.key != 'undefined' &&
      e.key == 'Enter' &&
      state.form.subject &&
      state.form.subject != ''
    ) {
      save()
    }
  }

  /**
   * @description 根据字段更新
   */
  const handleSave = async (row, field, value) => {
    let newValue = row[field]
    if (value) {
      //部分下拉的逻辑可能不是model，需由该参数传进来
      newValue = value
    }
    doEdit({ id: row['id'], [field]: newValue }).then(() => {
      $baseMessage('修改成功', 'success', 'vab-hey-message-success')
      // fetchdata
      $pub('issue-edit-success', [row, field])
    })
  }

  onMounted(async () => {
    handleEnumerationList()
  })
  defineExpose({
    doAdd,
  })
</script>

<style lang="scss" scoped>
  :deep() {
    .child-issue:not(:last-child) {
      max-height: 30px;
      margin-bottom: 5px;
    }
    .new-child-input {
      .el-input__wrapper {
        box-shadow: none;
      }
    }
  }
  .new-child-row {
    padding: 7px 10px;
    margin-top: 8px;
    margin-bottom: 5px;
    border: 1px solid #3977f3;
    border-radius: 6px 6px 6px 6px;
  }
  .child-issue {
    align-items: center;
    padding: 7px 10px;
    // background: #f7f8fa;
    border-radius: 6px 6px 6px 6px;
  }
  .child-col-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    color: #7e899d;
  }
  .custom-link {
    overflow: hidden; /* 隐藏溢出的文本 */
    text-overflow: ellipsis; /* 显示省略号 */
    white-space: nowrap; /* 防止文本换行 */
    cursor: pointer;
    &:hover {
      color: $base-color-primary;
    }
  }
</style>
