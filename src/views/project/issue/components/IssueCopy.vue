<template>
  <div class="issue-copy-container">
    <!-- 项目菜单 -->
    <project-menu v-model:active-name="activeName" />

    <!-- 主内容区域 -->
    <vab-card
      shadow="never"
      class="issue-copy-main-content issue-copy-custom-scope"
    >
      <!-- 页面标题和项目选择器 -->
      <div class="issue-copy-page-header">
        <h2 class="issue-copy-page-title">复制需求</h2>
        <div class="issue-copy-project-selector">
          <el-form-item
            label="请选择目标项目："
            class="issue-copy-project-form-item"
            label-position="left"
          >
            <el-select
              v-model="state.targetProjectId"
              filterable
              :placeholder="
                state.projectListLoading
                  ? '正在加载项目列表...'
                  : '请选择目标项目'
              "
              style="width: 280px"
              @change="handleProjectChange"
              :loading="state.projectListLoading"
              :disabled="state.projectListLoading"
              popper-class="issue-copy-project-select-dropdown"
            >
              <el-option
                v-for="item in projectOptions"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
                <span>
                  <span
                    v-if="item.level > 0"
                    :style="{ width: `${item.level * 16}px` }"
                  ></span>
                  <span v-if="item.level > 0">└─</span>
                  <span>{{ item.name }}</span>
                  <span v-if="item.id === state.currentProjectId">
                    （当前项目）
                  </span>
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 事项卡片列表 -->
      <div
        class="issue-copy-cards-container"
        v-loading="state.loading || state.addingChildIssues"
        element-loading-text="正在处理事项数据..."
        element-loading-spinner="el-icon-loading"
      >
        <div
          v-for="(issue, index) in state.issueList"
          :key="issue.id"
          class="issue-copy-card"
        >
          <!-- 卡片标题 -->
          <div class="issue-copy-card-header">
            <div class="issue-copy-card-title">
              <TrackerTypeTag
                v-if="state.trackerIdToIdentifierMap[issue.tracker_id]?.trim()"
                :name="issue.issue_type?.name"
                :tracker-type="issue.issue_type?.tracker_type"
                :color="issue.issue_type?.color"
                :identifier="issue.issue_type?.identifier"
                variant="filled"
                size="small"
                style="margin-right: 12px; margin-top: -1px"
              />
              <CommonIcon
                v-else
                style="margin-right: 16px"
                :issue-type="getIssueType(issue)"
                class="issue-copy-issue-icon"
              />

              <span class="issue-copy-issue-id">#{{ issue.id }}</span>
              <!-- 双击编辑标题 -->
              <span
                v-if="!issue.isEditingSubject"
                class="issue-copy-issue-title"
                @dblclick="startEditSubject(issue, index)"
                :title="'双击编辑标题'"
              >
                {{ issue.subject }}
              </span>
              <el-input
                v-else
                :ref="(el) => setSubjectInputRef(el, index)"
                v-model="issue.editingSubject"
                class="issue-copy-subject-input"
                size="small"
                @blur="handleSubjectBlur(issue)"
                @keydown.enter="saveSubject(issue)"
                @keydown.esc="cancelEditSubject(issue)"
                autofocus
              />
            </div>
          </div>

          <!-- 卡片内容 -->
          <div class="issue-copy-card-content">
            <el-form
              :model="issue"
              label-width="88px"
              class="issue-copy-issue-form"
            >
              <div class="issue-copy-card-content-item-1">
                <!-- 基础字段网格布局 -->
                <div class="issue-copy-form-grid">
                  <el-form-item label="事项类型">
                    <el-select
                      :model-value="getSelectValue(issue.tracker_id)"
                      @update:model-value="
                        (value) => setSelectValue(value, 'tracker_id', issue)
                      "
                      placeholder="请选择事项类型"
                      clearable
                    >
                      <el-option
                        v-for="item in trackerOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>

                  <el-form-item label="优先级">
                    <el-select
                      :model-value="getSelectValue(issue.priority_id)"
                      @update:model-value="
                        (value) => setSelectValue(value, 'priority_id', issue)
                      "
                      placeholder="请选择优先级"
                      clearable
                      popper-class="issue-priority-option-list-style"
                    >
                      <template #prefix v-if="issue.priority_id">
                        <el-tag
                          class="work-status-tag"
                          :type="priorityTagType[issue.priority_id]"
                          style="min-width: 44px; border: none"
                        >
                          <span style="font-weight: 700">
                            {{ priorityTagName[issue.priority_id] }}
                          </span>
                        </el-tag>
                      </template>
                      <el-option
                        v-for="item in state.priorityList"
                        :key="item.id"
                        :label="' '"
                        :value="item.id"
                      >
                        <div class="flex-algin-center">
                          <el-tag
                            class="work-status-tag"
                            :type="priorityTagType[item.id]"
                            style="min-width: 44px; border: none"
                          >
                            <span style="font-weight: 700">
                              {{ priorityTagName[item.id] }}
                            </span>
                          </el-tag>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item
                    label="父事项"
                    v-if="!issue.isChildIssue && !isOriginalChildIssue(issue)"
                  >
                    <SelectParent
                      :ref="(el) => setSelectParentRef(el, index)"
                      :project-id="state.targetProjectId"
                      :issue-id="issue.id"
                      :traker-id="issue.tracker_id"
                      :issue-partent-id="issue.parent_id"
                      :show-dialog="false"
                      :show-border="true"
                      @handle-parent-issue="
                        (parentIssue) =>
                          handleParentIssueChange(issue, parentIssue)
                      "
                    />
                  </el-form-item>

                  <el-form-item label="状态">
                    <el-select
                      :model-value="getSelectValue(issue.status_id)"
                      @update:model-value="
                        (value) => setSelectValue(value, 'status_id', issue)
                      "
                      placeholder="请选择状态"
                      clearable
                    >
                      <template #prefix v-if="issue.status_id">
                        <el-tag
                          class="ml-2 work-status-tag"
                          :type="getStatusClass(issue.status_id)"
                          style="border: none"
                        >
                          {{
                            (issue.statusList || []).find(
                              (item) =>
                                (item.id || item.value) === issue.status_id
                            )?.name ||
                            (issue.statusList || []).find(
                              (item) =>
                                (item.id || item.value) === issue.status_id
                            )?.label ||
                            ''
                          }}
                        </el-tag>
                      </template>
                      <el-option
                        v-for="item in issue.statusList || []"
                        :key="item.id || item.value"
                        :label="' '"
                        :value="item.id || item.value"
                      >
                        <el-tag
                          class="ml-2 work-status-tag"
                          :type="getStatusClass(item.id || item.value)"
                          style="border: none"
                        >
                          {{ item.name || item.label }}
                        </el-tag>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="版本">
                    <el-select
                      :model-value="getSelectValue(issue.fixed_version_id)"
                      @update:model-value="
                        (value) =>
                          setSelectValue(value, 'fixed_version_id', issue)
                      "
                      placeholder="请选择版本"
                      clearable
                    >
                      <el-option
                        v-for="item in state.versionList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      >
                        {{ item.name }}
                        <span
                          v-if="item.status == 'closed'"
                          class="issue-copy-version-status issue-copy-version-closed"
                        >
                          [已关闭]
                        </span>
                        <span
                          v-if="item.status == 'locked'"
                          class="issue-copy-version-status issue-copy-version-locked"
                        >
                          [已锁定]
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="处理人">
                    <IssueAssign
                      v-if="memberOptions.length > 0"
                      v-model:value="issue.assigned"
                      :option="memberOptions"
                      :row-data="issue"
                      :use-immediate-option="true"
                      :min-width="'195px'"
                      :no-border="false"
                      @change="handleAssignedChange(issue)"
                    />
                  </el-form-item>
                </div>

                <!-- 创建人选择 -->
                <el-form-item
                  label="创建人"
                  class="issue-copy-author-selection"
                >
                  <el-button-group class="issue-copy-author-button-group">
                    <el-button
                      :type="issue.authorOption === 'original' ? 'primary' : ''"
                      :plain="issue.authorOption !== 'original'"
                      size="small"
                      @click="handleAuthorOptionChange(issue, 'original')"
                    >
                      保留原需求的创建人
                    </el-button>
                    <el-button
                      :type="issue.authorOption === 'current' ? 'primary' : ''"
                      :plain="issue.authorOption !== 'current'"
                      size="small"
                      @click="handleAuthorOptionChange(issue, 'current')"
                    >
                      我（{{ state.currentUser.name }}）
                    </el-button>
                  </el-button-group>
                </el-form-item>
              </div>
              <!-- 展开更多字段 -->
              <div style="background: #f5f5f5">
                <div class="issue-copy-expandable-section">
                  <el-button
                    type=""
                    @click="toggleExpanded(index)"
                    class="issue-copy-toggle-button"
                    :class="{
                      'issue-copy-toggle-button-collapsed': issue.expanded,
                    }"
                    link
                  >
                    <el-icon>
                      <component
                        :is="issue.expanded ? 'ArrowUp' : 'ArrowDown'"
                      />
                    </el-icon>
                    {{ issue.expanded ? '收起字段' : '展开更多字段' }}
                  </el-button>

                  <div
                    v-show="issue.expanded"
                    class="issue-copy-expanded-content"
                  >
                    <!-- 扩展字段网格布局 -->
                    <div class="issue-copy-expanded-form-grid">
                      <!-- 所属分类/阶段 -->
                      <el-form-item
                        :label="
                          issue.tracker_id == 21 ? '所属阶段' : '所属分类'
                        "
                        v-if="issue.tracker_id == 11 || issue.tracker_id == 21"
                      >
                        <el-cascader
                          v-model="issue.class_id"
                          :options="state.issueClassList"
                          @change="handleClassChange(issue)"
                          :show-all-levels="false"
                          :props="{
                            value: 'id',
                            label: 'name',
                            checkStrictly: true,
                          }"
                          placeholder="请选择分类"
                          clearable
                        />
                      </el-form-item>

                      <!-- 所属模块 -->
                      <el-form-item label="所属模块">
                        <el-select
                          :model-value="getSelectValue(issue.category_id)"
                          @update:model-value="
                            (value) =>
                              setSelectValue(value, 'category_id', issue)
                          "
                          placeholder="请选择模块"
                          clearable
                        >
                          <el-option
                            v-for="item in state.categoryList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          />
                        </el-select>
                      </el-form-item>

                      <!-- 关注人 -->
                      <el-form-item
                        label="关注人"
                        class="issue-copy-full-width"
                      >
                        <IssueAssign
                          v-if="watcherOptions.length > 0"
                          v-model:value="issue.watchers"
                          :option="watcherOptions"
                          :row-data="issue"
                          :use-immediate-option="true"
                          :min-width="'195px'"
                          :no-border="false"
                          :field-name="'watchers'"
                          @change="handleWatchersChange(issue)"
                        />
                      </el-form-item>

                      <el-form-item label="开始日期">
                        <el-date-picker
                          v-model="issue.start_date"
                          type="date"
                          placeholder="请选择开始日期"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>

                      <el-form-item label="完成日期">
                        <el-date-picker
                          v-model="issue.due_date"
                          type="date"
                          placeholder="请选择完成日期"
                          value-format="YYYY-MM-DD"
                        />
                      </el-form-item>

                      <!-- 自定义字段 -->
                      <!-- <template
                      v-if="
                        issue.custom_fields && issue.custom_fields.length > 0
                      "
                    >
                      <el-form-item
                        v-for="(field, fieldIndex) in issue.custom_fields"
                        :key="fieldIndex"
                        :label="field.field.name"
                      >
                        <template v-if="field.field.field_format === 'list'">
                          <el-select
                            v-model="field.value"
                            :multiple="field.field.multiple === 1"
                            :placeholder="'请选择' + field.field.name"
                          >
                            <el-option
                              v-for="(val, valIndex) in field.field
                                .possible_values"
                              :key="valIndex"
                              :label="val"
                              :value="val"
                            />
                          </el-select>
                        </template>
                        <template
                          v-else-if="field.field.field_format === 'user'"
                        >
                          <el-select
                            v-model="field.value"
                            :multiple="field.field.multiple === 1"
                            :placeholder="'请选择' + field.field.name"
                          >
                            <el-option
                              v-for="(val, valIndex) in field.field
                                .possible_values"
                              :key="valIndex"
                              :label="val.name"
                              :value="val.value"
                            />
                          </el-select>
                        </template>
                        <template v-else>
                          <el-input
                            v-model="field.value"
                            :placeholder="'请输入' + field.field.name"
                          />
                        </template>
                      </el-form-item>
                    </template> -->
                    </div>
                  </div>
                </div>
              </div>
            </el-form>
          </div>
        </div>
      </div>

      <div
        v-if="issueIds.length === 1 && state.hasChildIssues"
        class="issue-copy-checkbox-simple"
      >
        <el-checkbox
          v-model="state.copyChildIssues"
          label="复制子事项"
          @change="handleCopyChildIssuesChange"
          :disabled="state.addingChildIssues"
        />
        <span v-if="state.addingChildIssues" class="issue-copy-loading-text">
          正在添加子事项...
        </span>
      </div>
      <!-- 复制选项面板 -->
      <div class="issue-copy-options-panel">
        <!-- <div class="issue-copy-panel-title">复制选项</div> -->
        <div class="issue-copy-panel-content">
          <div class="issue-copy-switch-item">
            <el-switch v-model="state.copyOptions.copy_attachments" />
            <span class="issue-copy-switch-label">复制附件</span>
          </div>
          <div class="issue-copy-switch-item">
            <el-switch v-model="state.copyOptions.link_to_original" />
            <span class="issue-copy-switch-label">关联原事项</span>
          </div>
          <!-- <div class="issue-copy-switch-item">
            <el-switch v-model="state.copyOptions.copy_children" />
            <span class="issue-copy-switch-label">复制子事项</span>
          </div> -->
          <!-- 同步更新字段信息 -->
          <div class="issue-copy-switch-item">
            <el-switch v-model="state.copyOptions.enable_sync" />
            <span class="issue-copy-switch-label">同步更新字段信息</span>
          </div>
          <!-- 同步选项详细配置 -->
          <div
            v-if="state.copyOptions.enable_sync"
            class="issue-copy-sync-options"
          >
            <!-- 同步方向选择 -->
            <div class="issue-copy-sync-direction">
              <span class="issue-copy-sync-label">同步方向：</span>
              <el-radio-group
                v-model="state.copyOptions.sync_direction"
                size="small"
              >
                <el-radio-button label="source_to_target">
                  源到目标
                </el-radio-button>
                <el-radio-button label="target_to_source">
                  目标到源
                </el-radio-button>
                <el-radio-button label="bidirectional">
                  双向同步
                </el-radio-button>
              </el-radio-group>
            </div>
            <!-- 同步字段选择 -->
            <div class="issue-copy-sync-fields">
              <span class="issue-copy-sync-label">同步字段：</span>
              <el-checkbox-group
                v-model="state.copyOptions.sync_fields"
                size="small"
              >
                <el-checkbox label="subject">标题</el-checkbox>
                <el-checkbox label="description">描述</el-checkbox>
                <el-checkbox label="attachment">附件</el-checkbox>
                <el-checkbox label="status_id">状态</el-checkbox>
                <el-checkbox label="priority_id">优先级</el-checkbox>
                <el-checkbox label="watchers">关注人</el-checkbox>
                <el-checkbox label="assigned">处理人</el-checkbox>
                <el-checkbox label="start_date">开始时间</el-checkbox>
                <el-checkbox label="due_date">结束时间</el-checkbox>
                <el-checkbox label="tracker_id">事项类型</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="issue-copy-action-buttons">
        <el-button
          type="primary"
          :loading="state.submitLoading"
          @click="handleSubmit"
        >
          提交
        </el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
    </vab-card>
  </div>
</template>

<script setup>
  import { reactive, ref, onMounted, inject, computed, nextTick } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useUserStore } from '@/store/modules/user'
  import { storeToRefs } from 'pinia'
  import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
  import ProjectMenu from '@/components/ProjectMenu.vue'
  import CommonIcon from '@/components/CommonIcon.vue'
  import SelectParent from '@/components/SelectParent.vue'
  import IssueAssign from '@/components/IssueAssign.vue'
  import TrackerTypeTag from '~/src/components/TrackerTypeTag.vue'
  import {
    getIssueInfo,
    getNewIssueStatus,
    getIssueStatus,
    batchCopyIssue,
    getMemberList,
    getEnumerationList,
    getIssueType as getIssueTypeApi,
  } from '@/api/projectIssue'
  import { getInitialStatuses } from '@/api/customWorkflow'
  import {
    getList as getProjectList,
    getJoinedProjectList,
  } from '@/api/projectIndex'
  import { getList as getVersionList } from '@/api/projectVersion'
  import { getList as getCategoryList } from '@/api/projectIssueCategories'
  import { getList as getIssueList } from '@/api/projectIssue'
  import { getList as getIssueClassListData } from '@/api/projectIssueClass'
  import { cloneDeep } from 'lodash'
  import { IssueIconParent, IssueIconChild } from '@/utils/index'
  import {
    priorityTagType,
    priorityTagName,
    issueStatusClass,
  } from '@/json/issues'

  const $baseMessage = inject('$baseMessage')
  const route = useRoute()
  const router = useRouter()
  const userStore = useUserStore()
  const { redmine } = storeToRefs(userStore)

  // 从路由获取参数
  const issueIds = computed(() => {
    const ids = route.query.ids
    if (typeof ids === 'string') {
      return ids.split(',').map((id) => parseInt(id))
    }
    return Array.isArray(ids) ? ids.map((id) => parseInt(id)) : []
  })

  const currentProjectId = computed(() => {
    return parseInt(route.query.project_id) || null
  })

  // 计算属性：处理项目选项列表（支持层级结构）
  const projectOptions = computed(() => {
    if (!state.projectList || state.projectList.length === 0) {
      return []
    }

    const flattenProjects = (projects, level = 0) => {
      const result = []

      projects.forEach((item) => {
        // 添加当前项目
        const projectItem = {
          id: item.id,
          name: item.name,
          label:
            '　'.repeat(level) +
            item.name +
            (item.id === state.currentProjectId ? '（当前项目）' : ''),
          value: item.id,
          level: level,
          isParent: item.children && item.children.length > 0,
        }
        result.push(projectItem)

        // 递归处理子项目
        if (item.children && item.children.length > 0) {
          result.push(...flattenProjects(item.children, level + 1))
        }
      })

      return result
    }

    return flattenProjects(state.projectList)
  })

  // 计算属性：处理成员选项列表（适配IssueAssign组件）
  const memberOptions = computed(() => {
    if (!state.memberList || state.memberList.length === 0) {
      return []
    }

    return state.memberList.map((item) => ({
      id: item.user_id,
      name: item.user?.name || '未知用户',
      thumb_avatar:
        item.user?.thumb_avatar || require('@/assets/cropper_images/user.gif'),
    }))
  })

  // 计算属性：处理关注人选项列表（适配IssueAssign组件）
  const watcherOptions = computed(() => {
    if (!state.assignOptions || state.assignOptions.length === 0) {
      return []
    }

    return state.assignOptions.map((item) => ({
      id: item.user_id,
      name: item.user?.name || '未知用户',
      thumb_avatar:
        item.user?.thumb_avatar || require('@/assets/cropper_images/user.gif'),
    }))
  })

  // 计算属性：处理0值字段，确保0值时显示为空
  const getSelectValue = (value) => {
    return value === 0 ? null : value
  }

  const setSelectValue = async (value, fieldName, issue) => {
    const oldValue = issue[fieldName]
    issue[fieldName] = value || 0

    // 如果是事项类型字段发生变化，需要重新加载状态列表
    if (fieldName === 'tracker_id' && oldValue !== (value || 0)) {
      await reloadIssueStatusList(issue)
      // 重置状态为第一个可用状态
      if (issue.statusList && issue.statusList.length > 0) {
        issue.status_id = issue.statusList[0].value
      }
    }
  }

  // 检测事项是否是原始的子事项（在批量选择时有父子关系）
  const isOriginalChildIssue = (issue) => {
    // 如果当前事项没有 parent_id，则不是子事项，显示父事项选择框
    if (!issue.parent_id) return false

    // 如果是通过勾选添加的子事项，不隐藏父事项选择框
    if (issue.isChildIssue) return false

    // 检查当前事项的 parent_id 是否存在于当前选中的事项列表中
    const parentExistsInList = state.issueList.some(
      (item) => item.id === issue.parent_id && !item.isChildIssue
    )

    // 如果parent_id存在于当前选中列表中，则隐藏父事项选择框（保持原有父子关系）
    // 如果parent_id不存在于当前选中列表中，则显示父事项选择框（允许重新选择父事项）
    return parentExistsInList
  }

  const activeName = ref('')

  const state = reactive({
    loading: false,
    submitLoading: false,
    targetProjectId: currentProjectId.value,
    currentProjectId: currentProjectId.value,
    issueList: [],
    projectList: [],
    versionList: [],
    statusList: [],
    // 重构：状态数据管理
    statusCache: new Map(), // 缓存不同project_id和tracker_id组合的状态数据
    statusLoading: new Set(), // 记录正在加载状态的组合，避免重复请求
    categoryList: [],
    priorityList: [],
    trackerList: [], // 新增：事项类别列表
    trackerIdToIdentifierMap: [], // 新增：tracker_id到identifier的映射
    trackerListLoading: false, // 新增：tracker列表加载状态

    memberList: [],
    issueClassList: [], // 新增：所属分类/阶段列表
    assignOptions: [], // 新增：关注人选项列表
    selectParentRefs: [], // SelectParent 组件引用数组
    currentUser: {
      name: redmine.value?.name || '当前用户',
      third_id: redmine.value?.third_id,
    },
    copyOptions: {
      copy_attachments: true,
      link_to_original: true,
      copy_description: true,
      copy_children: false,
      enable_sync: false,
      sync_direction: 'bidirectional', // 默认双向同步
      sync_fields: [], // 默认不选择任何字段
    },
    projectListLoading: false,
    memberListLoading: false,
    // 新增：子事项复制相关状态
    copyChildIssues: false,
    hasChildIssues: false,
    originalIssueChildren: [],
    addingChildIssues: false, // 添加子事项的加载状态

    // 新增：保存初始状态用于恢复
    initialFieldStates: new Map(), // 保存每个事项的初始字段状态
  })

  // 状态类名映射对象
  const classObj = {
    1: 'status-new',
    2: 'status-in-progress',
    3: 'status-resolved',
    4: 'status-feedback',
    5: 'status-closed',
    6: 'status-rejected',
    // 可以根据实际项目需要添加更多状态类名
  }

  // 获取事项类型
  const getIssueType = (issue) => {
    // 根据事项是否有父级来判断使用哪种图标

    return IssueIconParent[issue.tracker_id] ?? IssueIconParent[11]
  }

  // 切换展开状态
  const toggleExpanded = (index) => {
    state.issueList[index].expanded = !state.issueList[index].expanded
  }

  // 设置 SelectParent 组件引用
  const setSelectParentRef = (el, index) => {
    if (el) {
      state.selectParentRefs[index] = el
    }
  }

  // 处理父级事项变更
  const handleParentIssueChange = (issue, parentIssue) => {
    if (parentIssue === '' || parentIssue === null) {
      // 清除父级事项
      issue.parent_id = null
    } else if (typeof parentIssue === 'object') {
      // 设置父级事项
      issue.parent_id = parentIssue.id
    } else {
      // 直接传入ID
      issue.parent_id = parentIssue
    }
  }

  // 初始化所有 SelectParent 组件
  const initSelectParentComponents = async () => {
    await nextTick()
    // 等待所有组件渲染完成
    await new Promise((resolve) => setTimeout(resolve, 100))

    state.issueList.forEach((issue, index) => {
      const selectParentRef = state.selectParentRefs[index]
      if (selectParentRef && issue.parent_id) {
        selectParentRef.getInitData()
      }
    })
  }

  // 获取项目列表
  const getProjects = async () => {
    state.projectListLoading = true
    try {
      const res = await getProjectList({
        filter: {
          name: '',
          projectsExt: {
            project_type: 'product_type',
          },
        },
        op: {
          name: 'LIKE',
          projectsExt: {
            project_type: '<>',
          },
        },
      })
      state.projectList = res.data || []
    } catch (error) {
      console.error('获取项目列表失败:', error)
      $baseMessage('获取项目列表失败', 'error', 'vab-hey-message-error')
    } finally {
      state.projectListLoading = false
    }
  }

  // 获取优先级列表
  const getPriorities = async () => {
    try {
      const res = await getEnumerationList({
        type: 'IssuePriority',
      })
      state.priorityList = res.data || []
    } catch (error) {
      console.error('获取优先级列表失败:', error)
      state.priorityList = []
    }
  }

  // 获取版本列表
  const getVersions = async (projectId) => {
    try {
      const res = await getVersionList({
        limit: 100,
        filter: { project_id: projectId },
      })
      state.versionList = res.data.data || []
    } catch (error) {
      console.error('获取版本列表失败:', error)
      state.versionList = []
    }
  }

  // // 获取状态列表
  // const getStatuses = async (projectId) => {
  //   try {
  //     // 获取项目相关的状态列表
  //     const res = await getNewIssueStatus({
  //       project_id: projectId,
  //     })
  //     state.statusList = res.data || []
  //   } catch (error) {
  //     console.error('获取状态列表失败:', error)
  //     state.statusList = []
  //   }
  // }

  // 生成状态缓存键
  const getStatusCacheKey = (projectId, trackerId) => {
    return `${projectId}-${trackerId}`
  }

  // 获取指定项目和事项类型的状态列表
  const getStatusList = async (projectId, trackerId) => {
    const cacheKey = getStatusCacheKey(projectId, trackerId)

    // 检查缓存
    if (state.statusCache.has(cacheKey)) {
      return state.statusCache.get(cacheKey)
    }

    // 检查是否正在加载，避免重复请求
    if (state.statusLoading.has(cacheKey)) {
      // 等待加载完成
      return new Promise((resolve) => {
        const checkCache = () => {
          if (state.statusCache.has(cacheKey)) {
            resolve(state.statusCache.get(cacheKey))
          } else {
            setTimeout(checkCache, 100)
          }
        }
        checkCache()
      })
    }

    // 标记为正在加载
    state.statusLoading.add(cacheKey)

    try {
      const { data } = await getInitialStatuses({
        project_id: projectId,
        tracker_id: trackerId,
      })

      const statusList = data.map((item) => ({
        label: item.name,
        value: item.id,
        id: item.id,
        name: item.name,
        className: classObj[item.id],
      }))

      // 缓存结果
      state.statusCache.set(cacheKey, statusList)

      return statusList
    } catch (error) {
      console.error(
        `获取状态列表失败 (project_id: ${projectId}, tracker_id: ${trackerId}):`,
        error
      )
      return []
    } finally {
      // 移除加载标记
      state.statusLoading.delete(cacheKey)
    }
  }

  // 为事项获取状态列表
  const getIssueStatusList = async (issue) => {
    const projectId = state.targetProjectId || state.currentProjectId
    const trackerId = issue.tracker_id

    if (!projectId || !trackerId) {
      return []
    }

    return await getStatusList(projectId, trackerId)
  }

  // 为所有事项加载状态列表
  const loadStatusListsForIssues = async () => {
    const promises = state.issueList.map(async (issue) => {
      try {
        issue.statusList = await getIssueStatusList(issue)
      } catch (error) {
        console.error(`为事项 ${issue.id} 加载状态列表失败:`, error)
        issue.statusList = []
      }
    })

    await Promise.all(promises)
  }

  // 为单个事项重新加载状态列表（当tracker_id变化时使用）
  const reloadIssueStatusList = async (issue) => {
    try {
      issue.statusList = await getIssueStatusList(issue)
    } catch (error) {
      console.error(`为事项 ${issue.id} 重新加载状态列表失败:`, error)
      issue.statusList = []
    }
  }

  // 获取模块列表
  const getCategories = async (projectId) => {
    try {
      const res = await getCategoryList({
        filter: { project_id: projectId },
        op: {},
        sort: 'position',
        order: 'asc',
        limit: 999,
      })
      state.categoryList = res.data.data || []
    } catch (error) {
      console.error('获取模块列表失败:', error)
      state.categoryList = []
    }
  }

  // 获取父事项列表
  const getParentIssues = async (projectId) => {
    try {
      const res = await getIssueList({
        filter: {
          project_id: projectId,
          parent_id: 'IS NULL',
        },
        op: {
          parent_id: 'IS NULL',
        },
        limit: 999,
      })
      state.parentIssueList = res.data.data || []
    } catch (error) {
      console.error('获取父事项列表失败:', error)
      state.parentIssueList = []
    }
  }

  // 获取项目成员
  const getMembers = async (projectId) => {
    state.memberListLoading = true
    try {
      const res = await getMemberList({
        limit: 100,
        filter: {
          project_id: projectId,
          user: { type: 'User', status: 1 },
        },
        op: { user: { status: '=' } },
      })
      state.memberList = res.data.data || []
      state.assignOptions = res.data.data || [] // 同时设置关注人选项
    } catch (error) {
      console.error('获取成员列表失败:', error)
      state.memberList = []
      state.assignOptions = []
    } finally {
      state.memberListLoading = false
    }
  }

  // 获取事项分类列表
  const getIssueClassList = async (projectId) => {
    try {
      const { data } = await getIssueClassListData({
        pageNo: 1,
        limit: 9999,
        filter: {
          project_id: projectId,
        },
        order: 'ASC',
      })

      if (data.data && data.data.length > 0 && data.data[0].children) {
        state.issueClassList = data.data[0].children
        // 添加"无"选项
        if (state.issueClassList.length > 0) {
          state.issueClassList[0].id = 0
          state.issueClassList[0].name = '无'
        }
      } else {
        state.issueClassList = []
      }
    } catch (error) {
      console.error('获取事项分类失败:', error)
      state.issueClassList = []
    }
  }

  // 获取事项详情列表
  const getIssueDetails = async () => {
    if (issueIds.value.length === 0) {
      $baseMessage('未找到要复制的事项', 'warning')
      return
    }

    state.loading = true
    try {
      const promises = issueIds.value.map((id) => getIssueInfo({ id }))
      const results = await Promise.all(promises)

      state.issueList = results.map((res, index) => {
        const issue = res.data

        const issueData = reactive({
          ...issue,
          expanded: false,
          class_id: issue.class_id || null, // 初始化所属分类
          watchers: issue.watchers || [], // 初始化关注人
          assigned_to_ids: issue.assigned_to_id ? [issue.assigned_to_id] : [], // 初始化多选处理人
          assigned: issue.assigned ? issue.assigned : [], // IssueAssign组件使用的字段
          // 添加图标和层级相关属性
          icon:
            issue.parent_id && issue.parent_id > 0
              ? IssueIconChild[issue.tracker_id] ?? IssueIconChild[11]
              : IssueIconParent[issue.tracker_id] ?? IssueIconParent[11],
          is_top: !issue.parent_id || issue.parent_id <= 0,
          top_tracker_id: issue.parent_id ? issue.tracker_id : issue.tracker_id,
          author_id: issue.author_id, // 默认保留原创建人
          authorOption: 'original', // 创建人选择选项：'original' 或 'current'
          start_date: issue.start_date,
          due_date: issue.due_date,
          // 双击编辑相关状态
          isEditingSubject: false, // 是否正在编辑标题
          editingSubject: issue.subject, // 编辑中的标题内容
          originalSubject: issue.subject, // 原始标题，用于取消编辑时恢复
          isChildIssue: false, // 标记是否为子事项
          issue_type: issue.issue_type, //事项类型详情
          // 新增：状态列表
          statusList: [], // 当前事项可用的状态列表
        })

        // 保存原始创建人ID用于恢复
        issueData.originalAuthorId = issue.author_id

        return issueData
      })

      // 为每个事项加载状态列表
      await loadStatusListsForIssues()

      // 检查是否只有一个事项且有子事项
      if (issueIds.value.length === 1 && results.length > 0) {
        const firstIssue = results[0].data
        if (
          firstIssue.children &&
          Array.isArray(firstIssue.children) &&
          firstIssue.children.length > 0
        ) {
          state.hasChildIssues = true
          state.originalIssueChildren = firstIssue.children
        } else {
          state.hasChildIssues = false
          state.originalIssueChildren = []
        }
      } else {
        state.hasChildIssues = false
        state.originalIssueChildren = []
      }

      // 保存初始字段状态用于恢复
      saveInitialFieldStates()

      // 初始化 SelectParent 组件
      await initSelectParentComponents()
    } catch (error) {
      console.error('获取事项详情失败:', error)
      $baseMessage('获取事项详情失败', 'error', 'vab-hey-message-error')
    } finally {
      state.loading = false
    }
  }

  // 获取项目tracker列表
  const getTrackerList = async (projectId) => {
    if (!projectId) return

    state.trackerListLoading = true
    try {
      const res = await getIssueTypeApi({
        filter: { 'projects_trackers.project_id': projectId },
      })
      state.trackerList = res.data || []
      state.trackerIdToIdentifierMap = res.data.reduce((map, item) => {
        map[item.id] = item.identifier
        return map
      }, {})
    } catch (error) {
      console.error('获取事项类别列表失败:', error)
      state.trackerList = []
    } finally {
      state.trackerListLoading = false
    }
  }

  // 保存初始字段状态
  const saveInitialFieldStates = () => {
    state.issueList.forEach((issue) => {
      state.initialFieldStates.set(issue.id, {
        tracker_id: issue.tracker_id,
        status_id: issue.status_id,
        fixed_version_id: issue.fixed_version_id,
        category_id: issue.category_id,
        class_id: issue.class_id,
        isChildIssue: issue.isChildIssue,
        parent_id: issue.parent_id,
      })
    })
  }

  // 智能匹配字段值
  const smartMatchField = (currentValue, optionsList, valueKey = 'id') => {
    if (!currentValue || !optionsList || optionsList.length === 0) {
      return optionsList && optionsList.length > 0
        ? optionsList[0][valueKey]
        : null
    }

    // 检查当前值是否在新的选项列表中存在
    const exists = optionsList.some(
      (option) => option[valueKey] === currentValue
    )

    if (exists) {
      return currentValue // 保留当前值
    } else {
      return optionsList.length > 0 ? optionsList[0][valueKey] : null // 选择第一个选项
    }
  }

  // 处理字段联动逻辑
  const handleFieldLinkage = async (projectId) => {
    const isBackToOriginal = projectId === state.currentProjectId

    // 使用 Promise.all 并行处理所有事项
    await Promise.all(
      state.issueList.map(async (issue) => {
        if (isBackToOriginal) {
          // 恢复到原项目：恢复所有字段的初始值
          const initialState = state.initialFieldStates.get(issue.id)
          if (initialState) {
            issue.tracker_id = initialState.tracker_id
            issue.status_id = initialState.status_id
            issue.class_id = initialState.class_id
            issue.fixed_version_id = initialState.fixed_version_id
            issue.category_id = initialState.category_id
            issue.parent_id = initialState.parent_id
            issue.isChildIssue = initialState.isChildIssue

            // 重新加载原项目的状态列表
            await reloadIssueStatusList(issue)
          }
        } else {
          // 切换到其他项目：执行字段联动逻辑

          // 1. 需要清空的字段
          issue.fixed_version_id = null // 版本字段清空
          issue.category_id = null // 所属模块字段清空
          issue.class_id = null // 所属分类字段清空
          issue.parent_id = null // 所属父事项字段清空

          let tmp = issue.isChildIssue
          issue.isChildIssue = true
          await nextTick() // 父事项筛选组件清空
          issue.isChildIssue = tmp

          // 2. 需要智能匹配的字段
          // 事项类型智能匹配
          const oldTrackerId = issue.tracker_id
          issue.tracker_id = smartMatchField(
            issue.tracker_id,
            state.trackerList,
            'id'
          )

          // 如果事项类型发生变化，需要重新加载状态列表
          if (oldTrackerId !== issue.tracker_id) {
            await reloadIssueStatusList(issue)
          }

          // 状态智能匹配（使用事项自己的状态列表）
          issue.status_id = smartMatchField(
            issue.status_id,
            issue.statusList || [],
            'value'
          )
        }
      })
    )
  }

  // 处理项目变更
  const handleProjectChange = async (projectId) => {
    if (!projectId) return

    state.projectListLoading = true
    try {
      // // 如果当前已勾选复制子事项，先取消勾选并移除子事项
      // if (state.copyChildIssues) {
      //   state.copyChildIssues = false
      //   state.issueList = state.issueList.filter((issue) => !issue.isChildIssue)
      // }

      await Promise.all([
        getVersions(projectId),
        // getStatuses(projectId),
        getCategories(projectId),
        getMembers(projectId),
        getIssueClassList(projectId), // 新增：获取分类列表
        getTrackerList(projectId), // 新增：获取tracker列表
      ])

      // 执行字段联动逻辑
      await handleFieldLinkage(projectId)

      // 重新初始化 SelectParent 组件（项目变更后需要重新加载父级事项）
      await initSelectParentComponents()
    } catch (error) {
      console.error('处理项目变更失败:', error)
      $baseMessage('处理项目变更失败', 'error', 'vab-hey-message-error')
    } finally {
      state.projectListLoading = false
    }
  }

  // 处理取消
  const handleCancel = () => {
    router.back()
  }

  // 处理提交
  const handleSubmit = async () => {
    if (!state.targetProjectId) {
      $baseMessage('请选择目标项目', 'warning')
      return
    }

    state.submitLoading = true
    try {
      // 分离父事项和子事项
      const parentIssues = state.issueList.filter(
        (issue) => !issue.isChildIssue
      )
      const childIssues = state.issueList.filter((issue) => issue.isChildIssue)

      // 新增：检测原始事项中的父子关系
      const detectOriginalParentChildRelations = () => {
        const relations = []
        const issueMap = new Map()

        // 创建事项ID映射
        state.issueList.forEach((issue) => {
          issueMap.set(issue.id, issue)
        })

        // 检测原始父子关系
        state.issueList.forEach((issue) => {
          // 如果事项有parent_id且该parent_id在当前事项列表中
          if (issue.parent_id && issueMap.has(issue.parent_id)) {
            let relation = relations.find(
              (r) => r.parentIssueId === issue.parent_id
            )
            if (!relation) {
              relation = {
                parentIssueId: issue.parent_id,
                childIssueIds: [],
              }
              relations.push(relation)
            }
            relation.childIssueIds.push(issue.id)
          }
        })

        return relations
      }

      // 检测原始父子关系
      const originalRelations = detectOriginalParentChildRelations()

      // 处理事项数据
      const processedIssues = state.issueList.map((issue) => {
        const issueData = {
          id: issue.id,
          subject: issue.subject,
          tracker_id: issue.tracker_id,
          status_id: issue.status_id,
          priority_id: issue.priority_id,
          category_id: issue.category_id,
          fixed_version_id: issue.fixed_version_id,
          assigned_to_id:
            issue.assigned_to_ids && issue.assigned_to_ids.length > 0
              ? issue.assigned_to_ids[0]
              : null, // 取第一个处理人
          assigned_to_ids: issue.assigned_to_ids || [], // 多选处理人
          start_date: issue.start_date,
          due_date: issue.due_date,
          class_id: issue.class_id, // 新增：所属分类
          watchers: issue.watchers, // 新增：关注人
          custom_fields: issue.custom_fields,
          author_id: issue.author_id, // 修复：添加创建人ID，使authorOption生效
        }

        // 检查事项是否在原始父子关系中
        const isChildInOriginalRelation = originalRelations.some((relation) =>
          relation.childIssueIds.includes(issue.id)
        )

        // 对于子事项，设置特殊的父事项处理
        if (issue.isChildIssue && issue.needsParentRelation) {
          // 通过勾选复制子事项添加的子事项
          issueData.parent_id = null
          issueData.originalParentId = issue.originalParentId
          issueData.needsParentRelation = true
          issueData.isChildIssue = true
        } else if (isChildInOriginalRelation) {
          // 原始就有父子关系的子事项
          issueData.parent_id = null // 清空，由后端重新建立关系
          issueData.originalParentId = issue.parent_id
          issueData.needsParentRelation = true
          issueData.isChildIssue = true
        } else {
          issueData.parent_id = issue.parent_id || null
        }

        return issueData
      })

      // 生成父子关系映射信息
      let parentChildMapping = null

      if (childIssues.length > 0) {
        // 单个事项复制+勾选子事项的场景
        parentChildMapping = {
          parentIssueId: parentIssues[0]?.id,
          childIssueIds: childIssues.map((child) => child.id),
        }
      } else if (originalRelations.length > 0) {
        // 批量选择包含原始父子关系的场景
        if (originalRelations.length === 1) {
          // 单个父子关系组
          parentChildMapping = originalRelations[0]
        } else {
          // 多个父子关系组
          parentChildMapping = {
            multipleRelations: true,
            relations: originalRelations,
          }
        }
      }

      const params = {
        target_project_id: state.targetProjectId,
        ...state.copyOptions,
        // 如果有子事项或原始父子关系，设置copy_children为true以触发后端的父子关系处理
        copy_children:
          childIssues.length > 0 ||
          originalRelations.length > 0 ||
          state.copyOptions.copy_children,
        issues: processedIssues,
        // 添加父子关系映射信息，帮助后端处理
        parentChildMapping: parentChildMapping,
      }

      const result = await batchCopyIssue(params)

      // 构建成功消息
      let successMessage = '批量复制成功'
      if (childIssues.length > 0) {
        successMessage += `，包含 ${parentIssues.length} 个主事项和 ${childIssues.length} 个子事项`
      } else if (originalRelations.length > 0) {
        const totalChildren = originalRelations.reduce(
          (sum, relation) => sum + relation.childIssueIds.length,
          0
        )
        successMessage += `，包含 ${originalRelations.length} 个父子关系组，共 ${totalChildren} 个子事项`
      }

      $baseMessage(successMessage, 'success', 'vab-hey-message-success')

      // 调试信息（开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.log('复制事项详情:', {
          parentIssues: parentIssues.length,
          childIssues: childIssues.length,
          originalRelations: originalRelations,
          totalIssues: processedIssues.length,
          parentChildMapping: parentChildMapping,
          authorOptions: processedIssues.map((issue) => ({
            id: issue.id,
            authorId: issue.author_id,
            isChildIssue: issue.isChildIssue,
            needsParentRelation: issue.needsParentRelation,
            originalParentId: issue.originalParentId,
          })),
          result,
        })
      }

      // 跳转到目标项目的事项列表
      router.push({
        path: '/project/issue',
        query: { project_id: state.targetProjectId },
      })
    } catch (error) {
      console.error('复制失败:', error)
      $baseMessage('复制失败', 'error', 'vab-hey-message-error')
    } finally {
      state.submitLoading = false
    }
  }

  // 处理创建人选择变更
  const handleAuthorOptionChange = (issue, option) => {
    issue.authorOption = option
    if (option === 'current') {
      issue.author_id = state.currentUser.third_id
    } else {
      // 恢复原始创建人ID（需要从原始数据中获取）
      const originalIssue = state.issueList.find((item) => item.id === issue.id)
      issue.author_id = originalIssue?.originalAuthorId || issue.author_id
    }
  }

  // 双击编辑相关方法
  // 存储输入框引用
  const subjectInputRefs = ref([])

  // 设置输入框引用
  const setSubjectInputRef = (el, index) => {
    if (el) {
      subjectInputRefs.value[index] = el
    }
  }

  // 开始编辑标题
  const startEditSubject = async (issue, index) => {
    issue.isEditingSubject = true
    issue.editingSubject = issue.subject
    // 在下一个tick后获得焦点
    await nextTick()
    const inputEl = subjectInputRefs.value[index]
    if (inputEl) {
      inputEl.focus()
    }
  }

  // 保存标题编辑
  const saveSubject = (issue) => {
    if (issue.editingSubject.trim()) {
      issue.subject = issue.editingSubject.trim()
      issue.originalSubject = issue.subject // 更新原始值
    }
    issue.isEditingSubject = false
  }

  // 取消编辑标题
  const cancelEditSubject = (issue) => {
    issue.editingSubject = issue.originalSubject // 恢复原始值
    issue.isEditingSubject = false
  }

  // 处理失焦事件（暂存但不退出编辑模式）
  const handleSubjectBlur = (issue) => {
    // 失焦时暂存编辑内容，但保持编辑状态
    if (issue.editingSubject.trim()) {
      issue.subject = issue.editingSubject.trim()
    }
    // 注意：这里不设置 isEditingSubject = false，保持编辑状态
  }

  // 初始化
  const initPage = async () => {
    await Promise.all([getProjects(), getPriorities(), getIssueDetails()])

    if (state.targetProjectId) {
      await handleProjectChange(state.targetProjectId)
    }
  }

  onMounted(() => {
    initPage()
  })

  // 处理头像加载错误
  const handleAvatarError = (event) => {
    event.target.src = require('@/assets/cropper_images/user.gif')
  }

  // 计算属性：处理事项类别选项列表
  const trackerOptions = computed(() => {
    if (!state.trackerList || state.trackerList.length === 0) {
      return []
    }

    return state.trackerList.map((item) => ({
      id: item.id,
      name: item.name,
      label: item.name,
      value: item.id,
    }))
  })

  // 计算属性：根据tracker_id获取tracker名称
  const getTrackerName = (trackerId) => {
    if (!state.trackerList || state.trackerList.length === 0) {
      return '未知类别'
    }
    const tracker = state.trackerList.find((item) => item.id === trackerId)
    return tracker?.name || '未知类别'
  }

  // 获取状态颜色类
  const getStatusClass = (statusId) => {
    return issueStatusClass[statusId] || ''
  }

  // 处理处理人变更
  const handleAssignedChange = (issue) => {
    // IssueAssign组件已经自动更新了issue.assigned字段
    // 同步更新assigned_to_ids字段以保持数据一致性
    issue.assigned_to_ids = issue.assigned || []
    console.log('处理人已更新:', issue.assigned)
  }

  // 处理关注人变更
  const handleWatchersChange = (issue) => {
    // IssueAssign组件已经自动更新了issue.watchers字段
    console.log('关注人已更新:', issue.watchers)
  }

  // 处理所属分类变更
  const handleClassChange = (issue) => {
    // el-cascader返回的是数组，我们需要取最后一个值作为class_id
    if (Array.isArray(issue.class_id) && issue.class_id.length > 0) {
      issue.class_id = issue.class_id[issue.class_id.length - 1]
    }
    console.log('所属分类已更新:', issue.class_id)
  }

  // 处理复制子事项复选框变化
  const handleCopyChildIssuesChange = async (checked) => {
    if (checked) {
      // 勾选时：将子事项添加到卡片列表
      if (state.originalIssueChildren.length > 0) {
        state.addingChildIssues = true

        try {
          const parentIssue = state.issueList[0] // 主事项

          // 模拟数据处理时间，让用户看到加载状态
          await new Promise((resolve) => setTimeout(resolve, 300))

          const childIssues = state.originalIssueChildren.map((child) => {
            return reactive({
              ...child,
              // 继承父事项的一些基本设置
              class_id: parentIssue.class_id,
              watchers: [], // 子事项初始无关注人
              assigned_to_ids: [], // 子事项初始无处理人
              assigned: [], // IssueAssign组件使用的字段
              // 添加图标和层级相关属性
              icon: IssueIconChild[child.tracker_id] ?? IssueIconChild[11],
              is_top: false,
              top_tracker_id: parentIssue.tracker_id,
              author_id: parentIssue.author_id, // 继承父事项的创建人设置
              authorOption: parentIssue.authorOption, // 继承父事项的创建人选项
              start_date: child.start_date,
              due_date: child.due_date,
              // 双击编辑相关状态
              isEditingSubject: false,
              editingSubject: child.subject,
              originalSubject: child.subject,
              isChildIssue: true, // 标记为子事项
              originalAuthorId: child.author_id,
              // 修复父子关系
              originalParentId: child.parent_id, // 保存原始父事项ID
              parent_id: parentIssue.id, // 设置为当前父事项ID，确保复制时关联正确
              needsParentRelation: true, // 标记需要在复制后建立父子关系
            })
          })

          // 将子事项添加到列表末尾
          state.issueList.push(...childIssues)

          // 重新初始化 SelectParent 组件
          await new Promise((resolve) => setTimeout(resolve, 200))
          await initSelectParentComponents()

          $baseMessage(
            `已添加 ${childIssues.length} 个子事项`,
            'success',
            'vab-hey-message-success'
          )
        } catch (error) {
          console.error('添加子事项失败:', error)
          $baseMessage('添加子事项失败', 'error', 'vab-hey-message-error')
          // 发生错误时取消勾选
          state.copyChildIssues = false
        } finally {
          state.addingChildIssues = false
        }
      }
    } else {
      // 取消勾选时：从列表中移除所有子事项
      const childIssueCount = state.issueList.filter(
        (issue) => issue.isChildIssue
      ).length
      state.issueList = state.issueList.filter((issue) => !issue.isChildIssue)

      if (childIssueCount > 0) {
        $baseMessage(
          `已移除 ${childIssueCount} 个子事项`,
          'info',
          'vab-hey-message-info'
        )
      }
    }
  }
</script>

<style lang="scss">
  .issue-copy-custom-scope {
    --el-text-color-regular: #333333;
  }

  .issue-priority-option-list-style {
    .el-select-dropdown__item {
      display: flex;
    }
    .flex-algin-center {
      margin: auto;
    }
  }

  .work-status-tag {
    .el-tag__content {
      font-weight: bold !important;
      transform: scale(1.08);
    }
  }

  .flex-algin-center {
    display: flex;
    align-items: center;
  }
</style>

<style lang="scss" scoped>
  .issue-copy-container {
    padding: 0px !important;
    .issue-copy-main-content {
      margin-bottom: 0;
      border: none;

      .issue-copy-page-header {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 24px;
        padding: 0 16px 16px 16px;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 24px;
        margin-left: -16px;
        margin-right: -16px;

        .issue-copy-page-title {
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          color: #333333;
        }

        .issue-copy-project-selector {
          .issue-copy-project-form-item {
            margin-bottom: 0;

            :deep(.el-form-item__label) {
              font-weight: 500;
              color: #606266;
            }
          }
        }
      }

      .issue-copy-cards-container {
        .issue-copy-card {
          margin-bottom: 24px;
          border: 1px solid #ebeef5;
          border-radius: 8px;
          overflow: hidden;

          .issue-copy-card-header {
            padding: 16px;
            // background-color: #f8f9fa;
            // border-bottom: 1px solid #ebeef5;

            .issue-copy-card-title {
              display: flex;
              align-items: center;

              .issue-copy-issue-icon {
                margin-right: 8px;
              }

              .issue-copy-issue-id {
                margin-right: 8px;
                // color: #409eff;
                color: #333333;
                font-weight: 500;
              }

              .issue-copy-issue-title {
                color: #333333;
                font-weight: 500;
                cursor: unset;
                padding: 4px 8px;
                border-radius: 4px;
                transition: all 0.2s ease;

                &:hover {
                  background-color: #f5f7fa;
                  color: #409eff;
                }
              }

              .issue-copy-subject-input {
                flex: 1;
                max-width: 400px;
                margin-left: 4px;

                :deep(.el-input__wrapper) {
                  border: 1px solid #409eff;
                  border-radius: 4px;
                  box-shadow: 0 0 4px rgba(64, 158, 255, 0.3);
                }

                :deep(.el-input__inner) {
                  font-weight: 500;
                  color: #333333;
                }
              }
            }
          }

          .issue-copy-card-content {
            // padding: 20px;

            .issue-copy-card-content-item-1 {
              padding: 16px;

              // 设置固定的内容宽度，右侧自动留白
              max-width: 1300px; // 内容区域最大宽度

              // // 在更宽屏幕上增加右侧空白
              // @media (min-width: 1400px) {
              //   max-width: 1100px;
              // }

              // @media (min-width: 1600px) {
              //   max-width: 1000px;
              // }
            }

            .issue-copy-issue-form {
              .issue-copy-expandable-section {
                // background: rgba(51, 51, 51, 0.05);
                padding: 12px 16px;
                position: relative;

                // 设置固定的内容宽度，右侧自动留白
                max-width: 1300px; // 内容区域最大宽度

                // // 在更宽屏幕上增加右侧空白
                // @media (min-width: 1400px) {
                //   max-width: 1100px;
                // }

                // @media (min-width: 1600px) {
                //   max-width: 1000px;
                // }

                .issue-copy-toggle-button {
                  padding: 0;
                  color: #666;
                  font-size: 14px;

                  .el-icon {
                    margin-right: 4px;
                  }

                  &.issue-copy-toggle-button-collapsed {
                    position: absolute;
                    bottom: 24px;
                    left: 20px;
                  }
                }

                .issue-copy-expanded-content {
                  padding: 12px 0 50px 0;
                }
              }
            }
          }
        }
      }

      .issue-copy-options-panel {
        margin: 16px 0;
        padding: 20px;
        // background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #ebeef5;

        .issue-copy-panel-title {
          margin-bottom: 16px;
          font-size: 16px;
          font-weight: 600;
          color: #333333;
        }

        .issue-copy-panel-content {
          .issue-copy-switch-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .issue-copy-switch-label {
              margin-left: 12px;
              font-size: 14px;
              color: #333333;
              font-weight: 400;
            }

            &:last-child {
              margin-bottom: 0;
            }
          }

          // 同步选项样式
          .issue-copy-sync-options {
            margin-top: 16px;
            padding: 16px;
            border-radius: 4px;
            border: 1px solid #e4e7ed;

            .issue-copy-sync-direction {
              display: flex;
              align-items: center;
              margin-bottom: 16px;

              .issue-copy-sync-label {
                margin-right: 12px;
                font-size: 14px;
                color: #606266;
                font-weight: 500;
                min-width: 80px;
              }

              :deep(.el-radio-group) {
                .el-radio-button__inner {
                  padding: 5px 15px;
                  font-size: 13px;
                }
              }
            }

            .issue-copy-sync-fields {
              .issue-copy-sync-label {
                display: block;
                margin-bottom: 12px;
                font-size: 14px;
                color: #606266;
                font-weight: 500;
              }

              :deep(.el-checkbox-group) {
                display: grid;
                grid-template-columns: repeat(5, 1fr);
                gap: 12px;
                max-width: 700px;

                @media (max-width: 1200px) {
                  grid-template-columns: repeat(3, 1fr);
                }

                @media (max-width: 768px) {
                  grid-template-columns: repeat(2, 1fr);
                }

                .el-checkbox {
                  margin-right: 0;

                  .el-checkbox__label {
                    font-size: 13px;
                    color: #606266;
                  }
                }
              }
            }
          }
        }
      }

      .issue-copy-checkbox-simple {
        // margin: 20px 0;
        margin-top: -4px;

        :deep(.el-checkbox) {
          font-size: 14px;
          color: #333333;

          .el-checkbox__label {
            font-weight: 500;
          }

          &.is-disabled {
            .el-checkbox__label {
              color: #c0c4cc;
            }
          }
        }

        .issue-copy-loading-text {
          margin-left: 12px;
          font-size: 13px;
          color: #409eff;
          font-weight: 500;
        }
      }

      .issue-copy-action-buttons {
        display: flex;
        justify-content: flex-start;
        gap: 16px;
        padding-top: 24px;
        border-top: 1px solid #ebeef5;
      }
    }
  }

  // 下拉选项样式

  .issue-copy-version-status {
    &.issue-copy-version-closed {
      color: #f56c6c;
    }

    &.issue-copy-version-locked {
      color: #e6a23c;
    }
  }

  // 全局选择框最大宽度限制
  :deep(.el-select) {
    max-width: 210px;
  }

  :deep(.el-cascader) {
    max-width: 210px;
    width: 100%;
  }

  :deep(.el-date-picker) {
    max-width: 210px;
    width: 100%;
  }

  :deep(.el-date-editor) {
    --el-date-editor-width: 210px;
    max-width: 210px;
  }

  :deep(.el-select__selection.is-near) {
    height: 26px; // 处理人标签太高顶高了el-select输入框
  }

  // :deep(.el-card__body) {
  //   padding: 0px !important;
  // }

  // 表单项间距调整
  :deep(.el-form-item) {
    margin-bottom: 2px;
    .el-form-item__content {
      min-width: 120px;
    }
    .el-form-item__label {
      justify-content: flex-start;
      color: #999 !important;
    }
  }

  // 基础字段网格布局（每行三个字段）
  .issue-copy-form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  // 创建人选择单独成行
  .issue-copy-author-selection {
    grid-column: 1 / -1;

    .issue-copy-author-button-group {
      display: flex;
      // gap: 16px;
      margin-left: -8px;

      .el-button {
        position: relative;
        padding: 8px 16px 8px 32px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        background-color: #ffffff;
        color: #333333;
        font-size: 14px;
        font-weight: normal;
        transition: all 0.3s;
        border: none;

        // 模拟 radio 圆圈
        &::before {
          content: '';
          position: absolute;
          left: 8px;
          top: 50%;
          transform: translateY(-50%);
          width: 14px;
          height: 14px;
          border: 1px solid #dcdfe6;
          border-radius: 50%;
          background-color: #ffffff;
          transition: all 0.3s;
        }

        // 模拟 radio 选中状态的内圆点
        &::after {
          content: '';
          position: absolute;
          left: 13px;
          top: 50%;
          transform: translateY(-50%);
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: #ffffff;
          opacity: 0;
          transition: all 0.3s;
        }

        // 悬停效果
        &:hover {
          border-color: #409eff;
          // color: #409eff;

          &::before {
            // border-color: #409eff;
          }
        }

        // 选中状态样式
        &.el-button--primary {
          background-color: #ffffff;
          border-color: #409eff;
          // color: #409eff;

          &::before {
            border-color: #ffffff;
            background-color: #3977f3;
          }

          &::after {
            opacity: 1;
          }
        }

        // 移除默认按钮样式
        &.is-plain {
          background-color: #ffffff;
          border-color: #dcdfe6;
          color: #606266;

          &::before {
            border-color: #dcdfe6;
          }

          &::after {
            opacity: 0;
          }
        }

        // 禁用状态
        &:disabled {
          color: #c0c4cc;
          border-color: #ebeef5;
          background-color: #f5f7fa;
          cursor: not-allowed;

          &::before {
            border-color: #ebeef5;
            background-color: #f5f7fa;
          }

          &::after {
            background-color: #c0c4cc;
          }
        }
      }
    }
  }

  // 扩展字段网格布局
  .issue-copy-expanded-form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    @media (max-width: 1200px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    // // 关注人字段占满整行
    // .issue-copy-full-width {
    //   grid-column: 1 / -1;
    // }
  }
</style>
