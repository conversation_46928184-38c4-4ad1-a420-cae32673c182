<template>
  <div>
    <el-row
      class="new-child-row"
      :style="{ display: state.isAdd ? 'flex' : 'none' }"
    >
      <el-col :span="16">
        <el-select
          v-loading="state.selectListLoading"
          v-model="state.form.issue_to_id"
          style="width: 100%; border: none; box-shadow: none"
          :style="{ border: 'none', 'box-shadow': 'none' }"
          filterable
          clearable
          ref="elInputRef"
          :filter-method="handlefilterMethod"
        >
          <el-option
            style="max-width: 500px"
            v-for="pItem in state.issueList"
            :key="pItem.id"
            :value="pItem.id"
            :label="pItem.subject || ''"
          >
            #{{ pItem.id }} {{ pItem.subject }}
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="8">
        <div class="child-col-right">
          <el-button @click="doAdd(false)">取消</el-button>
          <el-button @click="save" type="primary">确定</el-button>
        </div>
      </el-col>
    </el-row>
    <el-row v-for="(citem, ckey) in list" :key="ckey" class="child-issue">
      <el-col
        id="custom-el-col-1"
        :span="11"
        style="display: flex; align-items: center; color: #333"
      >
        <div v-for="(item, index) in citem.level" :key="index">
          <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
        </div>
        <div style="height: 20px; margin-right: 8px">
          <CommonIcon
            :issue-type="getChildIssueImg(info.tracker_id)"
            :size="22"
          />
        </div>
        <!-- <a class="custom-link" @click="toIssuePage(citem.id, citem.project_id)">
          {{ citem.subject }}
        </a> -->
        <a
          class="custom-link-issue-relation-list"
          @click="toIssuePage(citem.id, citem.project_id)"
        >
          <showTip
            :content="citem.subject"
            :max-width="state.relationIssueMaxWidth"
            :as-table-col="false"
          />
        </a>
      </el-col>
      <el-col :span="13">
        <div class="child-col-right">
          <div style="display: contents; width: 115px; background: white">
            <div style="display: contents">
              <span class="text-color-secondary inline-dot">&nbsp;•&nbsp;</span>

              <el-link
                :href="`/#/project/issue?project_id=${citem.project_id}`"
                :underline="false"
              >
                <span class="text-color-secondary" style="white-space: pre">
                  {{ citem?.project_text?.name }}
                </span>
              </el-link>
            </div>
            <div>
              <el-tag
                type="danger"
                size="default"
                effect="dark"
                v-if="
                  citem.issue_status?.status_category !== 'completed' && // 挂起状态
                  citem.due_date &&
                  new Date(citem.due_date).setHours(0, 0, 0, 0) < state.nowDate
                "
                :style="{ 'border-radius': '4px', 'font-size': '14px' }"
              >
                {{
                  '已超期' +
                  Math.abs(
                    Math.floor(
                      (new Date(citem.due_date).setHours(0, 0, 0, 0) -
                        state.nowDate) /
                        (24 * 60 * 60 * 1000)
                    )
                  ) +
                  '天'
                }}
              </el-tag>
              <el-tag
                type="info"
                v-else-if="
                  citem.due_date &&
                  new Date(citem.due_date).setHours(0, 0, 0, 0) >= state.nowDate
                "
                :style="{
                  'font-size': '14px',
                  border: 'none',
                  background: 'none',
                }"
              >
                {{
                  '剩余' +
                  Math.abs(
                    Math.floor(
                      (new Date(citem.due_date).setHours(0, 0, 0, 0) -
                        state.nowDate) /
                        (24 * 60 * 60 * 1000)
                    )
                  ) +
                  '天'
                }}
              </el-tag>
              <span v-else style="width: 100%">&nbsp;&nbsp;</span>
            </div>
          </div>

          <div
            style="
              width: 85px;
              min-width: 85px;
              padding: 0px 2px;
              color: rgb(102, 102, 102);
            "
          >
            {{ citem.assigned_text?.name ?? '无处理人' }}
          </div>
          <IssuePriority
            :key="'issue-priority-' + citem.id"
            v-if="state.enumerationList.length > 0"
            v-model:value="citem.priority_id"
            :option="state.enumerationList"
            @change="handleSave(citem, 'priority_id')"
          />
          &nbsp;
          <IssueStatus
            style="width: 80px"
            :key="'issue-status-' + citem.id"
            v-if="!state.listLoading"
            v-model:value="citem.status_id"
            :row-data="citem"
            :option="citem.status_list"
            @change="handleSave(citem, 'status_id')"
          />
          &nbsp;
          <el-tooltip content="解除关联" :hide-after="50" placement="top-start">
            <a @click="unLinkChild(citem)" style="color: #999; cursor: pointer">
              <CommonIcon :type="'release'" />
            </a>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <edit-dialog ref="editRef" />
  </div>
</template>

<script setup>
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import IssueStatus from '@/components/IssueStatus.vue'
  import IssuePriority from '@/components/IssuePriority.vue'
  import IssueAssign from '@/components/IssueAssign.vue'
  import EditDialog from './IssueEditDialog.vue'
  import showTip from './overflowTooltip.vue'
  import { charAt } from '@/utils/common'

  import {
    doEdit,
    getEnumerationList,
    getAllList as getIssueList,
    addIssueRelation,
    delIssueRelation,
  } from '@/api/projectIssue'

  import { getImgByIssueType, IssueIconRelation } from '@/utils/index'

  const emit = defineEmits(['fetch-overview'])
  const props = defineProps({
    info: {
      type: Object,
      default: () => {
        return {}
      },
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    parentId: {
      type: Number,
      default: null,
    },
    openType: {
      type: String,
      default: 'page',
    },
    fixedVersionId: {
      type: Number,
      default: null,
    },
  })

  const { info, list } = toRefs(props)

  watch(info, (v) => {
    state.form.parent_id = v.id
    state.form.tracker_id = v.tracker_id
    state.form.project_id = v.project_id
  })

  const childIssueIcon = {
    0: require('@/assets/icon_images/demand.png'),
    11: require('@/assets/icon_images/childissue-requirement.png'),
    12: require('@/assets/icon_images/childissue-task.png'),
    20: require('@/assets/icon_images/childissue-bug.png'),
  }

  const getImgByIssueTypeAndExistParent = (row) => {
    if (row.parent_id && row.parent_id > 0) {
      let parent = info.value
      if (parent) {
        return childIssueIcon[parent.tracker_id] || childIssueIcon[0]
      } else {
        return getImgByIssueType(row.tracker_id)
      }
    } else {
      return getImgByIssueType(row.tracker_id)
    }
  }

  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')
  const $pub = inject('$pub')

  const getChildIssueImg = (id) => {
    return IssueIconRelation[id] || IssueIconRelation[0]
  }

  const dialogHeight = computed(() => {
    const h = document.body.clientHeight - 40
    return h
  })
  const state = reactive({
    relationIssueMaxWidth: '90%',
    relationIssueId: null,
    issueList: [],
    listLoading: false,
    selectListLoading: false,
    assignList: [],
    nowDate: new Date().setHours(0, 0, 0, 0),
    isAdd: false,
    form: {
      id: null,
      issue_to_id: null,
      relation_type: 'relates',
    },
    enumerationList: [],
    statusClassList: {
      1: '',
      2: '',
      6: '',
      7: '',
      8: 'warning',
      9: 'warning',
      10: 'warning',
      3: 'success',
      4: 'success',
      5: 'success',
    },
    priorityClassList: {
      2: require('@/assets/icon_images/low.png'),
      3: require('@/assets/icon_images/normal.png'),
      4: require('@/assets/icon_images/high.png'),
      5: require('@/assets/icon_images/urgent.png'),
      6: require('@/assets/icon_images/immediate.png'),
    },
    filterMethodTimer: null,
  })

  const editRef = ref(null)
  const elInputRef = ref(null)

  const tableColumnMaxWidth = async (row) => {
    await nextTick()
    // 获取table标题列标题的宽度
    const element = document.querySelector('.main-container-left')
    const ColWidth = element.clientWidth

    // 动态减去（项目名）的宽度
    let tmp = charAt(props.simple ? row.project_text?.name : 0) + 70
    state.relationIssueMaxWidth = ColWidth
      ? (ColWidth - tmp - 70) * (13 / 24) - 70 + 'px'
      : ''
  }
  /**
   * 问题详情页面
   */
  const toIssuePage = (id, project_id) => {
    if (props.openType == 'page') {
      let row = list.value.find((r) => {
        if (r.id == id) {
          return r
        }
      })
      editRef.value.showEdit(row)
    } else {
      window.open(
        '/#/project/detail?issue_id=' + id + '&project_id=' + project_id
      )
    }
  }

  const getStatusClass = (value) => {
    return typeof state.statusClassList[value] != 'undefined'
      ? state.statusClassList[value]
      : ''
  }
  const getPriorityClass = (value) => {
    return typeof state.priorityClassList[value] != 'undefined'
      ? state.priorityClassList[value]
      : ''
  }

  const handlefilterMethod = async (query) => {
    if (state.filterMethodTimer) {
      clearTimeout(state.filterMethodTimer)
      state.filterMethodTimer = null
    }
    if (query) {
      state.filterMethodTimer = setTimeout(async () => {
        const { data } = await getIssueList({
          filter: {
            subject: query,
            id: props.info.id,
            as_relation: true,
          },
          op: { subject: 'LIKE', id: '<>' },
          limit: 999,
        }).catch(() => {
          return { data: null }
        })
        if (typeof data != 'undefined') {
          state.issueList = data
        }
      }, 880)
    }
  }

  const handleEnumerationList = async () => {
    const { data } = await getEnumerationList()
    state.enumerationList = data
  }

  const doAdd = (value) => {
    if (value === false) {
      state.form.subject = null
      setTimeout(() => {
        state.isAdd = false
      }, 80)
      return
    } else {
      state.isAdd = true
      elInputRef.value.focus()
    }
  }

  /**
   * 解除关联
   */
  const unLinkChild = (row) => {
    $baseConfirm('是否确定取消关联', null, () => {
      delIssueRelation({ ids: row.relation_id }).then((res) => {
        $baseMessage('删除成功', 'success', 'vab-hey-message-success')
        emit('fetch-overview')
      })
    })
  }

  const save = async () => {
    // 子事项没填写标题
    if (!state.form.issue_to_id) {
      $baseMessage('请选择相关任务', 'error', 'vab-hey-message-error')
      return
    }
    console.log(props.info)
    state.form.id = props.info.id
    addIssueRelation(state.form)
      .then((res) => {
        $pub('add-child-success')
        emit('fetch-overview')
        $baseMessage(res.msg, 'success', 'vab-hey-message-success')
        doAdd(false)
        state.form.issue_to_id = null
        state.issueList = []
      })
      .catch(() => {
        state.btnLoading = false
      })
      .finally(() => {})
  }

  const onKeyupSave = (e) => {
    if (
      typeof e.key != 'undefined' &&
      e.key == 'Enter' &&
      state.form.subject &&
      state.form.subject != ''
    ) {
      save()
    }
  }

  /**
   * @description 根据字段更新
   */
  const handleSave = async (row, field, value) => {
    let newValue = row[field]
    if (value) {
      //部分下拉的逻辑可能不是model，需由该参数传进来
      newValue = value
    }
    doEdit({ id: row['id'], [field]: newValue }).then(() => {
      $baseMessage('修改成功', 'success', 'vab-hey-message-success')
      // fetchdata
      $pub('issue-edit-success', [row, field])
    })
  }

  onMounted(async () => {
    handleEnumerationList()
    await tableColumnMaxWidth()
  })
  defineExpose({
    doAdd,
  })
</script>

<style lang="scss" scoped>
  :deep() {
    .text-color-secondary {
      padding-top: 2px;
    }
    .child-issue:not(:last-child) {
      max-height: 30px;
      margin-bottom: 5px;
    }
    .new-child-input {
      .el-input__wrapper {
        box-shadow: none;
      }
    }
    .new-child-row {
      .el-input__wrapper {
        box-shadow: none;
      }
      .is-focus {
        box-shadow: none !important;
      }
      .is-focus {
        .el-input__wrapper {
          box-shadow: none !important;
        }
      }
      .el-input__wrapper:hover {
        box-shadow: none;
      }
    }
  }
  .new-child-row {
    padding: 7px 10px;
    margin-top: 8px;
    margin-bottom: 5px;
    border: 1px solid #3977f3;
    border-radius: 6px 6px 6px 6px;
  }
  .child-issue {
    align-items: center;
    padding: 7px 10px;
    // background: #f7f8fa;
    border-radius: 6px 6px 6px 6px;
  }
  .child-col-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    color: #7e899d;
  }
  .custom-link-issue-relation-list {
    overflow: hidden; /* 隐藏溢出的文本 */
    text-overflow: ellipsis; /* 显示省略号 */
    white-space: nowrap; /* 防止文本换行 */
    cursor: pointer;
    &:hover {
      color: $base-color-primary;
    }
  }
  .inline-dot {
    font-size: 14px;
  }
</style>
