<template>
  <div v-if="processedData" style="margin-right: 16px">
    <!-- 子事项模式：使用换行布局 -->
    <template v-if="props.listType === 'child'">
      <div style="margin-bottom: 10px">
        <!-- 第一行：搜索框和状态筛选器 -->
        <div
          style="
            display: flex;
            gap: 8px;
            align-items: center;
            margin-bottom: 8px;
          "
        >
          <el-input
            v-model="state.queryForm.filter.subject"
            placeholder="请输入标题搜索"
            clearable
            @keyup.enter="judListTypeAndInit"
            @input="fetchDataDebounced"
            style="width: 160px"
          />
          <!-- 状态大类筛选 -->
          <el-select
            v-model="state.selectedStatusCategories"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            placeholder="请选择状态"
            style="width: 200px"
            @change="handleStatusCategoryChange"
          >
            <el-option
              v-for="category in statusCategories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
          <div style="width: 160px" v-if="state.assignList.length > 0">
            <PersonnelSelect
              v-model:value="state.queryForm.filter.assigned_to_id"
              :option-list="state.assignList"
              @change="judListTypeAndInit"
              :is-project-member-list="true"
            />
          </div>
        </div>
        <!-- 第二行：操作按钮 -->
        <div style="display: flex; align-items: center">
          <div>
            <!-- 新增按钮 -->
            <el-button @click="addNewRow" :icon="Plus" text>快速创建</el-button>
            <el-button
              v-if="props.listType != 'child'"
              @click="relateToIssue"
              style="margin-left: 8px"
              :icon="Search"
              text
            >
              {{ '关联' + state.tabNameToTrackerNameMap[props.listType] }}
            </el-button>
          </div>
          <div>
            <!-- 列设置按钮 -->
            <el-popover
              ref="columnPopoverRef"
              placement="bottom-end"
              :width="240"
              :show-arrow="false"
              popper-class="column-set-popover"
              popper-style="padding:16px 0px 0px 0px"
              trigger="click"
            >
              <template #reference>
                <el-button
                  style="width: 20px; border: none"
                  plain
                  @click.stop="showColumnForm"
                >
                  <vab-icon
                    style="width: 22px; height: 22px; margin-right: 0px"
                    class="button-icon"
                    icon="setting"
                    is-custom-svg
                  />
                </el-button>
              </template>
              <div style="padding-left: 8px">
                <span
                  style="
                    padding-right: 16px;
                    padding-left: 8px;
                    font-weight: bold;
                    color: #999;
                  "
                >
                  请选择列表中要展示的信息
                </span>
                <el-checkbox
                  style="padding-left: 8px"
                  v-model="state.columnAllChecked"
                  :indeterminate="state.columnIndeterminate"
                  @change="(value) => handleAllChecked(value)"
                >
                  全部
                </el-checkbox>
                <el-scrollbar style="max-height: 450px; overflow: scroll">
                  <draggable
                    item-key="prop"
                    v-model="filteredColumns"
                    ghost-class="ghost"
                    :force-fallback="true"
                    chosen-class="chosenClass"
                    animation="300"
                    filter=".unmover"
                    handle=".move-box"
                    draggable=".mover-item"
                  >
                    <template #item="{ element }">
                      <div
                        style="display: flex"
                        :class="
                          !element.disabledDrag
                            ? 'column-item mover-item'
                            : 'column-item unmover fix-item'
                        "
                      >
                        <el-checkbox
                          :disabled="element.disabledDrag"
                          v-model="element.show"
                          @change="changeColumnchecked"
                        >
                          {{ element.label }}
                        </el-checkbox>
                        <div class="move-box" style="width: 100%; height: 32px">
                          <div
                            v-if="!element.disabledDrag"
                            class="move-box-icon"
                          >
                            <vab-icon icon="draggable-move" is-custom-svg />
                          </div>
                        </div>
                      </div>
                    </template>
                  </draggable>
                </el-scrollbar>
              </div>
              <div>
                <div class="flex-align-center column-set-bottom">
                  <el-button
                    style="margin-left: 8px; font-size: 14px"
                    size="small"
                    link
                    type="primary"
                    @click="handleResetColumn"
                  >
                    恢复默认
                  </el-button>
                  <div style="margin-right: 8px">
                    <el-button
                      style="padding: 3px 6px; font-size: 14px"
                      size="small"
                      @click="showColumnForm"
                    >
                      取消
                    </el-button>
                    <el-button
                      style="padding: 3px 6px; font-size: 14px"
                      size="small"
                      type="primary"
                      @click="handleSaveColumn"
                    >
                      保存
                    </el-button>
                  </div>
                </div>
              </div>
            </el-popover>
          </div>
        </div>
      </div>
    </template>

    <!-- 其他模式：使用原有的单行布局 -->
    <template v-else>
      <div
        style="
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
        "
      >
        <div style="display: flex; gap: 8px; align-items: center">
          <el-input
            v-if="props.listType == 'child'"
            v-model="state.queryForm.filter.subject"
            placeholder="请输入标题搜索"
            clearable
            @keyup.enter="judListTypeAndInit"
            @input="fetchDataDebounced"
            style="width: 160px"
          />
          <!-- 状态大类筛选器 -->
          <el-select
            v-model="state.selectedStatusCategories"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            :placeholder="`请选择${getTabDisplayName()}状态`"
            @change="handleStatusCategoryChange"
            style="width: 200px"
          >
            <el-option
              v-for="category in state.statusCategories"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
          <div style="width: 160px" v-if="state.assignList.length > 0">
            <PersonnelSelect
              v-model:value="state.queryForm.filter.assigned_to_id"
              :option-list="state.assignList"
              @change="judListTypeAndInit"
              :is-project-member-list="true"
            />
          </div>
        </div>
        <div>
          <!-- 新增按钮 -->
          <el-button @click="addNewRow" :icon="Plus" text>快速创建</el-button>
          <el-button
            v-if="props.listType != 'child'"
            @click="relateToIssue"
            style="margin-left: 8px"
            :icon="Search"
            text
          >
            {{ '关联' + state.tabNameToTrackerNameMap[props.listType] }}
          </el-button>
          <!-- 列设置按钮 -->
          <el-popover
            ref="columnPopoverRef"
            placement="bottom-end"
            :width="240"
            :show-arrow="false"
            popper-class="column-set-popover"
            popper-style="padding:16px 0px 0px 0px"
            trigger="click"
          >
            <template #reference>
              <el-button
                style="width: 20px; border: none"
                plain
                @click.stop="showColumnForm"
              >
                <vab-icon
                  style="width: 22px; height: 22px; margin-right: 0px"
                  class="button-icon"
                  icon="setting"
                  is-custom-svg
                />
              </el-button>
            </template>
            <div style="padding-left: 8px">
              <span
                style="
                  padding-right: 16px;
                  padding-left: 8px;
                  font-weight: bold;
                  color: #999;
                "
              >
                请选择列表中要展示的信息
              </span>
              <el-checkbox
                style="padding-left: 8px"
                v-model="state.columnAllChecked"
                :indeterminate="state.columnIndeterminate"
                @change="(value) => handleAllChecked(value)"
              >
                全部
              </el-checkbox>
              <el-scrollbar style="max-height: 450px; overflow: scroll">
                <draggable
                  item-key="prop"
                  v-model="filteredColumns"
                  ghost-class="ghost"
                  :force-fallback="true"
                  chosen-class="chosenClass"
                  animation="300"
                  filter=".unmover"
                  handle=".move-box"
                  draggable=".mover-item"
                >
                  <template #item="{ element }">
                    <div
                      style="display: flex"
                      :class="
                        !element.disabledDrag
                          ? 'column-item mover-item'
                          : 'column-item unmover fix-item'
                      "
                    >
                      <el-checkbox
                        :disabled="element.disabledDrag"
                        v-model="element.show"
                        @change="changeColumnchecked"
                      >
                        {{ element.label }}
                      </el-checkbox>
                      <div class="move-box" style="width: 100%; height: 32px">
                        <div v-if="!element.disabledDrag" class="move-box-icon">
                          <vab-icon icon="draggable-move" is-custom-svg />
                        </div>
                      </div>
                    </div>
                  </template>
                </draggable>
              </el-scrollbar>
            </div>
            <div>
              <div class="flex-align-center column-set-bottom">
                <el-button
                  style="margin-left: 8px; font-size: 14px"
                  size="small"
                  link
                  type="primary"
                  @click="handleResetColumn"
                >
                  恢复默认
                </el-button>
                <div style="margin-right: 8px">
                  <el-button
                    style="padding: 3px 6px; font-size: 14px"
                    size="small"
                    @click="showColumnForm"
                  >
                    取消
                  </el-button>
                  <el-button
                    style="padding: 3px 6px; font-size: 14px"
                    size="small"
                    type="primary"
                    @click="handleSaveColumn"
                  >
                    保存
                  </el-button>
                </div>
              </div>
            </div>
          </el-popover>
        </div>
      </div>
    </template>

    <el-table
      ref="tableRef"
      v-loading="state.loading"
      :data="processedData"
      style="width: 100%; height: 500px; border-top: 1px solid #dcdfe6"
      default-expand-all
      row-key="id"
      :row-style="{ height: '45px' }"
      :cell-style="cellStyleMethod"
      row-class-name="issue-talbe-list-cloumn-select-class"
    >
      <el-table-column width="50" type="selection" align="center">
        <template #default="{ row }">
          <!-- 新增的操作菜单组件 -->
          <span v-if="row.isNew">{{ ' ' }}</span>
          <div v-else style="display: flex; gap: 8px; align-items: center">
            <IssueActionMenu
              :row-data="row"
              :is-visible="state.visiblePopoverIds.includes(row.id)"
              :is-selected="false"
              @popover-show="handlePopoverShow"
              @popover-hide="handlePopoverHide"
              @copy-title="handleCopyTitle"
              @copy-link="handleCopyLink"
              @copy-issue="handleCopyIssue"
              @delete-issue="handleDeleteIssue"
            />
          </div>
        </template>
      </el-table-column>
      <!-- 动态生成列 -->
      <template
        v-for="(column, index) in dynamicTableColumns"
        :key="column.prop"
      >
        <el-table-column
          v-if="column.show && (column.prop !== 'tracker_id' || hasNewRows)"
          :key="index"
          :label="column.label"
          :prop="column.prop"
          :widt="column.width"
          :fixed="column.fixed ?? false"
          :min-width="column.minWidth"
          :show-overflow-tooltip="column.showOverflowTooltip ?? false"
        >
          <template #default="{ row }">
            <!-- 根据列的类型动态渲染不同的内容 -->
            <!-- todo: 加载卡顿优化参考测试计划下用例列表 -->

            <span
              v-if="column.prop === 'subject'"
              style="
                margin-left: 4px;
                display: inline-flex;
                overflow: hidden; /* 隐藏超出容器的文本 */
                text-overflow: ellipsis; /* 超出部分用省略号表示 */
                white-space: nowrap; /* 禁止换行 */
              "
              class="issue-table-list-subject-colomn"
            >
              <template v-if="row.isNew">
                <el-input
                  style="width: 320px"
                  v-model="row.subject"
                  placeholder="请输入"
                  @keyup.enter="quicklyCreateSave(row)"
                />
              </template>
              <template v-else-if="row.subjectEditing">
                <el-input
                  ref="inputRef"
                  :autofocus="true"
                  style="width: 320px"
                  v-model="state.subjectForEdit"
                  @blur="saveSubject(row)"
                  @keyup.enter="saveSubject(row)"
                  placeholder="请输入"
                />
              </template>
              <template v-else>
                <span style="height: 20px; margin-right: 8px">
                  <!-- 如果有 identifier，显示 TrackerTypeTag，否则显示 CommonIcon -->
                  <TrackerTypeTag
                    v-if="hasTrackerIdentifier(row)"
                    :name="row.tracker_name"
                    :tracker-type="row.tracker_type"
                    :color="row.color"
                    :identifier="row.identifier"
                    variant="filled"
                    size="small"
                  />
                  <CommonIcon
                    v-else
                    :issue-type="getIssueTypeFromTracker(row)"
                  />
                </span>
                <a
                  class="common-link"
                  @click="toIssuePage(row.id, row.project_id)"
                >
                  <span class="text-color-secondary">#{{ row.id }}</span>

                  {{ row.subject }}
                </a>
                <vab-icon
                  class="text-operation-button-of-edit common-icon"
                  style="min-width: 24px; margin-top: 1px"
                  icon="edit"
                  is-custom-svg
                  @click="editIssueSubject(row)"
                />
              </template>
            </span>

            <div v-else-if="column.prop === 'priority_id'">
              <IssuePriority
                v-if="state.enumerationList.length > 0"
                v-model:value="row.priority_id"
                :option="state.enumerationList"
                @change="handleSave(row, 'priority_id')"
              />
            </div>

            <div v-else-if="column.prop === 'tracker_id'">
              <template v-if="row.isNew">
                <!-- 新建行显示下拉选择（默认已有父项事项类型） -->
                <el-dropdown
                  class="tracker-select-dropdown"
                  popper-class="tracker-select-popper"
                  max-height="300px"
                  trigger="click"
                >
                  <div
                    class="tracker-dropdown-link"
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      width: 100%;
                      padding: 4px 8px;
                    "
                  >
                    <!-- 图标 -->
                    <div style="flex-shrink: 0">
                      <!-- 如果有 identifier，显示 TrackerTypeTag，否则显示 CommonIcon -->
                      <TrackerTypeTag
                        v-if="hasTrackerIdentifier(row)"
                        :name="row.tracker_name"
                        :tracker-type="row.tracker_type"
                        :color="row.color"
                        :identifier="row.identifier"
                        variant="filled"
                        size="small"
                      />
                      <CommonIcon
                        v-else
                        :issue-type="getIssueTypeFromTracker(row)"
                      />
                    </div>
                    <!-- 文字 -->
                    <div style="flex: 1; text-align: center; margin: 0 8px">
                      {{ row.tracker_name }}
                    </div>
                    <!-- 下拉箭头 -->
                    <div style="flex-shrink: 0">
                      <vab-icon class="select-icon" icon="arrow-down-s-fill" />
                    </div>
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item
                        v-for="tracker in state.availableTrackers"
                        :key="tracker.tracker_id"
                        class="tracker-option"
                        :class="{
                          'is-selected': tracker.tracker_id === row.tracker_id,
                        }"
                        style="padding: 6px 12px !important"
                        @click="handleTrackerChange(row, tracker)"
                      >
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            padding: 4px 0;
                          "
                        >
                          <!-- 如果有 identifier，显示 TrackerTypeTag，否则显示 CommonIcon -->
                          <TrackerTypeTag
                            v-if="hasTrackerIdentifier(tracker)"
                            :name="tracker.tracker_name"
                            :tracker-type="tracker.tracker_type"
                            :color="tracker.color"
                            :identifier="tracker.identifier"
                            variant="filled"
                            size="small"
                          />
                          <CommonIcon
                            v-else
                            :issue-type="getIssueTypeFromTracker(tracker)"
                          />
                          <span style="margin-left: 8px; flex: 1">
                            {{ tracker.tracker_name }}
                          </span>
                          <vab-icon
                            v-if="tracker.tracker_id === row.tracker_id"
                            icon="check-line"
                            style="color: #409eff; margin-left: auto"
                          />
                        </div>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
              <template v-else>
                <!-- 已保存行显示标签 -->
                <!-- 如果有 identifier，显示 TrackerTypeTag，否则显示 CommonIcon -->
                <TrackerTypeTag
                  v-if="hasTrackerIdentifier(row)"
                  :name="row.tracker_name"
                  :tracker-type="row.tracker_type"
                  :color="row.color"
                  :identifier="row.identifier"
                  variant="filled"
                  size="small"
                />
                <CommonIcon v-else :issue-type="getIssueTypeFromTracker(row)" />
              </template>
            </div>
            <div v-else-if="column.prop === 'fixed_version_id'">
              <IssueVersion
                v-if="!row.isNew || (row.isNew && row.fixed_version_id)"
                v-model:value="row.fixed_version_id"
                :defalut-label="row.version_text ? row.version_text.name : null"
                :option="getVersionListForRow(row.project_id)"
                @click="reloadProjectVersionList(row)"
                @change="handleSave(row, 'fixed_version_id')"
              />
            </div>
            <div v-else-if="column.prop === 'status_id'">
              <template
                v-if="
                  row.isNew &&
                  (!row.status_list || row.status_list.length === 0)
                "
              >
                <!-- 新建行且未选择事项类型时显示提示 -->
                <el-text type="info" size="small">请先选择事项类型</el-text>
              </template>
              <template v-else>
                <!-- 有状态列表时显示状态选择器 -->
                <IssueStatus
                  style="width: 80px"
                  v-model:value="row.status_id"
                  :row-data="row"
                  :option="
                    row.status_list ||
                    (row.isNew ? state.newRowStatusList : null)
                  "
                  :only-show-tag="false"
                  @change="handleSave(row, 'status_id')"
                />
              </template>
            </div>
            <div v-else-if="column.prop === 'assigned'">
              <IssueAssign
                v-if="state.assignList.length > 0"
                v-model:value="row.assigned"
                :option="state.assignList"
                :row-data="row"
                :min-width="'236px'"
                @change="handleSave(row, 'assigned')"
              />
            </div>
            <div v-else-if="column.prop === 'author'">
              <div
                style="pointer-events: none"
                class="issue-talbe-list-author-cloumn-select-class"
              >
                <div v-if="row.isNew">
                  <IssueAssign
                    v-if="state.assignList.length > 0"
                    :value="redmine.third_user_id"
                    :option="state.assignList"
                    :row-data="row"
                    :custom-user-id="parseInt(redmine.third_user_id)"
                  />
                </div>
                <div v-else>
                  <IssueAssign
                    v-if="state.assignList.length > 0"
                    :value="row.author_id"
                    :option="state.assignList"
                    :row-data="row"
                    :is-show-author="true"
                  />
                </div>
              </div>
            </div>

            <div v-else-if="column.prop === 'sync_info'">
              <template v-if="!row.isNew">
                <div style="display: flex; align-items: center; gap: 4px">
                  <template v-if="row.sync_status === 'active'">
                    <el-tooltip
                      :content="getSyncDirectionTooltip(row)"
                      placement="top"
                    >
                      <el-tag :type="getSyncTagType(row)" size="small">
                        {{ getSyncDirectionText(row) }}
                      </el-tag>
                    </el-tooltip>
                  </template>
                  <template v-else>
                    <span style="color: #999; font-size: 12px">未同步</span>
                  </template>
                </div>
              </template>
            </div>
            <div v-else-if="column.prop === 'operation'">
              <template v-if="row.isNew">
                <el-button @click="quicklyCreateSave(row)" type="primary">
                  保存
                </el-button>
                <el-button @click="quicklyCreateCancel(row)">取消</el-button>
              </template>
              <template v-else>
                <div style="display: flex; gap: 8px; align-items: center">
                  <el-tooltip
                    content="同步设置"
                    :hide-after="50"
                    placement="top-start"
                    v-if="props.listType !== 'child'"
                  >
                    <a
                      @click="openSyncSettings(row)"
                      class="text-operation-button"
                    >
                      <vab-icon
                        style="width: 22px; height: 22px"
                        class="button-icon"
                        icon="setting"
                        is-custom-svg
                      />
                    </a>
                  </el-tooltip>
                  <el-tooltip
                    v-if="!props.onlyShowProductChildIssue"
                    content="解除关联"
                    :hide-after="50"
                    placement="top-start"
                  >
                    <a @click="unLinkChild(row)" class="text-operation-button">
                      <CommonIcon :type="'release'" />
                    </a>
                  </el-tooltip>
                  <el-tooltip
                    v-if="props.listType == 'child'"
                    content="删除事项"
                    :hide-after="50"
                    placement="top-start"
                  >
                    <a @click="deleteChild(row)" class="text-operation-button">
                      <CommonIcon :type="'delete'" />
                    </a>
                  </el-tooltip>
                </div>
              </template>
            </div>
            <div v-else-if="column.prop === 'project_id'">
              <template v-if="row.isNew && props.listType != 'child'">
                <span>
                  <el-select
                    v-model="row.project_id"
                    style="width: 150px"
                    @change="handleProjectIdChanged(row)"
                    filterable
                    :class="['none-border-on-common']"
                    clearable
                    placeholder="请选择所加入的项目"
                  >
                    <el-option
                      v-for="pItem in state.joinedProjectList"
                      :key="pItem.id"
                      :value="pItem.id"
                      :label="pItem.name || ''"
                    >
                      {{ pItem.name }}
                    </el-option>
                  </el-select>
                </span>
              </template>
              <template v-else>
                <span>
                  {{
                    row.project_text?.name ??
                    state.joinedProjectList.find(
                      (pItem) => pItem.id == row.project_id
                    )?.name
                  }}
                </span>
              </template>
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <edit-dialog ref="editRef" :height="dialogHeight - 100" />
    <IssueRelationDialog
      ref="issueRelationDialogRef"
      :enumeration-list="state.enumerationList"
      :list-type="props.listType"
      :info="props.info"
      :now-relations="processedData"
      @fetch-relations="
        () => {
          emit('refresh-count')
          judListTypeAndInit()
        }
      "
      :joined-project-list="state.joinedProjectList"
    />
    <IssueSyncDialog
      ref="issueSyncDialogRef"
      :source-issue="props.info"
      @sync-created="handleSyncCreated"
      @sync-updated="handleSyncUpdated"
    />
  </div>
</template>
<script setup>
  import { Plus, Search } from '@element-plus/icons-vue'
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import IssueStatus from '@/components/IssueStatus.vue'
  import IssuePriority from '@/components/IssuePriority.vue'
  import IssueAssign from '@/components/IssueAssign.vue'
  import { useUserStore } from '@/store/modules/user'
  import {
    issueStatusClass,
    trackerIdMap,
    getStatusColorByCategory,
  } from '~/src/json/issues.js'
  import {
    doEdit,
    getEnumerationList,
    getIssueStatus,
    getIssueInfo,
    issueRelationsList,
    delIssueRelation,
    addIssueRelation,
    doDelete,
    getAllChild,
    getSyncRelationList,
    deleteSyncRelation,
  } from '@/api/projectIssue'
  import EditDialog from './IssueEditDialog.vue'
  import IssueRelationDialog from './IssueRelationDialog.vue'
  import IssueSyncDialog from './IssueSyncDialog.vue'
  import { getList as getVersionList } from '@/api/projectVersion'
  import IssueVersion from '@/components/IssueVersion.vue'
  import draggable from 'vuedraggable'
  import PersonnelSelect from '~/src/components/PersonnelSelect.vue'
  import { getJoinedProjectList } from '@/api/projectIndex'
  import IssueActionMenu from '@/components/IssueActionMenu.vue'

  import {
    getImgByIssueType,
    IssueIconParent,
    IssueIconChild,
    IssueIconRelation,
    getTrackerIcon,
  } from '@/utils/index'
  import { stat } from 'fs'
  import OuthelpRecordList from '~/src/views/production/production/components/OuthelpRecordList.vue'
  import { getNewIssueStatus } from '@/api/projectIssue'
  import { getInitialStatuses, getProjectStatuses } from '@/api/customWorkflow'
  import { getProjectTrackersWithTemplates } from '@/api/projectTracker'
  import TrackerTypeTag from '@/components/TrackerTypeTag.vue'

  const emit = defineEmits(['fetch-overview', 'refresh-count'])

  const inputRef = ref(null) // 创建对 el-input 的引用

  // 状态大类定义
  const statusCategories = [
    { value: 'not_started', label: '未开始' },
    { value: 'in_progress', label: '进行中' },
    { value: 'awaiting_acceptance', label: '待验收' },
    { value: 'completed', label: '已完成' },
  ]

  const userStore = useUserStore()
  let { redmine, user_settings, user_id } = userStore

  const props = defineProps({
    info: {
      type: Object,
      default: () => {
        return {}
      },
    },
    list: {
      type: Array,
      default: () => {
        return []
      },
    },
    parentId: {
      type: Number,
      default: null,
    },
    openType: {
      type: String,
      default: 'page',
    },
    listType: {
      type: String,
      // child=子事项, relation=关联事项
      default: 'child',
    },
    fixedVersionId: {
      type: Number,
      default: null,
    },
    fixedVersionList: {
      type: Array,
      default: () => {
        return []
      },
    },
    assignOptions: {
      type: Array,
      default: () => {
        return {}
      },
    },
    extendWatcher: {
      type: Number,
      default: 0,
    },
    tagLabel: {
      type: String,
      default: '',
    },
    onlyShowProductChildIssue: {
      type: Boolean,
      default: false,
    },
  })

  const { info, assignOptions, list, extendWatcher, fixedVersionList } =
    toRefs(props)

  const tableColumns = [
    {
      label: '标题',
      prop: 'subject',
      showOverflowTooltip: true,
      minWidth: 350,
      show: true,
      disabledDrag: true,
    },
    {
      label: '事项类型',
      prop: 'tracker_id',
      width: 160,
      minWidth: 160,
      show: true,
    },
    {
      label: '优先级',
      prop: 'priority_id',
      minWidth: 110,
      show: true,
    },
    // {
    //   label: '版本',
    //   prop: 'fixed_version_id',
    //   minWidth: 150,
    //   show: true,
    // },
    {
      label: '状态',
      prop: 'status_id',
      minWidth: 110,
      show: true,
    },
    {
      label: '处理人',
      prop: 'assigned',
      width: 254,
      minWidth: 254,
      show: true,
    },
    {
      label: '所属' + (props.onlyShowProductChildIssue ? '产品' : '项目'),
      prop: 'project_id',
      width: 160,
      minWidth: 160,
      show: true,
    },
    {
      label: '创建人',
      prop: 'author',
      width: 124,
      minWidth: 124,
      show: true,
    },
    {
      label: '信息同步',
      prop: 'sync_info',
      width: 100,
      minWidth: 100,
      show: true,
    },
    {
      label: '操作',
      prop: 'operation',
      minWidth: 200,
      show: true,
      disabledDrag: true,
    },
  ]
  if (!props.onlyShowProductChildIssue) {
    tableColumns.splice(2, 0, {
      label: '版本',
      prop: 'fixed_version_id',
      minWidth: 150,
      show: true,
    })
  }

  const extend = ref(extendWatcher)

  const processedData = ref([])
  const versionList = ref([])

  const tempSaveField = ref({})

  // 统一的状态颜色获取函数（与 IssueStatus 组件保持一致）
  const getStatusClass = (statusItem) => {
    const statusId = statusItem.id || statusItem.new_status_id
    // 如果有 status_category，优先使用动态映射（包括空字符串）
    if (statusItem.status_category) {
      return getStatusColorByCategory(statusItem.status_category)
    }
    // 否则使用硬编码映射
    return issueStatusClass[statusId] || ''
  }

  const filteredColumns = ref([...tableColumns])

  const dynamicTableColumns = ref([...tableColumns])

  const editRef = ref(null)
  const elInputRef = ref(null)
  const issueRelationDialogRef = ref(null)
  const issueSyncDialogRef = ref(null)

  watch(info, (v) => {
    // info.value = v
    state.form.parent_id = v.id
    state.form.tracker_id = v.tracker_id
    state.form.project_id = v.project_id
    state.form.watcher_count = v.watcher_count
    state.form.watchers = v.watchers
    // updateTreeData()
    processedData.value.length = 0
  })
  watch(assignOptions, (v) => {
    // info.value = v
    state.assignList = v
  })
  watch(extendWatcher, (v) => {
    extend.value = v
  })

  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')
  const $pub = inject('$pub')

  const dialogHeight = computed(() => {
    const h = document.body.clientHeight - 40
    return h
  })

  // 检查是否有新建行
  const hasNewRows = computed(() => {
    return processedData.value.some((row) => row.isNew)
  })
  const state = reactive({
    subjectForEdit: false,
    columnAllChecked: false,
    columnIndeterminate: false, // el checkBox的待定状态
    columnVisiblePopover: false,
    visiblePopoverIds: [], // 当前显示popover的行ID列表
    // 动态事项类型映射（将在初始化时填充）
    trackerIdToTabNameMap: {},
    tabNameToTrackerIdMap: {},
    tabNameToTrackerNameMap: {
      bugRelation: '缺陷',
      requirementRelation: '需求',
      taskRelation: '任务',
      progressRelation: '流程任务',
    },
    loading: false,
    addRelation: false,
    assignList: props.assignOptions ?? [],
    nowDate: new Date().setHours(0, 0, 0, 0),
    isAdd: false,

    form: {
      subject: null,
      parent_id: props.info.id,
      tracker_id: props.info.tracker_id,
      project_id: props.info.project_id,
      status_id: 1,
      priority_id: 3,
      description: '',
      category_id: props.info.category_id,
      watcher_count: props.info.watcher_count,
      watchers: props.info.watchers,
      class_id: props.info.class_id,
    },
    queryForm: {
      filter: {
        issueAssigned: {
          user_id: null,
        },
      },
      op: {
        issueAssigned: {
          user_id: '=',
        },
        tracker_id: 'IN',
        subject: 'like',
        status_id: 'in',
      },
      limit: 9999,
    },
    enumerationList: [],
    issueStatusList: [],
    newRowStatusList: [], // 新建行的状态列表
    availableTrackers: [], // 可用的事项类型列表
    selectedStatusIds: [], // 级联选择器选中的状态ID（保留兼容性）
    selectedStatusCategories: [], // 选中的状态大类
    statusHierarchy: [], // 状态分层数据（保留兼容性）
    statusCategories: [], // 状态大类选项
    requirementStatusHierarchy: [], // 需求状态分层数据
    bugStatusHierarchy: [], // 缺陷状态分层数据
    statusCascaderProps: {
      value: 'value',
      label: 'label',
      children: 'children',
      multiple: true,
      checkStrictly: false,
      emitPath: false,
    },
    requirementStatusCascaderProps: {
      value: 'value',
      label: 'label',
      children: 'children',
      multiple: true,
      checkStrictly: false, // 允许父子节点关联，点击父级自动选择所有子级
      emitPath: false,
    },
    bugStatusCascaderProps: {
      value: 'value',
      label: 'label',
      children: 'children',
      multiple: true,
      checkStrictly: false, // 允许父子节点关联，点击父级自动选择所有子级
      emitPath: false,
    },
    priorityClassList: {
      2: require('@/assets/icon_images/low.png'),
      3: require('@/assets/icon_images/normal.png'),
      4: require('@/assets/icon_images/high.png'),
      5: require('@/assets/icon_images/urgent.png'),
      6: require('@/assets/icon_images/immediate.png'),
    },
    joinedProjectList: [],
    projectVersionMap: {}, // 添加项目版本映射缓存
  })

  /**
   * 问题详情页面
   */
  const toIssuePage = (id, project_id) => {
    window.open(
      '/#/project/detail?issue_id=' + id + '&project_id=' + project_id
    )
    // }
  }

  /**
   * 优先级列表获取
   */
  const handleEnumerationList = async () => {
    const { data } = await getEnumerationList()
    state.enumerationList = data
  }

  const doAdd = (value) => {
    if (value === false) {
      state.form.subject = null
    }
    state.isAdd = value
    if (value == true) {
      elInputRef.value.focus()
    }
  }

  /**
   * 事项标题编辑
   */
  const editIssueSubject = (currentRow) => {
    state.subjectForEdit = currentRow.subject
    processedData.value.forEach((row) => {
      row.subjectEditing = false
    })
    // 将当前行设置为编辑状态
    currentRow.subjectEditing = true
    setFocus()
  }

  /**
   * 保存编辑的标题
   */
  const saveSubject = async (row) => {
    if (row.subjectEditing) {
      // 存在编辑差异需要保存
      if (row.subject != state.subjectForEdit) {
        row.subject = state.subjectForEdit
        await doEdit({ id: row['id'], subject: row.subject })

        $baseMessage('修改成功', 'success', 'vab-hey-message-success')
      }
      row.subjectEditing = false
    }
  }

  // 在需要的时候设置el-input焦点
  const setFocus = async () => {
    await new Promise((resolve) => setTimeout(resolve, 100))
    inputRef.value?.[0].focus() // 获取 input 元素并设置焦点
  }

  /**
   * 解除关联
   */
  const unLinkChild = (row) => {
    $baseConfirm('是否确定取消关联', null, () => {
      if (props.listType == 'child') {
        doEdit({ id: row['id'], parent_id: '' }).then(() => {
          $pub('issue-edit-success', [null, 'parent_id'])
          emit('fetch-overview')
          $baseMessage('修改成功', 'success', 'vab-hey-message-success')
          emit('refresh-count')
        })
      } else {
        delIssueRelation({ ids: row.relation_id }).then((res) => {
          deleteSyncRelation({
            relation_id: row.sync_relation_id,
          })
          $baseMessage('解除成功', 'success', 'vab-hey-message-success')
          emit('refresh-count')
          judListTypeAndInit()
        })
      }
    })
  }

  /**
   * 事项删除
   */
  const deleteChild = (row) => {
    $baseConfirm('确认是否删除该事项?', null, () => {
      doDelete({ ids: row.id }).then(async (r) => {
        $baseMessage(r.msg, 'success', 'vab-hey-message-success')
        $pub('issue-del-success', 'IssueChildListTable')
        $pub('issue-edit-success', [null, null])
        // emit('refresh-count')
        const data = list.value
        emit('refresh-count', [
          props.listType,
          data.length ?? 0,
          props.info.id,
          list.value,
        ])

        // await new Promise((resolve) => setTimeout(resolve, 1000))
        judListTypeAndInit()
      })
    })
  }

  /**
   * @description 根据字段更新
   */
  const handleSave = async (row, field, value) => {
    let newValue = row[field]
    if (value) {
      //部分下拉的逻辑可能不是model，需由该参数传进来
      newValue = value
    }

    if (row.isNew) {
      tempSaveField.value[field] = newValue
      return
    }

    doEdit({ id: row['id'], [field]: newValue }).then(async () => {
      $baseMessage('修改成功', 'success', 'vab-hey-message-success')

      // 如果修改的是状态字段，需要重新获取该事项的可用状态列表
      if (field === 'status_id') {
        try {
          // 更新行数据中的状态ID
          row.status_id = newValue

          // 重新获取该事项的可用状态列表
          const response = await getNewIssueStatus({
            project_id: row.project_id,
            tracker_id: row.tracker_id,
            status_id: newValue, // 使用新的状态ID
            author_id: row.author_id,
            assigned_to_id: row.assigned_to_id,
            issue_id: row.id,
          })

          // 更新该事项的状态列表
          if (response.data && response.data.length > 0) {
            row.status_list = response.data
          }
        } catch (error) {
          console.error('重新获取状态列表失败:', error)
        }
      }
    })
  }

  /**
   * 更新列表数据，构造树形结构
   * @param {*} data
   */
  const updateTreeData = (data) => {
    const map = {}
    const tree = []

    const childrenData = data ?? list.value

    childrenData.forEach((item) => {
      // 处理关联事项的 tracker 字段映射
      let processedItem = { ...item }

      // 如果有 issue_type 对象，将其字段映射到根级别
      if (item.issue_type) {
        processedItem.tracker_name = item.issue_type.name
        processedItem.tracker_type = item.issue_type.tracker_type
        processedItem.color = item.issue_type.color
        processedItem.identifier = item.issue_type.identifier
      }

      // 🔧 为关联事项构建状态列表，包含当前状态，让IssueStatus组件能正确显示
      if (item.issue_status && !item.isNew) {
        processedItem.status_list = [
          {
            id: item.issue_status.id,
            name: item.issue_status.name,
            label: item.issue_status.name,
            value: item.issue_status.id,
            status_category: item.issue_status.status_category,
            className: getStatusClass(item.issue_status),
          },
        ]
      }

      map[item.id] = {
        ...processedItem,
        children: [],
      }
    })

    childrenData.forEach((item) => {
      if (
        item.parent_id == null ||
        item.parent_id == 0 ||
        item.parent_id == info.value.id
      ) {
        tree.push(map[item.id])
      } else if (!map[item.parent_id]) {
        tree.push(map[item.id])
      } else {
        map[item.parent_id]?.children.push(map[item.id])
      }
    })

    processedData.value = tree
    state.loading = false
  }

  /**
   * 重新加载项目版本列表
   * @param {*} row 当前行数据
   */
  const reloadProjectVersionList = async (row) => {
    if (!row.project_id) return
    // 移除多余的日志输出
    // console.log('state.projectVersionMap:', state.projectVersionMap)

    // 如果已经缓存了该项目的版本列表，直接使用缓存
    if (state.projectVersionMap[row.project_id]) {
      versionList.value = state.projectVersionMap[row.project_id]
      return
    }

    try {
      const { data } = await getVersionList({
        filter: {
          project_id: row.project_id,
        },
        limit: 100,
        status: 'open',
      })

      // 缓存该项目的版本列表
      state.projectVersionMap[row.project_id] = data.data ?? []
      versionList.value = state.projectVersionMap[row.project_id] // 使用缓存的数据，保持引用一致
    } catch (error) {
      console.error('获取项目版本列表失败:', error)
      versionList.value = []
    }
  }

  /**
   * 更新版本列表，现在只用于初始化默认项目的版本
   */
  const updateVersionList = () => {
    const tree = []

    fixedVersionList.value.forEach((item) => {
      tree.push(item)
    })
    versionList.value = tree

    // 将默认项目的版本列表添加到缓存
    if (props.info.project_id && versionList.value.length > 0) {
      state.projectVersionMap[props.info.project_id] = _.cloneDeep(
        versionList.value
      )
    }
  }

  /**
   * 关联新事项
   */
  const relateToIssue = () => {
    issueRelationDialogRef.value.showEdit()
  }

  // 新增行的方法
  const addNewRow = async () => {
    if (processedData.value[0]?.isNew) {
      return
    }
    state.loading = true
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 先加载可用事项类型
    await loadAvailableTrackers()

    // 根据列表类型确定默认的事项类型
    let defaultTracker = null
    let defaultTrackerId = null

    if (props.listType === 'child') {
      // 子事项：使用父事项的类型
      defaultTrackerId = props.info.tracker_id
      defaultTracker = state.availableTrackers.find(
        (tracker) => tracker.tracker_id === props.info.tracker_id
      )
    } else {
      // 关联事项：使用该Tab页对应类型的第一个事项类型
      defaultTracker = state.availableTrackers[0] || null
      defaultTrackerId = defaultTracker?.tracker_id || props.info.tracker_id
    }

    // 为默认事项类型加载状态列表
    const defaultStatusList = await loadNewRowStatusList(defaultTrackerId)

    // 设置默认状态ID
    const defaultStatusId =
      defaultStatusList.length > 0 ? defaultStatusList[0].value : 1

    const newRow = {
      id: Date.now(), // 使用时间戳生成一个唯一的ID
      subject: '',
      assigned: '',
      priority_id: 3,
      status_id: defaultStatusId, // 使用动态获取的默认状态
      isNew: true, // 标记该行是新增行
      fixed_version_id: props.fixedVersionId,
      issue_assigned: [],
      project_id: props.info.project_id,
      // start_date: props.info.start_date,
      tracker_id: defaultTrackerId, // 使用根据列表类型确定的默认事项类型
      parent_id: props.listType == 'child' ? props.info.id : 0,
      class_id:
        defaultTrackerId == trackerIdMap['需求'] ? props.info.class_id : 0, // 需求事项继承事项分类
      // 设置默认事项类型的状态列表
      status_list: [...defaultStatusList],
      // 设置默认事项类型信息
      tracker_name: defaultTracker?.tracker_name || '',
      tracker_type: defaultTracker?.tracker_type || '',
      color: defaultTracker?.color || '',
      identifier: defaultTracker?.identifier || '',
    }
    processedData.value.unshift(newRow) // 将新行添加到数组的最前面

    dynamicTableColumns.value.length = 0 // 清空原有列
    let tmpColumns = _.cloneDeep(filteredColumns.value)
    // 使用 map 处理并返回新的数组
    dynamicTableColumns.value = tmpColumns.map((item) => {
      if (item.prop === 'operation') {
        item.fixed = 'right' // 修改 'operation' 列的 fixed 值
      }
      return item
    })
    state.loading = false
    // dynamicTableColumns.value = _.cloneDeep(filteredColumns.value)

    // _.forEach(dynamicTableColumns, (v, k) => {
    //   if (dynamicTableColumns.value[k]?.prop == 'operation') {
    //     dynamicTableColumns.value[k]['fixed'] = 'right'
    //   }
    // })
  }

  /**
   * 快速创建取消
   * @param {*} row
   */
  const quicklyCreateCancel = async (row) => {
    state.loading = true
    await new Promise((resolve) => setTimeout(resolve, 100))
    processedData.value.shift()
    dynamicTableColumns.value = _.cloneDeep(filteredColumns.value)
    state.loading = false
  }

  /**
   * 快速创建保存
   * @param {*} row
   */
  const quicklyCreateSave = _.throttle(async (row) => {
    // 验证事项类型是否已选择
    if (!row.tracker_id) {
      $baseMessage('请先选择事项类型', 'error', 'vab-hey-message-error')
      state.loading = false
      return
    }

    // 验证事项类型是否符合父事项状态分类限制
    const selectedTracker = state.availableTrackers.find(
      (tracker) => tracker.tracker_id === row.tracker_id
    )
    const parentTracker = state.availableTrackers.find(
      (tracker) => tracker.tracker_id === props.info.tracker_id
    )

    if (
      selectedTracker &&
      parentTracker &&
      selectedTracker.tracker_type !== parentTracker.tracker_type
    ) {
      $baseMessage(
        `子事项类型必须与父事项保持同一大类（${parentTracker.tracker_type}）`,
        'error',
        'vab-hey-message-error'
      )
      state.loading = false
      return
    }

    // 验证标题是否已填写
    if (!row.subject || row.subject.trim() === '') {
      $baseMessage('请填写事项标题', 'error', 'vab-hey-message-error')
      state.loading = false
      return
    }

    state.loading = true
    await new Promise((resolve) => setTimeout(resolve, 100))
    _.forEach(row, (v, k) => {
      tempSaveField.value[k] = v
    })
    tempSaveField.value['id'] = -1
    tempSaveField.value['isNew'] = null

    doEdit(tempSaveField.value)
      .then(async (res) => {
        if (props.listType != 'child' && res.data?.issue?.id) {
          await addIssueRelation({
            id: props.info.id,
            issue_to_id: res.data?.issue?.id,
            relation_type: 'relates',
          })
        }
        $baseMessage('创建成功', 'success', 'vab-hey-message-success')
        $pub('issue-add-success')
        emit('refresh-count', [
          props.listType,
          list.value.length ?? 0,
          props.info.id,
          list.value,
        ])

        dynamicTableColumns.value = _.cloneDeep(filteredColumns.value)
        judListTypeAndInit()
      })
      .finally(() => {
        state.loading = false
      })
  }, 1000)

  /**
   * 获取子工作项
   */
  const getNewChildList = async () => {
    let queryForm = _.cloneDeep(state.queryForm)
    queryForm.id = props.info.id

    delete queryForm.filter.issueAssigned
    if (!queryForm.filter.assigned_to_id) {
      delete queryForm.filter.assigned_to_id
    } else {
      if (
        typeof queryForm.filter.issueAssigned == 'undefined' &&
        queryForm.filter.assigned_to_id
      ) {
        queryForm.filter.issueAssigned = {}
        queryForm.op.issueAssigned = { user_id: '=' }
      }
      queryForm.filter.issueAssigned['user_id'] =
        queryForm.filter.assigned_to_id
      queryForm.op.issueAssigned['user_id'] = '='
      delete queryForm.filter.assigned_to_id
    }

    let { data } = await getAllChild(queryForm)
    list.value = data // 平铺的数据
    // 加载同步状态
    await loadSyncStatus(data)
    emit('refresh-count', [
      props.listType,
      data.length ?? 0,
      queryForm.id,
      data,
    ])
    updateTreeData(data)
    state.loading = false
  }

  /**
   * 动态初始化事项类型映射
   */
  const initTrackerMapping = async () => {
    try {
      const result = await getProjectTrackersWithTemplates(
        props.info.project_id
      )
      const trackers = result.data || []

      // 按事项大类分组
      const trackersByType = {
        bug: [],
        requirement: [],
        task: [],
        other: [],
      }

      trackers.forEach((tracker) => {
        const type = tracker.tracker_type
        if (type === 'bug') {
          trackersByType.bug.push(tracker.tracker_id)
        } else if (type === 'requirement') {
          trackersByType.requirement.push(tracker.tracker_id)
        } else if (type === 'task') {
          trackersByType.task.push(tracker.tracker_id)
        } else {
          trackersByType.other.push(tracker.tracker_id)
        }
      })

      // 构建动态映射
      state.tabNameToTrackerIdMap = {
        bugRelation: trackersByType.bug.join(','),
        requirementRelation: trackersByType.requirement.join(','),
        taskRelation: trackersByType.task.join(','),
        progressRelation: trackersByType.other.join(','), // 其他类型归为流程
      }

      // 构建反向映射
      state.trackerIdToTabNameMap = {}
      trackersByType.bug.forEach((id) => {
        state.trackerIdToTabNameMap[id] = 'bugRelation'
      })
      trackersByType.requirement.forEach((id) => {
        state.trackerIdToTabNameMap[id] = 'requirementRelation'
      })
      trackersByType.task.forEach((id) => {
        state.trackerIdToTabNameMap[id] = 'taskRelation'
      })
      trackersByType.other.forEach((id) => {
        state.trackerIdToTabNameMap[id] = 'progressRelation'
      })
    } catch (error) {
      console.error('初始化事项类型映射失败:', error)
      // 降级到硬编码映射
      state.tabNameToTrackerIdMap = {
        bugRelation: `${trackerIdMap['bug']},${trackerIdMap['Defect']}`,
        requirementRelation: `${trackerIdMap['需求']},${trackerIdMap['Feature']}`,
        taskRelation: `${trackerIdMap['任务']},${trackerIdMap['Support']}`,
        progressRelation: trackerIdMap['研发流程'],
      }
      state.trackerIdToTabNameMap = {
        [trackerIdMap['bug']]: 'bugRelation',
        [trackerIdMap['需求']]: 'requirementRelation',
        [trackerIdMap['任务']]: 'taskRelation',
        [trackerIdMap['Defect']]: 'bugRelation',
        [trackerIdMap['Feature']]: 'requirementRelation',
        [trackerIdMap['Support']]: 'taskRelation',
        [trackerIdMap['研发流程']]: 'progressRelation',
      }
    }
  }

  /**
   * 获取关联事项
   */
  const queryIssueRelation = async (tracker_id) => {
    let queryForm = _.cloneDeep(state.queryForm)
    queryForm.filter.tracker_id = tracker_id
    queryForm.filter.issue_id = props.info.id

    delete queryForm.filter.issueAssigned
    if (!queryForm.filter.assigned_to_id) {
      delete queryForm.filter.assigned_to_id
    } else {
      if (
        typeof queryForm.filter.issueAssigned == 'undefined' &&
        queryForm.filter.assigned_to_id
      ) {
        queryForm.filter.issueAssigned = {}
        queryForm.op.issueAssigned = { user_id: '=' }
      }
      queryForm.filter.issueAssigned['user_id'] =
        queryForm.filter.assigned_to_id
      queryForm.op.issueAssigned['user_id'] = '='
      delete queryForm.filter.assigned_to_id
    }

    const { data } = await issueRelationsList(queryForm)
    if (data.data) {
      // 加载同步状态
      await loadSyncStatus(data.data)
      updateTreeData(data.data)
    } else {
      processedData.value = []
      state.loading = false
    }
    state.loading = false
  }

  /**
   * 获取状态列表
   */
  const handleIssueStatusList = async () => {
    let data = []

    if (props.onlyShowProductChildIssue) {
      // 产品模式：使用特定的产品状态
      const result = await getNewIssueStatus({
        tracker_id: 21,
        project_id: state.form.project_id,
      })
      data = result.data
    } else if (props.listType === 'child') {
      // 子事项模式：使用项目级别的状态查询
      try {
        const statusResult = await getProjectStatuses({
          project_id: props.info.project_id, // 使用项目ID查询
        })
        data = statusResult.data || []
      } catch (error) {
        // 出错时降级到系统默认状态
        const result = await getIssueStatus()
        data = result.data || []
      }
    } else {
      // 关联事项模式：根据当前tab获取对应类型的所有状态（跨项目查询）
      const trackerType = getTrackerTypeByTab(props.listType)
      try {
        const statusResult = await getProjectStatuses({
          tracker_type: trackerType, // 跨项目查询
        })
        data = statusResult.data || []
      } catch (error) {
        // 出错时降级到系统默认状态
        const result = await getIssueStatus()
        data = result.data || []
      }
    }

    state.issueStatusList.length = 0
    data.forEach((item) => {
      state.issueStatusList.push({
        label: item.name,
        value: item.id,
        className: issueStatusClass[item.id],
        status_category: item.status_category,
        tracker_type: item.tracker_type,
      })
    })
    buildStatusHierarchy()
  }

  /**
   * 获取新建行的初始状态列表
   */
  const loadNewRowStatusList = async (trackerId = null) => {
    try {
      // 使用传入的 trackerId 或默认使用父事项的 tracker_id
      const targetTrackerId = trackerId || props.info.tracker_id

      const params = {
        project_id: props.info.project_id,
        tracker_id: targetTrackerId,
      }
      const { data } = await getInitialStatuses(params)

      // 如果是为特定行加载状态，返回状态列表
      if (trackerId) {
        return data.map((item) => ({
          id: item.id,
          name: item.name,
          label: item.name,
          value: item.id,
          status_category: item.status_category,
          className: getStatusClass(item),
        }))
      }

      // 否则更新全局的新行状态列表
      state.newRowStatusList.length = 0
      data.forEach((item) => {
        state.newRowStatusList.push({
          id: item.id, // IssueStatus 组件需要的字段
          name: item.name, // IssueStatus 组件需要的字段
          label: item.name, // 保持兼容性
          value: item.id,
          status_category: item.status_category, // 保留状态分类信息
          className: getStatusClass(item), // 使用统一的颜色获取函数
        })
      })
    } catch (error) {
      console.warn('获取新建行状态列表失败，使用筛选状态列表:', error)
      return []
    }
  }

  /**
   * 根据tab获取显示名称
   */
  const getTabDisplayName = () => {
    const tabNames = {
      bugRelation: '缺陷',
      requirementRelation: '需求',
      taskRelation: '任务',
      progressRelation: '流程任务',
    }
    return tabNames[props.listType] || ''
  }

  /**
   * 根据tab获取tracker_type
   */
  const getTrackerTypeByTab = (listType) => {
    const mapping = {
      bugRelation: 'bug',
      requirementRelation: 'requirement',
      taskRelation: 'task',
      progressRelation: 'other',
    }
    return mapping[listType]
  }

  /**
   * 构建状态层级数据（保留兼容性）
   */
  const buildStatusHierarchy = () => {
    const statusGroups = [
      { value: 'not_started', label: '未开始', category: 'not_started' },
      { value: 'in_progress', label: '进行中', category: 'in_progress' },
      {
        value: 'awaiting_acceptance',
        label: '待验收',
        category: 'awaiting_acceptance',
      },
      { value: 'completed', label: '已完成', category: 'completed' },
    ]

    // 构建层级数据（保留兼容性）
    state.statusHierarchy = statusGroups
      .map((group) => ({
        value: group.value,
        label: group.label,
        children: state.issueStatusList
          .filter((status) => status.status_category === group.category)
          .map((status) => ({
            value: status.value,
            label: status.label,
            className: status.className,
          })),
      }))
      .filter((group) => group.children.length > 0)

    // 构建状态大类选项
    buildStatusCategories()
  }

  /**
   * 构建状态大类选项
   */
  const buildStatusCategories = () => {
    const statusGroups = [
      { value: 'not_started', label: '未开始', category: 'not_started' },
      { value: 'in_progress', label: '进行中', category: 'in_progress' },
      {
        value: 'awaiting_acceptance',
        label: '待验收',
        category: 'awaiting_acceptance',
      },
      { value: 'completed', label: '已完成', category: 'completed' },
    ]

    // 只显示有对应状态的大类
    state.statusCategories = statusGroups.filter((group) => {
      return state.issueStatusList.some(
        (status) => status.status_category === group.category
      )
    })
  }

  /**
   * 状态大类筛选变化处理
   */
  const handleStatusCategoryChange = (selectedCategories) => {
    state.selectedStatusCategories = selectedCategories

    if (selectedCategories && selectedCategories.length > 0) {
      // 根据选中的状态大类，获取所有对应的小类状态ID
      const allStatusIds = []

      selectedCategories.forEach((category) => {
        const categoryStatuses = state.issueStatusList.filter(
          (status) => status.status_category === category
        )
        categoryStatuses.forEach((status) => {
          allStatusIds.push(status.value)
        })
      })

      if (allStatusIds.length > 0) {
        state.queryForm.filter.status_id = allStatusIds.join(',')
        state.queryForm.op.status_id = 'IN'
      } else {
        state.queryForm.filter.status_id = null
        delete state.queryForm.op.status_id
      }
    } else {
      // 未选择任何状态大类时，清除筛选条件
      state.queryForm.filter.status_id = null
      delete state.queryForm.op.status_id
    }

    judListTypeAndInit()
  }

  /**
   * 根据列表类型获取对应的事项大类
   */
  const getTrackerTypeByListType = (listType) => {
    const typeMapping = {
      bugRelation: 'bug',
      requirementRelation: 'requirement',
      taskRelation: 'task',
      progressRelation: 'other',
    }
    return typeMapping[listType] || null
  }

  /**
   * 获取项目可用的事项类型列表
   */
  const loadAvailableTrackers = async () => {
    try {
      const { data } = await getProjectTrackersWithTemplates(
        props.info.project_id
      )

      // 获取所有可用事项类型
      const allTrackers = data || []

      // 根据不同的列表类型过滤可选的事项类型
      if (props.listType === 'child') {
        // 子事项：根据父事项的tracker_type过滤（保持原有逻辑）
        const parentTracker = allTrackers.find(
          (tracker) => tracker.tracker_id === props.info.tracker_id
        )

        if (parentTracker && parentTracker.tracker_type) {
          state.availableTrackers = allTrackers.filter(
            (tracker) => tracker.tracker_type === parentTracker.tracker_type
          )
        } else {
          state.availableTrackers = allTrackers
        }
      } else {
        // 关联事项：根据Tab页类型过滤
        const targetTrackerType = getTrackerTypeByListType(props.listType)
        if (targetTrackerType) {
          state.availableTrackers = allTrackers.filter(
            (tracker) => tracker.tracker_type === targetTrackerType
          )
        } else {
          state.availableTrackers = allTrackers
        }
      }
    } catch (error) {
      console.error('获取事项类型失败:', error)
      state.availableTrackers = []
    }
  }

  /**
   * 处理事项类型变更
   */
  const handleTrackerChange = async (row, selectedTracker) => {
    try {
      // 更新行的事项类型相关信息
      row.tracker_id = selectedTracker.tracker_id
      row.tracker_name = selectedTracker.tracker_name
      row.tracker_type = selectedTracker.tracker_type
      row.color = selectedTracker.color
      row.identifier = selectedTracker.identifier

      // 为新的事项类型加载状态列表
      const statusList = await loadNewRowStatusList(selectedTracker.tracker_id)

      if (statusList && statusList.length > 0) {
        // 将状态列表绑定到当前行
        row.status_list = statusList

        // 重置为该事项类型的默认状态（第一个可用状态）
        row.status_id = statusList[0].value
      } else {
        // 如果没有获取到状态，使用系统默认状态
        row.status_id = 1
        row.status_list = []
        console.warn('未获取到状态列表，使用默认状态')
      }
    } catch (error) {
      console.error('处理事项类型变更失败:', error)
      // 出错时使用默认状态
      row.status_id = 1
      row.status_list = []
    }
  }

  /**
   * 判断表格类型并初始化
   */
  const judListTypeAndInit = () => {
    state.loading = true

    if (state.queryForm.filter.assigned_to_id == '') {
      delete state.queryForm.filter.issueAssigned['user_id']
      delete state.queryForm.op.issueAssigned['user_id']
    }

    switch (props.listType) {
      case 'child': {
        getNewChildList()
        break
      }
      case 'bugRelation': {
        queryIssueRelation(state.tabNameToTrackerIdMap.bugRelation)
        break
      }
      case 'requirementRelation': {
        queryIssueRelation(state.tabNameToTrackerIdMap.requirementRelation)
        break
      }
      case 'taskRelation': {
        queryIssueRelation(state.tabNameToTrackerIdMap.taskRelation)
        break
      }
      case 'progressRelation': {
        queryIssueRelation(state.tabNameToTrackerIdMap.progressRelation)
        break
      }
    }
  }

  const fetchDataDebounced = _.debounce(() => {
    judListTypeAndInit()
  }, 850)

  const firstInitData = async () => {
    // 先初始化事项类型映射
    await initTrackerMapping()

    if (processedData.value.length == 0 || state.loading == true) {
      state.loading = true
      judListTypeAndInit()
    }

    // 🔧 初始化时加载当前tab的状态列表
    await handleIssueStatusList()

    // 🔧 完全移除loadNewRowStatusList调用，只在点击快速创建时才加载
    // 避免tab切换时覆盖已有的状态列表

    setFilteredColumn()
  }

  const initTableData = () => {
    // console.log('initTableData1237')
    processedData.value.length = 0
  }

  /**
   * 获取个人所加入的项目
   */
  const getJoinedProject = async () => {
    const { data } = await getJoinedProjectList({
      user_id: userStore.redmine.third_user_id,
    })
    state.joinedProjectList = data.data
  }

  /**
   * 快速创建-修改了项目id
   */
  const handleProjectIdChanged = (row) => {
    if (row.project_id !== props.info.project_id) {
      row.fixed_version_id = null
    } else {
      row.fixed_version_id = props.fixedVersionId
    }
  }

  /**
   * 列设置-列展示与否
   */
  const showColumnForm = async () => {
    state.columnVisiblePopover = !state.columnVisiblePopover
    await new Promise((resolve) => setTimeout(resolve, 100))
    if (state.columnVisiblePopover) {
      changeAllChecked()
    } else {
      filteredColumns.value = _.cloneDeep(dynamicTableColumns.value)
    }
  }

  /**
   * 列设置-关闭列选择框
   */
  const closeColumnForm = () => {
    state.columnVisiblePopover = false
  }

  /**
   * 列设置-全选与非全选
   */
  const changeAllChecked = () => {
    let checked = filteredColumns.value.filter((item) => {
      return item.disabledDrag || item.show
    })
    state.columnAllChecked = checked.length === filteredColumns.value.length

    state.columnIndeterminate =
      checked.length > 0 && checked.length < filteredColumns.value.length
  }

  /**
   * 列设置-全选栏勾选与否触发
   * @param {*} val
   */
  const handleAllChecked = (val) => {
    filteredColumns.value.forEach((item, index) => {
      if (!item.disabledDrag) {
        item.show = val
      }
    })
    changeAllChecked()
  }

  /**
   * 列设置-恢复默认列
   */
  const handleResetColumn = () => {
    filteredColumns.value = _.cloneDeep(tableColumns)
    changeAllChecked()
  }

  /**
   * 列设置-保存列设置
   */
  const handleSaveColumn = async () => {
    state.loading = true
    state.columnVisiblePopover = false
    localStorage.setItem(
      'IssueChildListTableColumnSet' + user_id,
      JSON.stringify(filteredColumns.value)
    )
    await new Promise((resovle) => setTimeout(resovle, 200))
    dynamicTableColumns.value = _.cloneDeep(filteredColumns.value)
    state.loading = false
  }

  /**
   * 列设置-读取已经保存的列
   * @param {*} isInit
   */
  const setFilteredColumn = async (isInit = false) => {
    let savedSettings = localStorage.getItem(
      'IssueChildListTableColumnSet' + user_id
    )
    if (savedSettings && !isInit) {
      savedSettings = JSON.parse(savedSettings)

      savedSettings = syncArrays(dynamicTableColumns.value, savedSettings)

      state.loading = true
      await new Promise((resovle) => setTimeout(resovle, 150))
      filteredColumns.value = _.cloneDeep(savedSettings)
      dynamicTableColumns.value = _.cloneDeep(savedSettings)
      state.loading = false

      return
    }
  }

  /**
   * 列设置-checkbox勾选存在变化
   */
  const changeColumnchecked = () => {
    changeAllChecked()
  }

  /**
   * 列设置-同步到dynamicTableColumns
   * @param {*} Arr1
   * @param {*} Arr2
   * @param {*} prop
   */
  const syncArrays = (Arr1, Arr2, prop = 'prop') => {
    //fixed放前面
    // const fixedCol = _.cloneDeep(Arr1.filter((item) => item.disabledDrag))
    // Arr2 = _.cloneDeep(Arr2.filter((item) => item.disabledDrag === undefined))
    // fixedCol.reverse().forEach((item) => {
    //   Arr2.unshift(item)
    // })
    // 创建 Arr2 中所有 id 的集合
    const arr2Props = new Set(Arr2.map((item) => item[prop]))

    // 找出需要添加到 arr2 的项
    const itemsToAdd = Arr1.filter((item) => !arr2Props.has(item[prop]))

    // 创建 Arr1 中所有 id 的集合
    const arr1Props = new Set(Arr1.map((item) => item[prop]))

    // 找出需要从 arr2 删除的项
    const itemsToRemove = Arr2.filter((item) => !arr1Props.has(item[prop]))

    // 将 arr1 中的新增项添加到 arr2
    itemsToAdd.forEach((newItem) => {
      const newItemProp = newItem[prop]
      const newItemIndexInArr1 = Arr1.findIndex(
        (item) => item[prop] === newItemProp
      )

      if (newItemIndexInArr1 > 0) {
        // 获取新项在 Arr1 中的前一个项的 prop
        const previousItemProp = Arr1[newItemIndexInArr1 - 1][prop]
        // 在 Arr2 中查找前一个项的 prop 的位置
        const previousItemIndexInArr2 = Arr2.findIndex(
          (item) => item[prop] === previousItemProp
        )

        if (previousItemIndexInArr2 !== -1) {
          // 如果找到了前一个项的位置，就在该位置后面插入新项
          Arr2.splice(previousItemIndexInArr2 + 1, 0, newItem)
        } else {
          // 如果在 Arr2 中没有找到前一个项的位置，就将新项添加到末尾
          Arr2.push(newItem)
        }
      } else {
        // 如果新项是 Arr1 中的第一个项，直接添加到 Arr2 的开头
        Arr2.unshift(newItem)
      }
    })

    // 从 arr2 删除不在 arr1 中的项
    itemsToRemove.forEach((item) => {
      const index = Arr2.findIndex((arrItem) => arrItem[prop] === item[prop])
      if (index !== -1) {
        Arr2.splice(index, 1)
      }
    })
    // 如果 Arr1 和 Arr2 中有相同项，更新 Arr2 中的属性
    const syncField = [
      'label',
      'width',
      'sortable',
      'align',
      'fixed',
      'type',
      'minWidth',
      'showOverflowTooltip',
    ]
    Arr1.forEach((item1) => {
      const item2 = Arr2.find((item) => item[prop] === item1[prop])
      if (item2) {
        syncField.forEach((key) => {
          if (item1[key] !== undefined) {
            item2[key] = item1[key]
          } else {
            delete item2[key]
          }
        })
      }
    })

    return Arr2 // 返回同步后的 arr2
  }

  // 新增计算属性，根据行的 project_id 返回对应的版本列表
  const getVersionListForRow = (projectId) => {
    return state.projectVersionMap[projectId] || []
  }

  /**
   * 获取同步方向文本
   */
  const getSyncDirectionText = (row) => {
    if (!row.sync_direction) return '未同步'

    // 根据当前事项是源还是目标，以及同步方向，返回对应的文本
    if (row.is_source) {
      // 当前是源事项
      switch (row.sync_direction) {
        case 'source_to_target':
          return '→'
        case 'target_to_source':
          return '←'
        case 'bidirectional':
          return '⇆'
        default:
          return '未同步'
      }
    } else {
      // 当前是target事项
      switch (row.sync_direction) {
        case 'source_to_target':
          return '←'
        case 'target_to_source':
          return '→'
        case 'bidirectional':
          return '⇆'
        default:
          return '未同步'
      }
    }
  }

  /**
   * 获取同步方向的Tooltip文本
   */
  const getSyncDirectionTooltip = (row) => {
    if (!row.sync_direction) return '未同步'

    if (row.is_source) {
      // 当前是源事项
      switch (row.sync_direction) {
        case 'source_to_target':
          return '同步方向：当前事项→关联事项'
        case 'target_to_source':
          return '同步方向：关联事项→当前事项'
        case 'bidirectional':
          return '同步方向：当前事项⇆关联事项'
        default:
          return '未同步'
      }
    } else {
      // 当前是target事项
      switch (row.sync_direction) {
        case 'source_to_target':
          return '同步方向：关联事项→当前事项'
        case 'target_to_source':
          return '同步方向：当前事项→关联事项'
        case 'bidirectional':
          return '同步方向：当前事项⇆关联事项'
        default:
          return '未同步'
      }
    }
  }

  /**
   * 获取同步标签类型
   */
  const getSyncTagType = (row) => {
    if (row.is_source) {
      // 当前是源事项
      switch (row.sync_direction) {
        case 'source_to_target':
          return 'primary'
        case 'target_to_source':
          return 'warning'
        case 'bidirectional':
          return 'success'
        default:
          return 'info'
      }
    } else {
      // 当前是target事项
      switch (row.sync_direction) {
        case 'source_to_target':
          return 'primary'
        case 'target_to_source':
          return 'warning'
        case 'bidirectional':
          return 'success'
        default:
          return 'info'
      }
    }
  }

  /**
   * 处理popover显示
   * @param {number} rowId - 行ID
   */
  const handlePopoverShow = (rowId) => {
    if (!state.visiblePopoverIds.includes(rowId)) {
      state.visiblePopoverIds.push(rowId)
    }
  }

  /**
   * 处理popover隐藏
   * @param {number} rowId - 行ID
   */
  const handlePopoverHide = (rowId) => {
    const index = state.visiblePopoverIds.indexOf(rowId)
    if (index > -1) {
      state.visiblePopoverIds.splice(index, 1)
    }
  }

  /**
   * 处理复制标题事件
   * @param {object} row - 行数据
   */
  const handleCopyTitle = (row) => {
    // 可以在这里添加额外的逻辑，比如统计等
    console.log('复制标题:', row.subject)
  }

  /**
   * 处理复制链接事件
   * @param {object} row - 行数据
   */
  const handleCopyLink = (row) => {
    // 可以在这里添加额外的逻辑，比如统计等
    console.log('复制链接:', row.id)
  }

  /**
   * 处理复制事项事件
   * @param {object} row - 行数据
   */
  const handleCopyIssue = (row) => {
    // 可以在这里添加额外的逻辑，比如统计等
    console.log('复制事项:', row.id)
  }

  /**
   * 处理删除事项事件
   * @param {object} row - 行数据
   */
  const handleDeleteIssue = (row) => {
    // 调用原有的删除方法
    deleteChild(row)
  }

  /**
   * 打开同步设置弹窗
   */
  const openSyncSettings = async (row) => {
    // 检查是否已有同步关系
    try {
      const { data } = await getSyncRelationList({
        source_issue_id: props.info.id, // 父事项ID
        target_issue_id: row.id, // 子事项ID
        page: 1,
        limit: 1,
      })

      if (data.list && data.list.length > 0) {
        // 编辑现有同步关系
        issueSyncDialogRef.value?.open(row, data.list[0])
      } else {
        // 创建新同步关系
        issueSyncDialogRef.value?.open(
          row,
          { is_source: data.current_issue_is_source },
          false
        )
      }
    } catch (error) {
      console.error('获取同步关系失败:', error)
      issueSyncDialogRef.value?.open(row)
    }
  }

  /**
   * 处理同步关系创建成功
   */
  const handleSyncCreated = (data) => {
    $baseMessage('同步关系创建成功', 'success', 'vab-hey-message-success')
    // 刷新列表以更新同步状态
    judListTypeAndInit()
  }

  /**
   * 处理同步关系更新成功
   */
  const handleSyncUpdated = (data) => {
    $baseMessage('同步关系更新成功', 'success', 'vab-hey-message-success')
    // 刷新列表以更新同步状态
    judListTypeAndInit()
  }

  /**
   * 获取同步状态
   */
  const loadSyncStatus = async (issues) => {
    if (!issues || issues.length === 0) return

    try {
      // 批量获取所有子事项的同步关系
      const targetIssueIds = issues.map((issue) => issue.id)
      const { data } = await getSyncRelationList({
        source_issue_id: props.info.id, // 父事项ID
        target_issue_ids: targetIssueIds, // 所有子事项ID数组
      })

      // 处理返回的同步关系数据
      issues.forEach((issue) => {
        const syncInfo = data[issue.id]
        if (syncInfo) {
          issue.sync_status = 'active'
          issue.sync_direction = syncInfo.sync_direction
          issue.sync_relation_id = syncInfo.relation_id
          issue.is_source = syncInfo.is_source
        } else {
          issue.sync_status = 'inactive'
          issue.sync_direction = null
        }
      })
    } catch (error) {
      console.error('批量获取同步状态失败:', error)
      // 如果批量获取失败，设置所有为未同步
      issues.forEach((issue) => {
        issue.sync_status = 'inactive'
        issue.sync_direction = null
      })
    }
  }

  const cellStyleMethod = ({ column, row }) => {
    if (column.property === 'subject') {
      let paddingLeft = 16 * (row.level ?? 0) + 'px'

      return {
        paddingLeft: paddingLeft,
      }
    }
    return ''
  }

  onMounted(async () => {
    handleEnumerationList()
    // judListTypeAndInit()

    updateVersionList()
    getJoinedProject()

    // 预加载当前项目的版本列表
    if (props.info.project_id) {
      try {
        const { data } = await getVersionList({
          filter: {
            project_id: props.info.project_id,
          },
          limit: 100,
          status: 'open',
        })
        state.projectVersionMap[props.info.project_id] = data.data ?? []
        versionList.value = state.projectVersionMap[props.info.project_id]
      } catch (error) {
        console.error('获取项目版本列表失败:', error)
      }
    }
  })

  // 检查tracker是否有identifier
  const hasTrackerIdentifier = (tracker) => {
    return tracker && tracker.identifier && tracker.identifier.trim() !== ''
  }

  // 根据tracker获取对应的issueType
  const getIssueTypeFromTracker = (tracker) => {
    if (!tracker) return null
    return getTrackerIcon(tracker, 'child')
  }

  // 监听listType变化，重新获取对应类型的状态
  watch(
    () => props.listType,
    async (newType, oldType) => {
      // 只在真正切换时才加载，不在初始化时加载
      if (oldType && newType !== oldType) {
        // 清空之前的选择
        state.selectedStatusIds = []
        state.selectedStatusCategories = []
        state.queryForm.filter.status_id = null
        delete state.queryForm.op.status_id

        // 重新加载状态列表
        await handleIssueStatusList()
      }
    }
  )

  defineExpose({
    doAdd,
    firstInitData,
    initTableData,
    closeColumnForm,
  })
</script>

<style lang="scss">
  .issue-talbe-list-cloumn-select-class:hover {
    .issue-talbe-list-author-cloumn-select-class {
      .el-select__wrapper {
        background-color: #f5f7fa;
      }
    }
  }
</style>

<style lang="scss" scoped>
  .issue-table-list-subject-colomn:hover {
    .text-operation-button-of-edit {
      display: inherit;
      opacity: 1;
      transition: opacity 0.3s;
    }
  }
  .text-operation-button-of-edit {
    display: none;
    color: #999;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
  }

  .text-operation-button-of-edit:hover {
    color: #6192f5;
    cursor: pointer;
  }

  .text-operation-button {
    color: #999;
    cursor: pointer;
  }

  .text-operation-button:hover {
    color: #6192f5;
    cursor: pointer;
  }

  :deep() {
    .el-table__body-wrapper tr td.el-table-fixed-column--left,
    .el-table__body-wrapper tr td.el-table-fixed-column--right,
    .el-table__body-wrapper tr th.el-table-fixed-column--left,
    .el-table__body-wrapper tr th.el-table-fixed-column--right,
    .el-table__footer-wrapper tr td.el-table-fixed-column--left,
    .el-table__footer-wrapper tr td.el-table-fixed-column--right,
    .el-table__footer-wrapper tr th.el-table-fixed-column--left,
    .el-table__footer-wrapper tr th.el-table-fixed-column--right,
    .el-table__header-wrapper tr td.el-table-fixed-column--left,
    .el-table__header-wrapper tr td.el-table-fixed-column--right,
    .el-table__header-wrapper tr th.el-table-fixed-column--left,
    .el-table__header-wrapper tr th.el-table-fixed-column--right {
      position: sticky !important;
    }

    .el-table [class*='el-table__row--level'] .el-table__expand-icon {
      position: relative !important;
      margin-top: 0px !important;
      margin-right: 4px !important;
      margin-left: 0px !important;
    }
    .el-table__indent {
      // padding-left: 32px !important;
      position: inherit !important;
    }

    .common-link {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-table__body tr:first-child td:nth-child(2) .cell {
      overflow: hidden !important;
    }

    .el-table {
      --el-table-border-color: #dcdfe6;
      --el-table-border: 1px solid #dcdfe6;
    }

    .el-table th .cell,
    .el-table th .cell {
      color: #999999 !important;
    }

    .el-table td.el-table__cell div.el-tooltip {
      box-sizing: border-box;
      display: flex;
      align-items: center;
    }

    .el-table__header {
      thead {
        tr {
          th {
            background: #ffffff !important;
          }
        }
      }
    }
    .child-issue:not(:last-child) {
      max-height: 30px;
      margin-bottom: 5px;
    }
    .new-child-input {
      .el-input__wrapper {
        box-shadow: none;
      }
    }
  }

  // 拖动排序相关
  .move-box-icon {
    display: none;
    align-items: center;
    justify-content: flex-end;
    height: 32px;
  }
  .move-box:hover {
    .move-box-icon {
      display: flex;
    }
  }
  .column-set-bottom {
    justify-content: space-between;
    height: 40px;
    background: #fff;
    border-top: 1px solid #e8e9eb;
    // border: 1px solid #e8e9eb;
    border-radius: 0px 0px 6px 6px;
  }

  .column-item {
    padding-left: 8px;
    // margin-right: 16px;
    margin-bottom: 0px !important;
    cursor: move;
    .el-checkbox {
      font-weight: 400;
      color: #333;
    }
  }
  .column-item:hover {
    cursor: move;
    background-color: rgba(15, 84, 255, 0.05);
  }

  // /////////
  .new-child-row {
    padding: 7px 10px;
    margin-top: 8px;
    margin-bottom: 5px;
    border: 1px solid #3977f3;
    border-radius: 6px 6px 6px 6px;
  }
  .child-issue {
    align-items: center;
    padding: 7px 10px;
    // background: #f7f8fa;
    border-radius: 6px 6px 6px 6px;
  }
  .child-col-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    color: #7e899d;
  }
  .custom-link {
    overflow: hidden; /* 隐藏溢出的文本 */
    text-overflow: ellipsis; /* 显示省略号 */
    white-space: nowrap; /* 防止文本换行 */
    cursor: pointer;
    &:hover {
      color: $base-color-primary;
    }
  }

  // 事项类型选择器样式
  .tracker-select-dropdown {
    display: block;
    width: 100%;
    height: 32px;
    padding: 3px 5px;
    cursor: pointer;
    border-radius: 2.5px;

    .tracker-dropdown-link {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;

      &:hover {
        .select-icon {
          opacity: 1;
        }
      }

      .select-icon {
        opacity: 0;
        line-height: 25px;
        color: #9c9a9a;
        transition: opacity 0.2s ease;
      }
    }

    &:hover {
      background: rgba(133, 146, 166, 0.1);
    }
  }

  // 下拉选项样式
  :deep(.tracker-select-popper) {
    .el-dropdown-menu {
      .el-dropdown-menu__item {
        padding: 12px 16px !important;
        min-width: 220px;
        line-height: 1.5;

        &.tracker-option {
          .flex {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;

            span {
              flex: 1;
              margin-left: 8px;
              font-size: 14px;
            }
          }

          &.is-selected {
            background-color: #f0f9ff !important;
            color: #409eff;
          }

          &:hover {
            background-color: #f5f7fa !important;
          }
        }

        &.reset-option {
          border-bottom: 1px solid #eee;
          margin-bottom: 4px;
        }
      }
    }
  }

  // 全局下拉菜单样式
  :deep(.el-dropdown-menu) {
    .el-dropdown-menu__item {
      &.tracker-option {
        padding: 12px 16px !important;

        .flex {
          display: flex !important;
          align-items: center !important;
          gap: 12px !important;

          span {
            margin-left: 8px !important;
          }
        }
      }
    }
  }
</style>
