<template>
  <div>
    <firefly-dialog
      v-model="state.dialogFormVisible"
      :title="state.title"
      top="50px"
      width="400"
      show-default-button
      :confirm-btn-disabled="isConfirmDisabled"
      :modal="false"
      @confirm="handleSave"
    >
      <el-form style="margin: 30px 0px">
        <el-form-item label="当前所属项目为：" label-width="124px">
          <span>{{ state.nowProject.name }}</span>
        </el-form-item>
        <el-form-item label="将转到的项目为：" label-width="124px">
          <div>
            <el-select
              v-model="state.issueProjectId"
              @change="handleParentIssue"
              style="width: 224px"
              filterable
              placeholder="请选择要转到的项目"
            >
              <el-option
                v-for="(pItem, pKey) in state.parentIssueList"
                :key="pKey"
                :value="pItem.id"
                :label="pItem.name"
              >
                {{ pItem.name }}
              </el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item
          v-if="state.issueProjectId"
          label="选择版本："
          label-width="124px"
        >
          <div>
            <el-select
              v-model="state.targetVersionId"
              @change="handleVersionChange"
              style="width: 224px"
              filterable
              clearable
              placeholder="请选择版本（可选）"
            >
              <el-option
                v-for="(vItem, vKey) in state.versionList"
                :key="vKey"
                :value="vItem.id"
                :label="
                  vItem.status === 'closed'
                    ? vItem.name + '（已关闭）'
                    : vItem.status === 'locked'
                    ? vItem.name + '（已锁定）'
                    : vItem.name
                "
                :disabled="
                  vItem.status === 'closed' || vItem.status === 'locked'
                "
              >
                {{
                  vItem.status === 'closed'
                    ? vItem.name + '（已关闭）'
                    : vItem.status === 'locked'
                    ? vItem.name + '（已锁定）'
                    : vItem.name
                }}
              </el-option>
            </el-select>
          </div>
        </el-form-item>

        <!-- 同项目警告提示 -->
        <el-alert
          v-if="isSameProject"
          title="不能迁移到相同项目"
          type="warning"
          :closable="false"
          show-icon
          style="margin-bottom: 15px"
        />

        <!-- 事项类型选择器 -->
        <el-form-item
          v-if="state.targetTrackers.length > 0"
          label="目标事项类型："
          label-width="124px"
        >
          <div>
            <el-select
              v-model="state.selectedTrackerId"
              placeholder="请选择事项类型"
              style="width: 224px"
              @change="handleTrackerChange"
            >
              <el-option
                v-for="tracker in state.targetTrackers"
                :key="tracker.id"
                :label="tracker.name"
                :value="tracker.id"
              />
            </el-select>
          </div>
        </el-form-item>

        <!-- 状态选择器 -->
        <el-form-item
          v-if="state.targetStatuses.length > 0"
          label="目标状态："
          label-width="124px"
        >
          <div>
            <el-select
              v-model="state.selectedStatusId"
              placeholder="请选择状态"
              style="width: 224px"
            >
              <el-option
                v-for="status in state.targetStatuses"
                :key="status.id"
                :label="status.name"
                :value="status.id"
              />
            </el-select>
          </div>
        </el-form-item>
      </el-form>
    </firefly-dialog>
  </div>
</template>

<script setup>
  import { changeIssueProject } from '@/api/projectIssue'
  import { getList } from '@/api/projectIndex'
  import { getList as getVersionList } from '@/api/projectVersion'
  import { getInitialStatuses } from '@/api/customWorkflow'
  import { getProjectTrackers } from '@/api/projectTracker'
  import { computed } from 'vue'
  import { ElMessageBox } from 'element-plus'
  const $baseMessage = inject('$baseMessage')
  const emit = defineEmits(['handle-change-issue-project'])
  const $pub = inject('$pub')

  const props = defineProps({
    projectId: {
      type: [Number, String],
      default: null,
    },
    issueId: {
      type: Number,
      default: null,
    },
    autoRefresh: {
      type: Boolean,
      default: true, // 默认启用自动刷新提示
    },
  })
  const state = reactive({
    dialogFormVisible: false,
    title: '迁移所属项目',
    parentIssueList: [],
    issueProjectId: null,
    list: [],
    nowProject: { name: '加载中...' },
    versionList: [],
    targetVersionId: null,
    targetVersionName: '',

    // 用户选择的目标事项类型和状态
    selectedTrackerId: null,
    selectedStatusId: null,

    // 目标项目的事项类型和状态列表
    targetTrackers: [],
    targetStatuses: [],
  })

  // 计算属性：判断是否为同项目迁移（不允许）
  const isSameProject = computed(() => {
    return (
      state.issueProjectId &&
      props.projectId &&
      state.issueProjectId == props.projectId
    )
  })

  // 计算属性：确定按钮是否禁用
  const isConfirmDisabled = computed(() => {
    // 没有选择项目或选择了相同项目或没有选择状态时禁用
    return (
      !state.issueProjectId || isSameProject.value || !state.selectedStatusId
    )
  })

  /**
   * 获取项目列表
   */
  const loadProjectList = async () => {
    nextTick(async () => {
      const { data } = await getList({
        pageNo: 1,
        pageSize: 10,
        sort: 'created_on',
        filter: {
          name: '',
          type: 'getAllProject',
        },
        op: {
          name: 'LIKE',
        },
      })
      state.parentIssueList = data
      state.parentIssueList = data.filter(
        (item) =>
          !item.projects_ext ||
          item.projects_ext.project_type !== 'product_type'
      )
      state.nowProject = state.parentIssueList.find((item) => {
        return item.id == props.projectId
      })
    })
  }

  const handleParentIssue = async () => {
    // 清空版本选择
    state.targetVersionId = null
    state.targetVersionName = ''
    state.versionList = []

    // 清空事项类型和状态选择
    state.selectedTrackerId = null
    state.selectedStatusId = null
    state.targetTrackers = []
    state.targetStatuses = []

    // 加载选中项目的版本列表和事项类型列表
    if (state.issueProjectId) {
      await Promise.all([
        loadVersionList(state.issueProjectId),
        loadTargetTrackers(state.issueProjectId),
      ])
    }
  }

  const handleVersionChange = () => {
    const selectedVersion = state.versionList.find(
      (item) => item.id === state.targetVersionId
    )
    state.targetVersionName = selectedVersion ? selectedVersion.name : ''
  }

  /**
   * 处理事项类型选择变化
   */
  const handleTrackerChange = async () => {
    // 清空状态选择
    state.selectedStatusId = null
    state.targetStatuses = []

    if (state.selectedTrackerId && state.issueProjectId) {
      await loadTargetStatuses(state.issueProjectId, state.selectedTrackerId)
    }
  }

  /**
   * 获取指定事项类型的状态列表
   */
  const loadTargetStatuses = async (projectId, trackerId) => {
    try {
      const response = await getInitialStatuses({
        project_id: projectId,
        tracker_id: trackerId,
      })
      state.targetStatuses = response.data || []
    } catch (error) {
      state.targetStatuses = []
      $baseMessage('加载状态列表失败', 'error')
    }
  }

  /**
   * 获取项目版本列表
   */
  const loadVersionList = async (projectId) => {
    try {
      const { data } = await getVersionList({
        pageNo: 1,
        pageSize: 100,
        sort: 'status',
        order: 'desc',
        filter: {
          project_id: projectId,
        },
      })
      state.versionList = data.data || []
    } catch (error) {
      console.error('加载版本列表失败:', error)
      state.versionList = []
    }
  }

  /**
   * 获取目标项目的事项类型列表
   */
  const loadTargetTrackers = async (projectId) => {
    try {
      const response = await getProjectTrackers(projectId)
      state.targetTrackers = response.data || []
    } catch (error) {
      state.targetTrackers = []
      $baseMessage('加载事项类型失败', 'error')
    }
  }

  const handleSave = async () => {
    // 检查是否为同项目迁移
    if (isSameProject.value) {
      $baseMessage('不能迁移到相同项目', 'warning')
      return
    }

    if (!state.issueProjectId) {
      $baseMessage('请选择目标项目', 'warning')
      return
    }

    let data = {
      id: props.issueId,
      projectId: state.issueProjectId,
    }

    // 如果选择了版本，添加版本参数
    if (state.targetVersionId) {
      data.versionId = state.targetVersionId
    }

    // 如果选择了事项类型，添加事项类型参数
    if (state.selectedTrackerId) {
      data.trackerId = state.selectedTrackerId
    }

    // 如果选择了状态，添加状态参数
    if (state.selectedStatusId) {
      data.statusId = state.selectedStatusId
    }

    console.log('迁移数据:', data)

    try {
      await changeIssueProject(data)
      $baseMessage('修改所属项目成功', 'success', 'vab-hey-message-success')

      const resultData = {
        id: props.issueId,
        projectId: state.issueProjectId,
        projectName: state.parentIssueList.find((item) => {
          return item.id == state.issueProjectId
        }).name,
        versionId: state.targetVersionId,
        versionName: state.targetVersionName,
        trackerId: state.selectedTrackerId,
        statusId: state.selectedStatusId,
      }
      emit('handle-change-issue-project')
      $pub('handle-change-issue-project', resultData)
      close()

      // 迁移成功后刷新页面，确保筛选条件等数据更新
      if (props.autoRefresh) {
        setTimeout(async () => {
          try {
            await ElMessageBox.confirm(
              '迁移成功！是否刷新页面以更新筛选条件？',
              '提示',
              {
                confirmButtonText: '刷新页面',
                cancelButtonText: '稍后手动刷新',
                type: 'success',
              }
            )
            window.location.reload()
          } catch {
            // 用户点击取消，不做任何操作
          }
        }, 1000) // 延迟1秒，让用户看到成功提示
      }
    } catch (error) {
      console.error('迁移失败:', error)
      $baseMessage('迁移失败: ' + (error.message || error), 'error')
    }
  }
  const close = () => {
    state.dialogFormVisible = false
    state.versionList = []
    state.targetVersionId = null
    state.targetVersionName = ''
  }

  const showComponent = () => {
    // 重置所有选择状态
    state.issueProjectId = props.projectId
    state.targetVersionId = null
    state.targetVersionName = ''
    state.selectedTrackerId = null
    state.selectedStatusId = null
    state.targetTrackers = []
    state.targetStatuses = []

    state.dialogFormVisible = true
    loadProjectList()
  }

  const hideComponent = () => {
    state.dialogFormVisible = false
    state.parentIssueList = []
  }

  defineExpose({ showComponent, hideComponent })
</script>
<style lang="scss" scoped>
  :deep() {
    .el-form-item .el-form-item__label {
      margin-top: 0px !important;
    }
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .el-select {
    width: 100%;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }

  // 同项目警告样式
  .el-alert {
    margin-bottom: 15px;
  }

  .el-alert--warning {
    background-color: #fdf6ec;
    border-color: #f5dab1;
  }
</style>
