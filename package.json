{"name": "admin-plus", "version": "10.3.0", "private": true, "author": "vue-admin-better", "scripts": {"serve": "vue-cli-service serve --mode local", "build:dev": "vue-cli-service build --mode development --dest dist", "build:pre": "vue-cli-service build --mode pre --dest dist", "build": "vue-cli-service build --mode production --dest dist", "online": "vue-cli-service build --mode online --dest dist-online", "test:unit": "vue-cli-service test:unit --detectOpenHandles", "lint": "vue-cli-service lint", "build:deploy": "start ./deploy.sh", "build:docker": "vue-cli-service build&&docker build --pull --rm -f \"dockerfile\" -t vueadminbeautifulpro:latest \".\"&&docker run --rm -d  -p 80:80/tcp vueadminbeautifulpro:latest", "build:report": "vue-cli-service build --report", "build:test": "vue-cli-service build --mode test", "global:install": "npm install -g nrm,cnpm,npm-check-updates,rimraf --registry=https://registry.npmmirror.com", "globle:update": "ncu -g", "lint:eslint": "eslint {src,mock}/**/*.{vue,js,ts} --fix", "lint:prettier": "prettier {src,mock}/**/*.{html,vue,css,sass,scss,js,ts,md} --write", "lint:stylelint": "stylelint {src,mock}/**/*.{html,vue,css,sass,scss} --fix --cache --cache-location node_modules/.cache/stylelint/", "module:install": "cnpm i", "module:reinstall": "rimraf node_modules&&npm run module:install", "module:update": "ncu -u --reject @types/node --registry https://registry.npmmirror.com&&npm run module:install", "nrm:npm": "nrm use npm", "nrm:taobao": "nrm use taobao", "template": "plop", "push": "rsync -avz --progress dist/* firefly@************:/home/<USER>/www/tchip_bi_frontend"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@arco-design/web-vue": "^2.56.0", "@element-plus/icons-vue": "^2.0.10", "@logicflow/core": "^2.0.10", "@logicflow/extension": "^2.0.14", "@logicflow/layout": "^1.2.0-alpha.16", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.2", "@vueuse/core": "^9.6.0", "@vueuse/head": "^1.0.20", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.9", "@wangeditor/plugin-md": "^1.0.0", "@wangeditor/plugin-mention": "^1.0.0", "@wecom/jssdk": "^1.4.5", "@zxing/browser": "^0.1.5", "aieditor": "http://bi.t-firefly.com:2101/files/2025/04/250407055522_aieditor-1.3.8.tgz", "ant-design-vue": "^4.2.6", "axios": "^1.2.1", "better-scroll": "^2.5.1", "buffer": "^6.0.3", "caniuse-lite": "^1.0.30001374", "cherry-markdown": "^0.9.4", "core-js": "^3.26.1", "count-vue3": "^3.2.40", "cropper-vue3": "^3.2.45", "d3": "^7.9.0", "dayjs": "^1.11.7", "diff2html": "^3.4.47", "echarts": "^5.4.0", "element-plus": "^2.5.0", "file-saver": "^2.0.5", "highlight.js": "^11.8.0", "html2canvas": "^1.4.1", "htmldiff-js": "^1.0.5", "image-conversion": "^2.1.1", "jinrishici": "^1.0.6", "js-cookie": "^3.0.1", "jsencrypt": "^3.3.1", "jspdf": "^3.0.1", "less-loader": "^12.2.0", "lodash": "^4.17.21", "magnifier-vue3": "^3.2.45", "marked": "^4.3.0", "mitt": "^3.0.0", "mockjs": "^1.1.0", "moment": "^2.29.4", "monent": "^0.0.2-security", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pinia": "^2.0.27", "pinyin": "^4.0.0-alpha.0", "player-vue3": "^3.2.41", "qs": "^6.11.0", "quill-vue3": "^3.2.45", "register-service-worker": "^1.7.2", "resize-detector": "^0.3.0", "save": "^2.5.0", "segmentit": "^2.0.3", "simditor": "^2.3.28", "sortablejs": "^1.15.2", "vab-icons": "file:vab-icons", "vditor": "^3.11.0", "vue": "^3.4.34", "vue-demi": "^0.14.6", "vue-i18n": "^9.2.2", "vue-json-viewer": "^3.0.2", "vue-qr": "^4.0.9", "vue-qrcode-reader": "^5.7.2", "vue-router": "^4.1.6", "vue3-tree-org": "^4.1.1", "vuedraggable": "^4.0.1", "vxe-table": "^4.13.31", "xlsx": "0.18.5", "zxing-wasm": "^2.1.0"}, "devDependencies": {"@babel/core": "^7.20.5", "@stylelint/postcss-css-in-js": "^0.38.0", "@types/file-saver": "^2.0.5", "@types/js-cookie": "^3.0.2", "@types/lodash-es": "^4.17.6", "@types/marked": "^4.0.3", "@types/node": "^18.8.5", "@types/nprogress": "^0.2.0", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-typescript": "^11.0.2", "@vue/test-utils": "^2.2.6", "body-parser": "^1.20.1", "chalk": "^4.1.2", "chokidar": "^3.5.3", "compression-webpack-plugin": "^10.0.0", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.8.0", "filemanager-webpack-plugin": "^8.0.0", "image-webpack-loader": "^8.1.0", "plop": "^3.1.1", "postcss": "^8.4.19", "postcss-html": "^1.5.0", "postcss-markdown": "^1.2.0", "postcss-scss": "^4.0.6", "postcss-syntax": "^0.36.2", "prettier": "^2.8.0", "raw-loader": "^4.0.2", "sass": "^1.56.1", "sass-loader": "^13.2.0", "stylelint": "^14.16.0", "stylelint-config-prettier": "^9.0.4", "stylelint-config-recess-order": "^3.0.0", "svg-sprite-loader": "^6.0.11", "typescript": "^4.9.3", "unplugin-auto-import": "^0.12.0", "unplugin-element-plus": "^0.4.1", "unplugin-vue-components": "^0.22.11", "unplugin-vue-define-options": "^1.0.0", "vab-templates-vue3": "^0.0.8", "vue-eslint-parser": "^9.1.0", "vue-global-api": "^0.4.1", "vue-unplugin": "^1.0.6", "vue-unplugins": "^1.0.6", "webpack": "^5.75.0", "webpackbar": "^5.0.2"}, "homepage": "https://chu1204505056.gitee.io/admin-plus", "license": "Mozilla Public License Version 2.0", "participants": ["FlowPeakFish"], "repository": {"type": "git", "url": "git+https://github.com/vue-admin-beautiful/admin-plus.git"}}